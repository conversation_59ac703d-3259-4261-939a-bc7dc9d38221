#!/usr/bin/env python3
"""
配置检查脚本

快速检查项目配置是否完整
"""

import os
import sys
from pathlib import Path

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv 未安装，无法加载 .env 文件")

def check_env_file():
    """检查.env文件"""
    print("📄 .env文件检查...")
    
    env_file = Path(".env")
    if env_file.exists():
        print("   ✅ .env 文件存在")
        return True
    else:
        print("   ❌ .env 文件不存在")
        print("   💡 请复制 .env.example 为 .env 并填入实际配置")
        return False

def check_deepseek_config():
    """检查DeepSeek配置"""
    print("\n🤖 DeepSeek API配置检查...")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    
    if api_key:
        masked_key = api_key[:8] + "..." if len(api_key) > 8 else api_key
        print(f"   ✅ DEEPSEEK_API_KEY: {masked_key}")
        return True
    else:
        print("   ❌ DEEPSEEK_API_KEY: 未配置")
        print("   💡 请在 .env 文件中设置 DEEPSEEK_API_KEY")
        return False

def check_aliyun_config():
    """检查阿里云TTS配置"""
    print("\n☁️  阿里云TTS配置检查...")
    
    configs = {
        "ALIYUN_AK_ID": "AccessKey ID",
        "ALIYUN_AK_SECRET": "AccessKey Secret", 
        "ALIYUN_TTS_APPKEY": "TTS AppKey"
    }
    
    all_configured = True
    
    for env_var, description in configs.items():
        value = os.getenv(env_var)
        if value:
            masked_value = value[:8] + "..." if len(value) > 8 else value
            print(f"   ✅ {env_var}: {masked_value}")
        else:
            print(f"   ❌ {env_var}: 未配置")
            print(f"      💡 {description}")
            all_configured = False
    
    if not all_configured:
        print("\n   📖 获取阿里云TTS配置的步骤:")
        print("   1. AccessKey: https://ram.console.aliyun.com/manage/ak")
        print("   2. AppKey: https://nls-portal.console.aliyun.com/ -> 项目管理")
        print("   3. 详细说明: docs/aliyun_tts_setup.md")
    
    return all_configured

def check_optional_config():
    """检查可选配置"""
    print("\n⚙️  可选配置检查...")
    
    optional_configs = {
        "ALIYUN_TTS_VOICE": "longxiaochun_v2",
        "ALIYUN_TTS_FORMAT": "wav",
        "ALIYUN_TTS_SAMPLE_RATE": "24000",
        "LOG_LEVEL": "INFO",
        "THEME": "light"
    }
    
    for env_var, default_value in optional_configs.items():
        value = os.getenv(env_var, default_value)
        print(f"   ℹ️  {env_var}: {value}")

def main():
    """主函数"""
    print("🔍 AI Video Editor 配置检查")
    print("=" * 40)
    
    checks = [
        ("环境文件", check_env_file),
        ("DeepSeek配置", check_deepseek_config),
        ("阿里云TTS配置", check_aliyun_config),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        result = check_func()
        results.append((check_name, result))
    
    # 显示可选配置
    check_optional_config()
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 配置检查结果:")
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 完整" if result else "❌ 缺失"
        print(f"   {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项配置完整")
    
    if passed == total:
        print("\n🎉 所有必要配置已完成！")
        print("\n🚀 可以开始使用:")
        print("   python ai_video_editor/main.py")
        print("   python test_aliyun_tts.py  # 测试TTS服务")
    else:
        print(f"\n⚠️  还有 {total - passed} 项配置需要完成")
        print("\n📝 下一步:")
        print("1. 编辑 .env 文件")
        print("2. 填入缺失的API密钥")
        print("3. 重新运行此脚本检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
