[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-video-editor"
version = "1.0.0"
description = "AI智能视频剪辑软件"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "AI Video Editor Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
    "Topic :: Multimedia :: Video",
    "Topic :: Multimedia :: Video :: Conversion",
]
requires-python = ">=3.13"
dependencies = [
    "PyQt6>=6.6.0",
    "openai>=1.0.0",
    "requests>=2.31.0",
    "pydub>=0.25.1",
    "ffmpeg-python>=0.2.0",
    "aiohttp>=3.9.0",
    "aiofiles>=23.2.1",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
    "loguru>=0.7.2",
    "click>=8.1.7",
    "tqdm>=4.66.0",
    "psutil>=5.9.6",
    "aliyun-python-sdk-core>=2.15.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-qt>=4.2.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-qt>=4.2.0",
    "pytest-cov>=4.1.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]
docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.scripts]
ai-video-editor = "ai_video_editor.main:main"

[project.gui-scripts]
ai-video-editor-gui = "ai_video_editor.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["ai_video_editor*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
ai_video_editor = [
    "ui/resources/**/*",
    "config/**/*",
]

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ai_video_editor"]
known_third_party = ["PyQt6", "openai", "pydub", "ffmpeg"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "ffmpeg.*",
    "pydub.*",
    "nls.*",
    "aliyunsdkcore.*",
]
ignore_missing_imports = true

# Pytest配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gui: marks tests as GUI tests",
]
# asyncio_mode = "auto"  # 需要pytest-asyncio插件

# Coverage配置
[tool.coverage.run]
source = ["ai_video_editor"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
