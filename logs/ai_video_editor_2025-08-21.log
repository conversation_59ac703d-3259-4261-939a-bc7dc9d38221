2025-08-21 00:13:44 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 00:13:44 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 00:13:44 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 00:13:44 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 00:13:44 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 00:13:53 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:13:55 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 00:13:56 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 00:13:59 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 00:13:59 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 00:13:59 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 00:13:59 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 00:14:15 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 6
2025-08-21 00:14:15 - ai_video_editor - INFO - 转换映射关系: 6 个对象 -> 6 个映射
2025-08-21 00:14:15 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5, 6, 7], 'rewritten_5': [8, 9]}
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 想象一下：1代表一个苹果，2是两个眼睛，3是三角形有三个角...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 想象一下：1代表一个苹果，2是两个眼睛，3是三角形有三个角...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 4是桌子的四条腿，5是我们一只手的手指头数...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 4是桌子的四条腿，5是我们一只手的手指头数...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 6是蜜蜂蜂房的六边形，7是一周的天数，8是八爪鱼的触手...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 6是蜜蜂蜂房的六边形，7是一周的天数，8是八爪鱼的触手...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 9是九宫格的格子数，10代表圆满——就像我们的十根手指...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 9是九宫格的格子数，10代表圆满——就像我们的十根手指...
2025-08-21 00:14:15 - ai_video_editor - INFO - 映射关系显示更新完成: 6 组映射
2025-08-21 00:15:09 - ai_video_editor - INFO - 字幕改写成功: 10 -> 6
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 想象一下：1代表一个苹果，2是两个眼睛，3是三角形有三个角...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 想象一下：1代表一个苹果，2是两个眼睛，3是三角形有三个角...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 4是桌子的四条腿，5是我们一只手的手指头数...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 4是桌子的四条腿，5是我们一只手的手指头数...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 6是蜜蜂蜂房的六边形，7是一周的天数，8是八爪鱼的触手...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 6是蜜蜂蜂房的六边形，7是一周的天数，8是八爪鱼的触手...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 9是九宫格的格子数，10代表圆满——就像我们的十根手指...
2025-08-21 00:15:10 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 9是九宫格的格子数，10代表圆满——就像我们的十根手指...
2025-08-21 00:15:11 - ai_video_editor - INFO - 保存了 6 条字幕修改
2025-08-21 00:15:13 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 00:15:13 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:15:13 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 00:15:13 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 00:15:13 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 00:15:14 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 00:15:14 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 00:15:14 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5, 6, 7], 'rewritten_5': [8, 9]}
2025-08-21 00:15:14 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 6 条字幕
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 12:15:14
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: 9243b3016df041acb0e8..., 剩余时间: 129600秒
2025-08-21 00:15:14 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10别看这些数字简单，它们就像盖房子...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 00:15:17 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"4231789a07a14453968c69850db19678","task_id":"b8d28d0cc004408b8ce41c81011ce146","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 00:15:17 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 128304 字节
2025-08-21 00:15:17 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755706514.mp3
2025-08-21 00:15:18 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755706514.mp3
2025-08-21 00:15:18 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 32.676000秒
2025-08-21 00:15:18 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 32.676000秒
2025-08-21 00:15:18 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755706514.mp3, 时长: 32.676000秒
2025-08-21 00:15:18 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算25.20s → 实际32.08s (缩放1.273)
2025-08-21 00:15:19 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 00:15:20 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 00:15:20 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 00:15:21 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 00:15:22 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 00:15:23 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 00:15:24 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 00:15:25 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 00:15:26 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 00:15:27 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 00:15:28 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 00:15:28 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 00:15:54 - ai_video_editor - ERROR - TTS和匹配错误: 视频处理失败: 视频分割失败: 视频分割异常: 'VideoSegment' object has no attribute 'output_file'
2025-08-21 00:20:57 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 00:20:57 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 00:20:57 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 00:20:57 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 00:20:57 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 00:21:04 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:21:06 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 00:21:09 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 00:21:10 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 00:21:10 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 00:21:10 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 00:21:10 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 00:21:22 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 3
2025-08-21 00:21:22 - ai_video_editor - INFO - 转换映射关系: 3 个对象 -> 3 个映射
2025-08-21 00:21:22 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1, 2], 'rewritten_1': [3, 4, 5], 'rewritten_2': [6, 7, 8, 9]}
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的现象，就像水烧开时蒸汽顶开壶盖那样简单易懂...
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的现象，就像水烧开时蒸汽顶开壶盖那样简单易懂...
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 其实这个原理在自然界无处不在，比如云朵形成就是空气中的水汽遇冷凝结的过程...
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 其实这个原理在自然界无处不在，比如云朵形成就是空气中的水汽遇冷凝结的过程...
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用三个生活中的例子，带你完全掌握这个科学原理的妙用...
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用三个生活中的例子，带你完全掌握这个科学原理的妙用...
2025-08-21 00:21:22 - ai_video_editor - INFO - 映射关系显示更新完成: 3 组映射
2025-08-21 00:21:52 - ai_video_editor - INFO - 字幕改写成功: 10 -> 3
2025-08-21 00:21:53 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的现象，就像水烧开时蒸汽顶开壶盖那样简单易懂...
2025-08-21 00:21:53 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的现象，就像水烧开时蒸汽顶开壶盖那样简单易懂...
2025-08-21 00:21:53 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 其实这个原理在自然界无处不在，比如云朵形成就是空气中的水汽遇冷凝结的过程...
2025-08-21 00:21:53 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 其实这个原理在自然界无处不在，比如云朵形成就是空气中的水汽遇冷凝结的过程...
2025-08-21 00:21:53 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用三个生活中的例子，带你完全掌握这个科学原理的妙用...
2025-08-21 00:21:53 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用三个生活中的例子，带你完全掌握这个科学原理的妙用...
2025-08-21 00:21:55 - ai_video_editor - INFO - 保存了 3 条字幕修改
2025-08-21 00:21:56 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 00:21:56 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:21:56 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 00:21:56 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 00:21:56 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 00:21:59 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 00:21:59 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 00:21:59 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1, 2], 'rewritten_1': [3, 4, 5], 'rewritten_2': [6, 7, 8, 9]}
2025-08-21 00:21:59 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 3 条字幕
2025-08-21 00:21:59 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 00:21:59 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 00:21:59 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 00:22:00 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 12:21:59
2025-08-21 00:22:00 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 00:22:00 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: 8df6c597fcb948c69257..., 剩余时间: 129599秒
2025-08-21 00:22:00 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别有趣的现象，就像水烧开时蒸汽顶开壶盖那样简单易懂其实这个原理在自然界无处...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 00:22:02 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"f192d93ea99843889074102266a3f18f","task_id":"05c9885e0fac4cb89621d8833b7ff488","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 00:22:02 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 84528 字节
2025-08-21 00:22:02 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755706919.mp3
2025-08-21 00:22:02 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755706919.mp3
2025-08-21 00:22:03 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 21.732000秒
2025-08-21 00:22:03 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 21.732000秒
2025-08-21 00:22:03 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755706919.mp3, 时长: 21.732000秒
2025-08-21 00:22:03 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算15.75s → 实际21.13s (缩放1.342)
2025-08-21 00:22:03 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 00:22:04 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 00:22:05 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 00:22:06 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 00:22:06 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 00:22:07 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 00:22:08 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 00:22:09 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 00:22:10 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 00:22:11 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 00:22:12 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 00:22:12 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 00:23:27 - ai_video_editor - ERROR - TTS和匹配错误: 视频处理失败: 视频分割失败: 视频分割异常: 'VideoSegment' object has no attribute 'output_file'
2025-08-21 00:33:44 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 00:33:52 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 00:33:52 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 00:33:52 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 00:33:52 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 00:33:52 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 00:33:59 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:34:02 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 00:34:04 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 00:34:06 - ai_video_editor - INFO - 开始处理会话: session_46f2f81a
2025-08-21 00:34:06 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 00:34:06 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_46f2f81a
2025-08-21 00:34:06 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 459b48f4-623e-4cf7-b47e-75b5f97e6be9
2025-08-21 00:34:06 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 459b48f4-623e-4cf7-b47e-75b5f97e6be9
2025-08-21 00:34:08 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 00:34:08 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 00:34:08 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 00:34:08 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 00:34:25 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 5
2025-08-21 00:34:25 - ai_video_editor - INFO - 转换映射关系: 5 个对象 -> 5 个映射
2025-08-21 00:34:25 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7], 'rewritten_4': [8, 9]}
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——就像搭积木一样，我们会把复杂的概念拆解成容易理解的...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——就像搭积木一样，我们会把复杂的概念拆解成容易理解的...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们从最基础的说起。想象一下建房子要先打地基，科学研究也是这样从简单开始层层搭建...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们从最基础的说起。想象一下建房子要先打地基，科学研究也是这样从简单开始层层搭建...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用生活中常见的例子来解释，比如用煮鸡蛋来说化学反应，用橡皮筋演示物理现象...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用生活中常见的例子来解释，比如用煮鸡蛋来说化学反应，用橡皮筋演示物理现象...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 记住三个关键要点：第一要保持好奇心，第二注意观察细节，第三所有结论都要经过验证...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 记住三个关键要点：第一要保持好奇心，第二注意观察细节，第三所有结论都要经过验证...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后提醒大家，科学其实就在我们身边。下期我们会用冰箱里的食材做有趣实验，记得关注哦！...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后提醒大家，科学其实就在我们身边。下期我们会用冰箱里的食材做有趣实验，记得关注哦！...
2025-08-21 00:34:25 - ai_video_editor - INFO - 映射关系显示更新完成: 5 组映射
2025-08-21 00:35:00 - ai_video_editor - INFO - 字幕改写成功: 10 -> 5
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——就像搭积木一样，我们会把复杂的概念拆解成容易理解的...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——就像搭积木一样，我们会把复杂的概念拆解成容易理解的...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们从最基础的说起。想象一下建房子要先打地基，科学研究也是这样从简单开始层层搭建...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们从最基础的说起。想象一下建房子要先打地基，科学研究也是这样从简单开始层层搭建...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用生活中常见的例子来解释，比如用煮鸡蛋来说化学反应，用橡皮筋演示物理现象...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接下来我们会用生活中常见的例子来解释，比如用煮鸡蛋来说化学反应，用橡皮筋演示物理现象...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 记住三个关键要点：第一要保持好奇心，第二注意观察细节，第三所有结论都要经过验证...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 记住三个关键要点：第一要保持好奇心，第二注意观察细节，第三所有结论都要经过验证...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后提醒大家，科学其实就在我们身边。下期我们会用冰箱里的食材做有趣实验，记得关注哦！...
2025-08-21 00:35:01 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后提醒大家，科学其实就在我们身边。下期我们会用冰箱里的食材做有趣实验，记得关注哦！...
2025-08-21 00:35:02 - ai_video_editor - INFO - 保存了 5 条字幕修改
2025-08-21 00:35:02 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 00:35:02 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:35:02 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 00:35:02 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 00:35:02 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 00:35:04 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 00:35:04 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 00:35:04 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7], 'rewritten_4': [8, 9]}
2025-08-21 00:35:04 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 5 条字幕
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 12:35:04
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: 47163345cf1e4992ad5c..., 剩余时间: 129600秒
2025-08-21 00:35:04 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别有趣的话题——就像搭积木一样，我们会把复杂的概念拆解成容易理解的小块首先...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 00:35:08 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"b31a916200204a139e0023029fa0aee9","task_id":"8e408d7bc4da4f16b770fb5a97372e94","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 00:35:08 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 180000 字节
2025-08-21 00:35:08 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755707704.mp3
2025-08-21 00:35:09 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755707704.mp3
2025-08-21 00:35:09 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 45.600000秒
2025-08-21 00:35:09 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 45.600000秒
2025-08-21 00:35:09 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755707704.mp3, 时长: 45.600000秒
2025-08-21 00:35:09 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算31.95s → 实际45.00s (缩放1.408)
2025-08-21 00:35:09 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 00:35:11 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 00:35:11 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 00:35:12 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 00:35:13 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 00:35:14 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 00:35:15 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 00:35:16 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 00:35:17 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 00:35:18 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 00:35:18 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 00:35:18 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 00:35:24 - ai_video_editor - ERROR - TTS和匹配错误: 音画匹配失败: 音画匹配异常: 'TTSAndMatchWorkflowThread' object has no attribute 'update_progress'
2025-08-21 00:54:43 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 00:54:43 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 00:54:43 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 00:54:43 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 00:54:43 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 00:54:49 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:54:51 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 00:54:52 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 00:54:54 - ai_video_editor - INFO - 开始处理会话: session_70cffb85
2025-08-21 00:54:54 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 00:54:54 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_70cffb85
2025-08-21 00:54:54 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: db77c64d-81c5-4dc6-8b3d-bad5a71e17a0
2025-08-21 00:54:54 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: db77c64d-81c5-4dc6-8b3d-bad5a71e17a0
2025-08-21 00:54:56 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 00:54:56 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 00:54:56 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 00:54:56 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 00:55:15 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 7
2025-08-21 00:55:15 - ai_video_editor - INFO - 转换映射关系: 7 个对象 -> 7 个映射
2025-08-21 00:55:15 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5], 'rewritten_5': [6, 7], 'rewritten_6': [8, 9]}
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像单个积木，代表任何单独存在的东西...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像单个积木，代表任何单独存在的东西...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 数字2就像一双筷子，总成对出现。3则是稳定的三角结构，比如三脚架...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 数字2就像一双筷子，总成对出现。3则是稳定的三角结构，比如三脚架...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4像桌子的四条腿，5就像我们每只手的手指数量，非常直观好记...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4像桌子的四条腿，5就像我们每只手的手指数量，非常直观好记...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6是骰子上常见点数，7代表一周的天数，8则是八卦的基本符号...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6是骰子上常见点数，7代表一周的天数，8则是八卦的基本符号...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 9作为个位数里最大的，接近完美的10。而10就是我们的手指总数...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 9作为个位数里最大的，接近完美的10。而10就是我们的手指总数...
2025-08-21 00:55:15 - ai_video_editor - INFO - 映射关系显示更新完成: 7 组映射
2025-08-21 00:55:17 - ai_video_editor - INFO - 字幕改写成功: 10 -> 7
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像单个积木，代表任何单独存在的东西...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像单个积木，代表任何单独存在的东西...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 数字2就像一双筷子，总成对出现。3则是稳定的三角结构，比如三脚架...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 数字2就像一双筷子，总成对出现。3则是稳定的三角结构，比如三脚架...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4像桌子的四条腿，5就像我们每只手的手指数量，非常直观好记...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4像桌子的四条腿，5就像我们每只手的手指数量，非常直观好记...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6是骰子上常见点数，7代表一周的天数，8则是八卦的基本符号...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6是骰子上常见点数，7代表一周的天数，8则是八卦的基本符号...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 9作为个位数里最大的，接近完美的10。而10就是我们的手指总数...
2025-08-21 00:55:19 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 9作为个位数里最大的，接近完美的10。而10就是我们的手指总数...
2025-08-21 00:55:20 - ai_video_editor - INFO - 保存了 7 条字幕修改
2025-08-21 00:55:21 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 00:55:21 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 00:55:21 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 00:55:21 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 00:55:21 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 00:55:22 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 00:55:22 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 00:55:22 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5], 'rewritten_5': [6, 7], 'rewritten_6': [8, 9]}
2025-08-21 00:55:22 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 7 条字幕
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 12:55:22
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: bf2eb2122cb04fe98956..., 剩余时间: 129600秒
2025-08-21 00:55:22 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10别看这些数字简单，它们就像盖房子...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 00:55:26 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"17eb8a005aa347a888cce434a1ed5931","task_id":"7036efe4f42746e6b5d618968796c6ee","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 00:55:26 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 171216 字节
2025-08-21 00:55:27 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755708922.mp3
2025-08-21 00:55:27 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755708922.mp3
2025-08-21 00:55:28 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 43.404000秒
2025-08-21 00:55:28 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 43.404000秒
2025-08-21 00:55:28 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755708922.mp3, 时长: 43.404000秒
2025-08-21 00:55:28 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算32.40s → 实际42.80s (缩放1.321)
2025-08-21 00:55:28 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 00:55:29 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 00:55:29 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 00:55:30 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 00:55:31 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 00:55:32 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 00:55:33 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 00:55:34 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 00:55:35 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 00:55:36 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 00:55:37 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 00:55:37 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 00:56:45 - ai_video_editor - ERROR - TTS和匹配错误: 音画匹配失败: 音画匹配异常: 'TTSAndMatchWorkflowThread' object has no attribute '_perform_audio_video_sync'
2025-08-21 00:56:47 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 01:04:21 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:04:21 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:04:21 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:04:21 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:04:21 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:04:28 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:04:30 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:04:31 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:04:32 - ai_video_editor - INFO - 开始处理会话: session_a942a844
2025-08-21 01:04:32 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:04:32 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_a942a844
2025-08-21 01:04:32 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 97014440-1116-4c81-8dc0-95b2b7e12a1f
2025-08-21 01:04:32 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 97014440-1116-4c81-8dc0-95b2b7e12a1f
2025-08-21 01:04:34 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:04:34 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:04:34 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:04:34 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:04:52 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 5
2025-08-21 01:04:52 - ai_video_editor - INFO - 转换映射关系: 5 个对象 -> 5 个映射
2025-08-21 01:04:52 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6], 'rewritten_4': [7, 8, 9]}
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 想象一下数字就像楼梯台阶：从1开始（第一级），2（第二级），3（第三级）这样逐级上升，每个数...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 想象一下数字就像楼梯台阶：从1开始（第一级），2（第二级），3（第三级）这样逐级上升，每个数...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 这种连续递增的特性让数字能准确描述数量变化。比如3个苹果加1个变成4个，就像往篮子里一个个添...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 这种连续递增的特性让数字能准确描述数量变化。比如3个苹果加1个变成4个，就像往篮子里一个个添...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 现在让我们重点看看中间段数字：4,5,6 这三个数正好处在1到10的中间位置，承前启后非常重...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 现在让我们重点看看中间段数字：4,5,6 这三个数正好处在1到10的中间位置，承前启后非常重...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后来到7,8,9,10——这些更大的数字就像成长中的小朋友，数字越大代表的能力越强，10更...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后来到7,8,9,10——这些更大的数字就像成长中的小朋友，数字越大代表的能力越强，10更...
2025-08-21 01:04:52 - ai_video_editor - INFO - 映射关系显示更新完成: 5 组映射
2025-08-21 01:05:05 - ai_video_editor - INFO - 字幕改写成功: 10 -> 5
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 想象一下数字就像楼梯台阶：从1开始（第一级），2（第二级），3（第三级）这样逐级上升，每个数...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 想象一下数字就像楼梯台阶：从1开始（第一级），2（第二级），3（第三级）这样逐级上升，每个数...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 这种连续递增的特性让数字能准确描述数量变化。比如3个苹果加1个变成4个，就像往篮子里一个个添...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 这种连续递增的特性让数字能准确描述数量变化。比如3个苹果加1个变成4个，就像往篮子里一个个添...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 现在让我们重点看看中间段数字：4,5,6 这三个数正好处在1到10的中间位置，承前启后非常重...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 现在让我们重点看看中间段数字：4,5,6 这三个数正好处在1到10的中间位置，承前启后非常重...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后来到7,8,9,10——这些更大的数字就像成长中的小朋友，数字越大代表的能力越强，10更...
2025-08-21 01:05:06 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后来到7,8,9,10——这些更大的数字就像成长中的小朋友，数字越大代表的能力越强，10更...
2025-08-21 01:05:08 - ai_video_editor - INFO - 保存了 5 条字幕修改
2025-08-21 01:05:10 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:05:10 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:05:10 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:05:10 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:05:10 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:05:11 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:05:11 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:05:11 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6], 'rewritten_4': [7, 8, 9]}
2025-08-21 01:05:11 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 5 条字幕
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 13:05:11
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: d66e4b2f05c24d2ba15e..., 剩余时间: 129600秒
2025-08-21 01:05:11 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是由......', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:05:16 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"cbd874a9d8ac468a9426a4692d4103c1","task_id":"83a1b0108ee648729860de502a451d9a","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:05:16 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 204336 字节
2025-08-21 01:05:18 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709511.mp3
2025-08-21 01:05:18 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709511.mp3
2025-08-21 01:05:19 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 51.684000秒
2025-08-21 01:05:19 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 51.684000秒
2025-08-21 01:05:19 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709511.mp3, 时长: 51.684000秒
2025-08-21 01:05:19 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算36.90s → 实际51.08s (缩放1.384)
2025-08-21 01:05:19 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:05:20 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:05:21 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:05:21 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:05:22 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:05:23 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:05:24 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:05:25 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:05:26 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:05:27 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:05:28 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:05:28 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:05:50 - ai_video_editor - ERROR - TTS和匹配错误: 音画匹配失败: 音画同步失败: 缺少必要的文件路径
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 想象一下数字就像楼梯台阶：从1开始（第一级），2（第二级），3（第三级）这样逐级上升，每个数...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 想象一下数字就像楼梯台阶：从1开始（第一级），2（第二级），3（第三级）这样逐级上升，每个数...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 这种连续递增的特性让数字能准确描述数量变化。比如3个苹果加1个变成4个，就像往篮子里一个个添...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 这种连续递增的特性让数字能准确描述数量变化。比如3个苹果加1个变成4个，就像往篮子里一个个添...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 现在让我们重点看看中间段数字：4,5,6 这三个数正好处在1到10的中间位置，承前启后非常重...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 现在让我们重点看看中间段数字：4,5,6 这三个数正好处在1到10的中间位置，承前启后非常重...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后来到7,8,9,10——这些更大的数字就像成长中的小朋友，数字越大代表的能力越强，10更...
2025-08-21 01:06:47 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后来到7,8,9,10——这些更大的数字就像成长中的小朋友，数字越大代表的能力越强，10更...
2025-08-21 01:06:48 - ai_video_editor - INFO - 保存了 5 条字幕修改
2025-08-21 01:06:49 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:06:49 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:06:49 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:06:49 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:06:49 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:06:50 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:06:50 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:06:50 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6], 'rewritten_4': [7, 8, 9]}
2025-08-21 01:06:50 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 5 条字幕
2025-08-21 01:06:50 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:06:50 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: d66e4b2f05c24d2ba15e..., 剩余时间: 129501秒
2025-08-21 01:06:50 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别有趣的话题——数字的奇妙世界。就像搭积木一样，所有复杂的数字都是由......', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:06:55 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"f488ad17f4f84f71a950ebcfabbb4ebf","task_id":"566d699e7572407b903fae3ae8b3d7da","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:06:55 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 199872 字节
2025-08-21 01:06:55 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709610.mp3
2025-08-21 01:06:55 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709610.mp3
2025-08-21 01:06:56 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 50.568000秒
2025-08-21 01:06:56 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 50.568000秒
2025-08-21 01:06:56 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709610.mp3, 时长: 50.568000秒
2025-08-21 01:06:56 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算36.90s → 实际49.97s (缩放1.354)
2025-08-21 01:06:56 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:06:57 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:06:58 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:06:58 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:06:59 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:07:00 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:07:01 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:07:02 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:07:03 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:07:04 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:07:05 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:07:05 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:07:18 - ai_video_editor - ERROR - TTS和匹配错误: 音画匹配失败: 音画同步失败: 缺少必要的文件路径
2025-08-21 01:07:22 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 01:07:30 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:07:30 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:07:31 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:07:31 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:07:31 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:07:36 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:07:38 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:07:40 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:07:40 - ai_video_editor - INFO - 开始处理会话: session_2d6bfd7e
2025-08-21 01:07:40 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:07:40 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_2d6bfd7e
2025-08-21 01:07:40 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: b59dd5d9-c4f9-4727-a1e0-176d706e8832
2025-08-21 01:07:40 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: b59dd5d9-c4f9-4727-a1e0-176d706e8832
2025-08-21 01:07:43 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:07:43 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:07:43 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:07:43 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:08:26 - ai_video_editor - ERROR - 字幕改写失败: 响应解析失败: JSON解析失败: Expecting ',' delimiter: line 1 column 174 (char 173)
2025-08-21 01:08:37 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:08:37 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:08:37 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:08:37 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:08:56 - ai_video_editor - ERROR - 字幕改写失败: 响应解析失败: JSON解析失败: Expecting ',' delimiter: line 1 column 252 (char 251)
2025-08-21 01:10:03 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:10:03 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:10:03 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:10:03 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:10:03 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:10:10 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:10:12 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:10:13 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:10:15 - ai_video_editor - INFO - 开始处理会话: session_ee9d0254
2025-08-21 01:10:15 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:10:15 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_ee9d0254
2025-08-21 01:10:15 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: c9003232-383b-4596-b843-1b82c1443163
2025-08-21 01:10:15 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: c9003232-383b-4596-b843-1b82c1443163
2025-08-21 01:10:17 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:10:17 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:10:17 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:10:17 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:10:36 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 7
2025-08-21 01:10:36 - ai_video_editor - INFO - 转换映射关系: 7 个对象 -> 7 个映射
2025-08-21 01:10:36 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5, 6], 'rewritten_5': [7, 8], 'rewritten_6': [9]}
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像队伍里的第一个人，代表单个物体...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像队伍里的第一个人，代表单个物体...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2和3是一对好搭档，就像你的双眼和三轮车的三个轮子...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2和3是一对好搭档，就像你的双眼和三轮车的三个轮子...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4和5经常出现在生活中——四叶草带来幸运，五指让我们能灵活抓握...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4和5经常出现在生活中——四叶草带来幸运，五指让我们能灵活抓握...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6到8这组数字很有意思：六边形蜂巢、一周七天、八爪章鱼...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6到8这组数字很有意思：六边形蜂巢、一周七天、八爪章鱼...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后9和10接近整数关卡，就像足球比赛临近终场前的倒计时...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后9和10接近整数关卡，就像足球比赛临近终场前的倒计时...
2025-08-21 01:10:36 - ai_video_editor - INFO - 映射关系显示更新完成: 7 组映射
2025-08-21 01:10:40 - ai_video_editor - INFO - 字幕改写成功: 10 -> 7
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 别看这些数字简单，它们就像盖房子的砖块，是所有数学运算的基础...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像队伍里的第一个人，代表单个物体...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 我们先从最简单的1开始：它就像队伍里的第一个人，代表单个物体...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2和3是一对好搭档，就像你的双眼和三轮车的三个轮子...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2和3是一对好搭档，就像你的双眼和三轮车的三个轮子...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4和5经常出现在生活中——四叶草带来幸运，五指让我们能灵活抓握...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 4和5经常出现在生活中——四叶草带来幸运，五指让我们能灵活抓握...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6到8这组数字很有意思：六边形蜂巢、一周七天、八爪章鱼...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 6到8这组数字很有意思：六边形蜂巢、一周七天、八爪章鱼...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后9和10接近整数关卡，就像足球比赛临近终场前的倒计时...
2025-08-21 01:10:41 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后9和10接近整数关卡，就像足球比赛临近终场前的倒计时...
2025-08-21 01:10:42 - ai_video_editor - INFO - 保存了 7 条字幕修改
2025-08-21 01:10:43 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:10:43 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:10:43 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:10:43 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:10:43 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:10:43 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:10:43 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:10:43 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5, 6], 'rewritten_5': [7, 8], 'rewritten_6': [9]}
2025-08-21 01:10:43 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 7 条字幕
2025-08-21 01:10:43 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:10:43 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 01:10:43 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 01:10:44 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 13:10:43
2025-08-21 01:10:44 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 01:10:44 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: c9c9c4f2cf6144c6a7ee..., 剩余时间: 129599秒
2025-08-21 01:10:44 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别基础但超级重要的数学概念——数字1到10别看这些数字简单，它们就像盖房子...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:10:48 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"1d6f2274dcc341b8bef85a568e082af2","task_id":"2a72534852204787987cf743d58f9269","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:10:48 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 161136 字节
2025-08-21 01:10:48 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709843.mp3
2025-08-21 01:10:48 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709843.mp3
2025-08-21 01:10:49 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 40.884000秒
2025-08-21 01:10:49 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 40.884000秒
2025-08-21 01:10:49 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709843.mp3, 时长: 40.884000秒
2025-08-21 01:10:49 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算30.75s → 实际40.28s (缩放1.310)
2025-08-21 01:10:49 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:10:50 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:10:51 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:10:52 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:10:52 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:10:53 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:10:54 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:10:55 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:10:56 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:10:57 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:10:58 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:10:58 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:10:58 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 01:10:58 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755709843.mp3
2025-08-21 01:10:58 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755709843.srt
2025-08-21 01:10:58 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: None
2025-08-21 01:11:07 - ai_video_editor - ERROR - TTS和匹配错误: 音画匹配失败: 音画同步失败: 缺少必要的文件路径: video_segments_dir
2025-08-21 01:11:17 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 01:12:14 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:12:14 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:12:14 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:12:14 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:12:14 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:12:20 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:12:21 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:12:23 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:12:24 - ai_video_editor - INFO - 开始处理会话: session_0112d159
2025-08-21 01:12:24 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:12:24 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_0112d159
2025-08-21 01:12:24 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: d850b919-278c-4bca-8ab7-36614aba8a7f
2025-08-21 01:12:24 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: d850b919-278c-4bca-8ab7-36614aba8a7f
2025-08-21 01:12:26 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:12:26 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:12:26 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:12:26 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:13:59 - ai_video_editor - ERROR - 字幕改写失败: 响应解析失败: JSON解析失败: Expecting ',' delimiter: line 1 column 90 (char 89)
2025-08-21 01:14:01 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 01:15:50 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:15:50 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:15:51 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:15:51 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:15:51 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:15:59 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:16:01 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:16:02 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:16:03 - ai_video_editor - INFO - 开始处理会话: session_079d26c6
2025-08-21 01:16:03 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:16:03 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_079d26c6
2025-08-21 01:16:03 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 3e6e1259-f748-4515-abb9-c325b022097d
2025-08-21 01:16:03 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 3e6e1259-f748-4515-abb9-c325b022097d
2025-08-21 01:16:05 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:16:05 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:16:05 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:16:05 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:16:33 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 10
2025-08-21 01:16:33 - ai_video_editor - INFO - 转换映射关系: 10 个对象 -> 10 个映射
2025-08-21 01:16:33 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5], 'rewritten_5': [6], 'rewritten_6': [7], 'rewritten_7': [8], 'rewritten_8': [9], 'rewritten_9': [10]}
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题，就像拆快递一样，咱们一层层揭开它的神秘面纱...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题，就像拆快递一样，咱们一层层揭开它的神秘面纱...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先我们需要理解最基础的概念，这就像搭积木，得先从最底下的方块开始垒...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先我们需要理解最基础的概念，这就像搭积木，得先从最底下的方块开始垒...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 举个例子来说，这个过程就像煮咖啡——咖啡粉是原料，热水是能量，最后得到香醇的咖啡...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 举个例子来说，这个过程就像煮咖啡——咖啡粉是原料，热水是能量，最后得到香醇的咖啡...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来我们会发现三个关键阶段，每个阶段都像接力赛跑一样环环相扣...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来我们会发现三个关键阶段，每个阶段都像接力赛跑一样环环相扣...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 第一阶段就像春天播种，种子在土壤里悄悄发芽（这里我们可以观察到温度变化带来的影响）...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 第一阶段就像春天播种，种子在土壤里悄悄发芽（这里我们可以观察到温度变化带来的影响）...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 第二阶段相当于植物生长，需要阳光和养分（注意这个过程中产生的特殊现象）...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 第二阶段相当于植物生长，需要阳光和养分（注意这个过程中产生的特殊现象）...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后阶段就是收获果实啦！不过要注意果实成熟需要满足特定条件哦...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后阶段就是收获果实啦！不过要注意果实成熟需要满足特定条件哦...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 整个过程的原理其实就像做蛋糕，材料配比和火候缺一不可...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 整个过程的原理其实就像做蛋糕，材料配比和火候缺一不可...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 如果我们把条件调整一下，比如改变温度或时间，结果就会像调音器一样产生不同效果...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 如果我们把条件调整一下，比如改变温度或时间，结果就会像调音器一样产生不同效果...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 记住这些规律后，下次遇到类似现象你就能像侦探一样找出背后的奥秘啦！...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 记住这些规律后，下次遇到类似现象你就能像侦探一样找出背后的奥秘啦！...
2025-08-21 01:16:33 - ai_video_editor - INFO - 映射关系显示更新完成: 10 组映射
2025-08-21 01:17:21 - ai_video_editor - INFO - 字幕改写成功: 10 -> 10
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题，就像拆快递一样，咱们一层层揭开它的神秘面纱...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 大家好！今天我们要聊一个特别有趣的话题，就像拆快递一样，咱们一层层揭开它的神秘面纱...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先我们需要理解最基础的概念，这就像搭积木，得先从最底下的方块开始垒...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先我们需要理解最基础的概念，这就像搭积木，得先从最底下的方块开始垒...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 举个例子来说，这个过程就像煮咖啡——咖啡粉是原料，热水是能量，最后得到香醇的咖啡...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 举个例子来说，这个过程就像煮咖啡——咖啡粉是原料，热水是能量，最后得到香醇的咖啡...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来我们会发现三个关键阶段，每个阶段都像接力赛跑一样环环相扣...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来我们会发现三个关键阶段，每个阶段都像接力赛跑一样环环相扣...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 第一阶段就像春天播种，种子在土壤里悄悄发芽（这里我们可以观察到温度变化带来的影响）...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 第一阶段就像春天播种，种子在土壤里悄悄发芽（这里我们可以观察到温度变化带来的影响）...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 第二阶段相当于植物生长，需要阳光和养分（注意这个过程中产生的特殊现象）...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 第二阶段相当于植物生长，需要阳光和养分（注意这个过程中产生的特殊现象）...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后阶段就是收获果实啦！不过要注意果实成熟需要满足特定条件哦...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后阶段就是收获果实啦！不过要注意果实成熟需要满足特定条件哦...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 整个过程的原理其实就像做蛋糕，材料配比和火候缺一不可...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 整个过程的原理其实就像做蛋糕，材料配比和火候缺一不可...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 如果我们把条件调整一下，比如改变温度或时间，结果就会像调音器一样产生不同效果...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 如果我们把条件调整一下，比如改变温度或时间，结果就会像调音器一样产生不同效果...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 记住这些规律后，下次遇到类似现象你就能像侦探一样找出背后的奥秘啦！...
2025-08-21 01:17:22 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 记住这些规律后，下次遇到类似现象你就能像侦探一样找出背后的奥秘啦！...
2025-08-21 01:17:23 - ai_video_editor - INFO - 保存了 10 条字幕修改
2025-08-21 01:17:24 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:17:24 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:17:24 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:17:24 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:17:24 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:17:25 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:17:25 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:17:25 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2], 'rewritten_2': [3], 'rewritten_3': [4], 'rewritten_4': [5], 'rewritten_5': [6], 'rewritten_6': [7], 'rewritten_7': [8], 'rewritten_8': [9], 'rewritten_9': [10]}
2025-08-21 01:17:25 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 10 条字幕
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 13:17:25
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: 78c081b735954f7da3eb..., 剩余时间: 129600秒
2025-08-21 01:17:25 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '大家好！今天我们要聊一个特别有趣的话题，就像拆快递一样，咱们一层层揭开它的神秘面纱首先我们需要理解最...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:17:31 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"82fa08c245d74bb5b9c9e29f4c90caf0","task_id":"0fa18c2bca3d4bb19c3471e3eb5c1fce","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:17:31 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 246384 字节
2025-08-21 01:17:31 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710245.mp3
2025-08-21 01:17:32 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710245.mp3
2025-08-21 01:17:32 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 62.196000秒
2025-08-21 01:17:32 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 62.196000秒
2025-08-21 01:17:32 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710245.mp3, 时长: 62.196000秒
2025-08-21 01:17:32 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算52.35s → 实际61.60s (缩放1.177)
2025-08-21 01:17:32 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:17:33 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:17:34 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:17:35 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:17:36 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:17:37 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:17:38 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:17:39 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:17:39 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:17:41 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:17:41 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:17:41 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:17:42 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 01:17:42 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710245.mp3
2025-08-21 01:17:42 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755710245.srt
2025-08-21 01:17:42 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - INFO - 开始音画同步处理...
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - INFO - 计算字幕时长: 10 条
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_000.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_001.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_002.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_003.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_004.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_005.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_006.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_007.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_008.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_010.mp4
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - INFO - 计算视频时长: 10 条
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - INFO - 输出目录结构创建完成
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - INFO - 音频和字幕文件重命名完成: New_tts_audio_1755710245.mp3, New_tts_subtitles_1755710245.srt
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕0没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕1没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕2没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕3没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕4没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕5没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕6没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕7没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕8没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - WARNING - 字幕9没有匹配的视频片段
2025-08-21 01:17:42 - ai_video_editor.services.audio_video_sync - INFO - 音画同步处理完成
2025-08-21 01:17:42 - ai_video_editor - INFO - 已切换到音画匹配标签页 (索引: 3)
2025-08-21 01:17:44 - ai_video_editor - INFO - TTS和匹配完成: {'success': True, 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_audio_1755710245.mp3', 'subtitle_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_subtitles_1755710245.srt', 'match_data': [{'subtitle_index': 0, 'subtitle_text': '大家好！今天我们要聊一个特别有趣的话题，就像拆快递一样，咱们一层层揭开它的神秘面纱', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_000.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 5.933, 'speed_factor': 2.9665, 'time_match': False}, {'subtitle_index': 1, 'subtitle_text': '首先我们需要理解最基础的概念，这就像搭积木，得先从最底下的方块开始垒', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_001.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 2.2330000000000005, 'speed_factor': 1.1165000000000003, 'time_match': True}, {'subtitle_index': 2, 'subtitle_text': '举个例子来说，这个过程就像煮咖啡——咖啡粉是原料，热水是能量，最后得到香醇的咖啡', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_002.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 1.8339999999999996, 'speed_factor': 0.9169999999999998, 'time_match': True}, {'subtitle_index': 3, 'subtitle_text': '接下来我们会发现三个关键阶段，每个阶段都像接力赛跑一样环环相扣', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_003.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_001.mp4'], 'video_count': 2, 'total_video_duration': 2.4000000000000004, 'speed_factor': 1.2000000000000002, 'time_match': True}, {'subtitle_index': 4, 'subtitle_text': '第一阶段就像春天播种，种子在土壤里悄悄发芽（这里我们可以观察到温度变化带来的影响）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_004.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 2.065999999999999, 'speed_factor': 1.0329999999999995, 'time_match': True}, {'subtitle_index': 5, 'subtitle_text': '第二阶段相当于植物生长，需要阳光和养分（注意这个过程中产生的特殊现象）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_005.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 1.866999999999999, 'speed_factor': 0.9334999999999996, 'time_match': True}, {'subtitle_index': 6, 'subtitle_text': '最后阶段就是收获果实啦！不过要注意果实成熟需要满足特定条件哦', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_006.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_006\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_006\\video_001.mp4'], 'video_count': 2, 'total_video_duration': 2.1330000000000027, 'speed_factor': 1.0665000000000013, 'time_match': True}, {'subtitle_index': 7, 'subtitle_text': '整个过程的原理其实就像做蛋糕，材料配比和火候缺一不可', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_007.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_007\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 2.799999999999997, 'speed_factor': 1.3999999999999986, 'time_match': False}, {'subtitle_index': 8, 'subtitle_text': '如果我们把条件调整一下，比如改变温度或时间，结果就会像调音器一样产生不同效果', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_008.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_008\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 1.2006670000000028, 'speed_factor': 0.6003335000000014, 'time_match': False}, {'subtitle_index': 9, 'subtitle_text': '记住这些规律后，下次遇到类似现象你就能像侦探一样找出背后的奥秘啦！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_009.mp3', 'audio_duration': 2.0, 'video_files': [], 'video_count': 0, 'total_video_duration': 0.0, 'speed_factor': 0.0, 'time_match': False}], 'match_count': 10, 'output_dir': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output', 'processing_time': 16.7741482257843}
2025-08-21 01:28:30 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:28:30 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:28:30 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:28:30 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:28:30 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:28:35 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:28:37 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:28:38 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:28:39 - ai_video_editor - INFO - 开始处理会话: session_b89087c2
2025-08-21 01:28:39 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:28:39 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_b89087c2
2025-08-21 01:28:39 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 8c5289af-1992-4b94-8718-d70d02421784
2025-08-21 01:28:39 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 8c5289af-1992-4b94-8718-d70d02421784
2025-08-21 01:28:41 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:28:41 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:28:41 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:28:41 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:29:03 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 4
2025-08-21 01:29:03 - ai_video_editor - INFO - 转换映射关系: 4 个对象 -> 4 个映射
2025-08-21 01:29:03 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7, 8, 9]}
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，保证让你大开眼界！...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，保证让你大开眼界！...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，这玩意儿虽然听起来高大上，其实原理特别接地气——就像煮开水时壶盖会被顶开一样...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，这玩意儿虽然听起来高大上，其实原理特别接地气——就像煮开水时壶盖会被顶开一样...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 具体来说呢，当A和B相遇时（不是罗密欧与朱丽叶那种相遇啊），会产生一种神奇的反应，这时候就会...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 具体来说呢，当A和B相遇时（不是罗密欧与朱丽叶那种相遇啊），会产生一种神奇的反应，这时候就会...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 砰！像摇晃过的可乐突然打开——能量瞬间爆发！不过别担心，这个‘砰’其实是比喻啦（当然如果是真...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 砰！像摇晃过的可乐突然打开——能量瞬间爆发！不过别担心，这个‘砰’其实是比喻啦（当然如果是真...
2025-08-21 01:29:03 - ai_video_editor - INFO - 映射关系显示更新完成: 4 组映射
2025-08-21 01:29:07 - ai_video_editor - INFO - 字幕改写成功: 10 -> 4
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，保证让你大开眼界！...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，保证让你大开眼界！...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，这玩意儿虽然听起来高大上，其实原理特别接地气——就像煮开水时壶盖会被顶开一样...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，这玩意儿虽然听起来高大上，其实原理特别接地气——就像煮开水时壶盖会被顶开一样...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 具体来说呢，当A和B相遇时（不是罗密欧与朱丽叶那种相遇啊），会产生一种神奇的反应，这时候就会...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 具体来说呢，当A和B相遇时（不是罗密欧与朱丽叶那种相遇啊），会产生一种神奇的反应，这时候就会...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 砰！像摇晃过的可乐突然打开——能量瞬间爆发！不过别担心，这个‘砰’其实是比喻啦（当然如果是真...
2025-08-21 01:29:16 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 砰！像摇晃过的可乐突然打开——能量瞬间爆发！不过别担心，这个‘砰’其实是比喻啦（当然如果是真...
2025-08-21 01:29:18 - ai_video_editor - INFO - 保存了 4 条字幕修改
2025-08-21 01:29:23 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:29:23 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:29:23 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:29:23 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:29:23 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:29:24 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:29:24 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:29:24 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7, 8, 9]}
2025-08-21 01:29:24 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 4 条字幕
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 13:29:24
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: be8f9adfe3134fa09e37..., 剩余时间: 129600秒
2025-08-21 01:29:24 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '嘿伙计们！今天咱们要聊个超级有意思的东西，保证让你大开眼界！首先咱们得知道，这玩意儿虽然听起来高大上...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:29:28 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"8379656f761e45f18a9f872cd763d086","task_id":"96e9895d5e444d7f85e9ab6b6a2f1a4e","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:29:28 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 144720 字节
2025-08-21 01:29:28 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710964.mp3
2025-08-21 01:29:28 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710964.mp3
2025-08-21 01:29:29 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 36.780000秒
2025-08-21 01:29:29 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 36.780000秒
2025-08-21 01:29:29 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710964.mp3, 时长: 36.780000秒
2025-08-21 01:29:29 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算26.85s → 实际36.18s (缩放1.347)
2025-08-21 01:29:29 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:29:30 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:29:31 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:29:31 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:29:32 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:29:33 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:29:34 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:29:35 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:29:36 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:29:37 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:29:38 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:29:38 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:29:38 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 01:29:38 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755710964.mp3
2025-08-21 01:29:38 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755710964.srt
2025-08-21 01:29:38 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 01:29:38 - ai_video_editor.ui.main_window - ERROR - 音画同步执行失败: 'TTSAndMatchWorkflowThread' object has no attribute '_get_current_mapping'
2025-08-21 01:29:40 - ai_video_editor - ERROR - TTS和匹配错误: 音画匹配失败: 音画同步失败: 音画同步异常: 'TTSAndMatchWorkflowThread' object has no attribute '_get_current_mapping'
2025-08-21 01:29:57 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 01:35:17 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:35:17 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:35:17 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:35:17 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:35:17 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:35:23 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:35:24 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:35:26 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:35:27 - ai_video_editor - INFO - 开始处理会话: session_072a1452
2025-08-21 01:35:27 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:35:27 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_072a1452
2025-08-21 01:35:27 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 25d0519a-c46a-4996-a95f-702a73a89456
2025-08-21 01:35:27 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 25d0519a-c46a-4996-a95f-702a73a89456
2025-08-21 01:35:29 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:35:29 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:35:29 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:35:29 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:35:50 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 4
2025-08-21 01:35:50 - ai_video_editor - INFO - 转换映射关系: 4 个对象 -> 4 个映射
2025-08-21 01:35:50 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7, 8, 9]}
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们来聊个特别烧脑的话题——不过别怕，我保证用大白话给你们整明白！...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们来聊个特别烧脑的话题——不过别怕，我保证用大白话给你们整明白！...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得知道地球其实是个超级大磁铁！不信你拿出指南针，那指针永远指着北边，就是因为地磁...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得知道地球其实是个超级大磁铁！不信你拿出指南针，那指针永远指着北边，就是因为地磁...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 但这个磁场可不是一成不变的哦！科学家发现地磁北极每年都在偷偷移动，最近它正以每小时40米的速...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 但这个磁场可不是一成不变的哦！科学家发现地磁北极每年都在偷偷移动，最近它正以每小时40米的速...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 更刺激的是——地磁场居然还会玩翻转！南北极互换位置这种事，在地球历史上已经发生过几百次了！...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 更刺激的是——地磁场居然还会玩翻转！南北极互换位置这种事，在地球历史上已经发生过几百次了！...
2025-08-21 01:35:50 - ai_video_editor - INFO - 映射关系显示更新完成: 4 组映射
2025-08-21 01:38:26 - ai_video_editor - INFO - 字幕改写成功: 10 -> 4
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们来聊个特别烧脑的话题——不过别怕，我保证用大白话给你们整明白！...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们来聊个特别烧脑的话题——不过别怕，我保证用大白话给你们整明白！...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得知道地球其实是个超级大磁铁！不信你拿出指南针，那指针永远指着北边，就是因为地磁...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得知道地球其实是个超级大磁铁！不信你拿出指南针，那指针永远指着北边，就是因为地磁...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 但这个磁场可不是一成不变的哦！科学家发现地磁北极每年都在偷偷移动，最近它正以每小时40米的速...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 但这个磁场可不是一成不变的哦！科学家发现地磁北极每年都在偷偷移动，最近它正以每小时40米的速...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 更刺激的是——地磁场居然还会玩翻转！南北极互换位置这种事，在地球历史上已经发生过几百次了！...
2025-08-21 01:38:27 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 更刺激的是——地磁场居然还会玩翻转！南北极互换位置这种事，在地球历史上已经发生过几百次了！...
2025-08-21 01:38:28 - ai_video_editor - INFO - 保存了 4 条字幕修改
2025-08-21 01:38:29 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:38:29 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:38:29 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:38:29 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:38:29 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:38:30 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:38:30 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:38:30 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7, 8, 9]}
2025-08-21 01:38:30 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 4 条字幕
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 13:38:30
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: ac4762d4b38041c8b4ac..., 剩余时间: 129600秒
2025-08-21 01:38:30 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '嘿伙计们！今天咱们来聊个特别烧脑的话题——不过别怕，我保证用大白话给你们整明白！首先啊，咱们得知道地...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:38:34 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"efe7529c4a3243c180f7abf5882ed3b4","task_id":"9e23e0fc5e424037a53a858fcd9752a9","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:38:34 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 161712 字节
2025-08-21 01:38:34 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755711510.mp3
2025-08-21 01:38:35 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755711510.mp3
2025-08-21 01:38:35 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 41.028000秒
2025-08-21 01:38:35 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 41.028000秒
2025-08-21 01:38:35 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755711510.mp3, 时长: 41.028000秒
2025-08-21 01:38:35 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算27.75s → 实际40.43s (缩放1.457)
2025-08-21 01:38:35 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:38:36 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:38:37 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:38:38 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:38:39 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:38:40 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:38:40 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:38:41 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:38:42 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:38:43 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:38:44 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:38:44 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:38:44 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 01:38:44 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755711510.mp3
2025-08-21 01:38:44 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755711510.srt
2025-08-21 01:38:44 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 01:38:44 - ai_video_editor.ui.main_window - INFO - 使用的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7, 8, 9]}
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - INFO - 开始音画同步处理...
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - INFO - 计算字幕时长: 4 条
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_000.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_001.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_002.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_003.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_004.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_005.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_006.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_007.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_008.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - INFO - 计算视频时长: 4 条
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - INFO - 输出目录结构创建完成
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - INFO - 音频和字幕文件重命名完成: New_tts_audio_1755711510.mp3, New_tts_subtitles_1755711510.srt
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕0没有匹配的视频片段
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕1没有匹配的视频片段
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕2没有匹配的视频片段
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕3没有匹配的视频片段
2025-08-21 01:38:44 - ai_video_editor.services.audio_video_sync - INFO - 音画同步处理完成
2025-08-21 01:38:44 - ai_video_editor - INFO - 已切换到音画匹配标签页 (索引: 3)
2025-08-21 01:39:51 - ai_video_editor - INFO - TTS和匹配完成: {'success': True, 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_audio_1755711510.mp3', 'subtitle_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_subtitles_1755711510.srt', 'match_data': [{'subtitle_index': 0, 'subtitle_text': '嘿伙计们！今天咱们来聊个特别烧脑的话题——不过别怕，我保证用大白话给你们整明白！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_000.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 5.933, 'speed_factor': 2.9665, 'time_match': False}, {'subtitle_index': 1, 'subtitle_text': '首先啊，咱们得知道地球其实是个超级大磁铁！不信你拿出指南针，那指针永远指着北边，就是因为地磁场...', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_001.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 4.067, 'speed_factor': 2.0335, 'time_match': False}, {'subtitle_index': 2, 'subtitle_text': '但这个磁场可不是一成不变的哦！科学家发现地磁北极每年都在偷偷移动，最近它正以每小时40米的速度...', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_002.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 4.465999999999999, 'speed_factor': 2.2329999999999997, 'time_match': False}, {'subtitle_index': 3, 'subtitle_text': '更刺激的是——地磁场居然还会玩翻转！南北极互换位置这种事，在地球历史上已经发生过几百次了！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_003.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 8.000667000000002, 'speed_factor': 4.000333500000001, 'time_match': False}], 'match_count': 4, 'output_dir': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output', 'processing_time': 14.481776714324951}
2025-08-21 01:50:59 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 01:50:59 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 01:50:59 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 01:50:59 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 01:50:59 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 01:51:06 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:51:07 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 01:51:10 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 01:51:11 - ai_video_editor - INFO - 开始处理会话: session_47c0daf7
2025-08-21 01:51:11 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 01:51:11 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_47c0daf7
2025-08-21 01:51:11 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 4d76acc6-a975-4410-9485-bcc12a3b0713
2025-08-21 01:51:11 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 4d76acc6-a975-4410-9485-bcc12a3b0713
2025-08-21 01:51:13 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 01:51:13 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 01:51:13 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 01:51:13 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 01:51:29 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 6
2025-08-21 01:51:29 - ai_video_editor - INFO - 转换映射关系: 6 个对象 -> 6 个映射
2025-08-21 01:51:29 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0], 'rewritten_1': [1, 2], 'rewritten_2': [3], 'rewritten_3': [4, 5], 'rewritten_4': [6, 7], 'rewritten_5': [8, 9]}
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，准备好瓜子小板凳了没？...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，准备好瓜子小板凳了没？...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得从最基础的说起。就像盖房子得先打地基对吧？...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得从最基础的说起。就像盖房子得先打地基对吧？...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 别看这些东西编号从1到10好像很枯燥，其实暗藏玄机！...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 别看这些东西编号从1到10好像很枯燥，其实暗藏玄机！...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来这几步特别关键，我用人话给大家翻译翻译...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来这几步特别关键，我用人话给大家翻译翻译...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 5678这几步简直就是连环扣，一环套一环特别有意思...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 5678这几步简直就是连环扣，一环套一环特别有意思...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 最后这两个步骤简直是神来之笔！到底多神奇？且听下回分解～...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 最后这两个步骤简直是神来之笔！到底多神奇？且听下回分解～...
2025-08-21 01:51:29 - ai_video_editor - INFO - 映射关系显示更新完成: 6 组映射
2025-08-21 01:51:36 - ai_video_editor - INFO - 字幕改写成功: 10 -> 6
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，准备好瓜子小板凳了没？...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！今天咱们要聊个超级有意思的东西，准备好瓜子小板凳了没？...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得从最基础的说起。就像盖房子得先打地基对吧？...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先啊，咱们得从最基础的说起。就像盖房子得先打地基对吧？...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 别看这些东西编号从1到10好像很枯燥，其实暗藏玄机！...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 别看这些东西编号从1到10好像很枯燥，其实暗藏玄机！...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来这几步特别关键，我用人话给大家翻译翻译...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 接下来这几步特别关键，我用人话给大家翻译翻译...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 5678这几步简直就是连环扣，一环套一环特别有意思...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 5678这几步简直就是连环扣，一环套一环特别有意思...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 最后这两个步骤简直是神来之笔！到底多神奇？且听下回分解～...
2025-08-21 01:52:06 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 最后这两个步骤简直是神来之笔！到底多神奇？且听下回分解～...
2025-08-21 01:52:07 - ai_video_editor - INFO - 保存了 6 条字幕修改
2025-08-21 01:52:08 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 01:52:08 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 01:52:08 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 01:52:08 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 01:52:08 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 01:52:09 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 01:52:09 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 01:52:09 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0], 'rewritten_1': [1, 2], 'rewritten_2': [3], 'rewritten_3': [4, 5], 'rewritten_4': [6, 7], 'rewritten_5': [8, 9]}
2025-08-21 01:52:09 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 6 条字幕
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 13:52:09
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: e7d5b80022634bc18407..., 剩余时间: 129600秒
2025-08-21 01:52:09 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '嘿伙计们！今天咱们要聊个超级有意思的东西，准备好瓜子小板凳了没？首先啊，咱们得从最基础的说起。就像盖...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 01:52:13 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"ebfa03b3b39b48da9422b05f343a2222","task_id":"a045d4d0e7414ee798cc110bc6e7ae65","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 01:52:13 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 138960 字节
2025-08-21 01:52:13 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755712329.mp3
2025-08-21 01:52:13 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755712329.mp3
2025-08-21 01:52:14 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 35.340000秒
2025-08-21 01:52:14 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 35.340000秒
2025-08-21 01:52:14 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755712329.mp3, 时长: 35.340000秒
2025-08-21 01:52:14 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算24.15s → 实际34.74s (缩放1.439)
2025-08-21 01:52:14 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 01:52:15 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 01:52:16 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 01:52:17 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 01:52:17 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 01:52:18 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 01:52:19 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 01:52:20 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 01:52:21 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 01:52:22 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 01:52:23 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 01:52:23 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 01:52:23 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 01:52:23 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755712329.mp3
2025-08-21 01:52:23 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755712329.srt
2025-08-21 01:52:23 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 01:52:23 - ai_video_editor.ui.main_window - INFO - 使用的映射关系: {'rewritten_0': [0], 'rewritten_1': [1, 2], 'rewritten_2': [3], 'rewritten_3': [4, 5], 'rewritten_4': [6, 7], 'rewritten_5': [8, 9]}
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - INFO - 开始音画同步处理...
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - INFO - 计算字幕时长: 6 条
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_000.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_001.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_002.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_003.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_004.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_005.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_006.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_007.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_008.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - INFO - 计算视频时长: 6 条
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - INFO - 输出目录结构创建完成
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - INFO - 音频和字幕文件重命名完成: New_tts_audio_1755712329.mp3, New_tts_subtitles_1755712329.srt
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 字幕0没有匹配的视频片段
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 字幕1没有匹配的视频片段
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 字幕2没有匹配的视频片段
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 字幕3没有匹配的视频片段
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 字幕4没有匹配的视频片段
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - WARNING - 字幕5没有匹配的视频片段
2025-08-21 01:52:23 - ai_video_editor.services.audio_video_sync - INFO - 音画同步处理完成
2025-08-21 01:52:23 - ai_video_editor - INFO - 已切换到音画匹配标签页 (索引: 3)
2025-08-21 01:52:25 - ai_video_editor - INFO - TTS和匹配完成: {'success': True, 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_audio_1755712329.mp3', 'subtitle_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_subtitles_1755712329.srt', 'match_data': [{'subtitle_index': 0, 'subtitle_text': '嘿伙计们！今天咱们要聊个超级有意思的东西，准备好瓜子小板凳了没？', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_000.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 3.833, 'speed_factor': 1.9165, 'time_match': False}, {'subtitle_index': 1, 'subtitle_text': '首先啊，咱们得从最基础的说起。就像盖房子得先打地基对吧？', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_001.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 4.333, 'speed_factor': 2.1665, 'time_match': False}, {'subtitle_index': 2, 'subtitle_text': '别看这些东西编号从1到10好像很枯燥，其实暗藏玄机！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_002.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 1.8339999999999996, 'speed_factor': 0.9169999999999998, 'time_match': True}, {'subtitle_index': 3, 'subtitle_text': '接下来这几步特别关键，我用人话给大家翻译翻译', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_003.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 4.465999999999999, 'speed_factor': 2.2329999999999997, 'time_match': False}, {'subtitle_index': 4, 'subtitle_text': '5678这几步简直就是连环扣，一环套一环特别有意思', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_004.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 4.000000000000002, 'speed_factor': 2.000000000000001, 'time_match': False}, {'subtitle_index': 5, 'subtitle_text': '最后这两个步骤简直是神来之笔！到底多神奇？且听下回分解～', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_005.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 4.000667, 'speed_factor': 2.0003335, 'time_match': False}], 'match_count': 6, 'output_dir': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output', 'processing_time': 13.997663497924805}
2025-08-21 01:52:47 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 02:17:38 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 02:17:38 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 02:17:38 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 02:17:38 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 02:17:38 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 02:17:45 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 02:17:46 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 02:17:49 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 02:17:50 - ai_video_editor - INFO - 开始处理会话: session_86522a98
2025-08-21 02:17:50 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 02:17:50 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_86522a98
2025-08-21 02:17:50 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 349714c7-95cd-4f64-aece-91a3c410d8e5
2025-08-21 02:17:50 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 349714c7-95cd-4f64-aece-91a3c410d8e5
2025-08-21 02:17:52 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 02:17:52 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 02:17:52 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 02:17:52 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 02:18:20 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 7
2025-08-21 02:18:20 - ai_video_editor - INFO - 转换映射关系: 7 个对象 -> 7 个映射
2025-08-21 02:18:20 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3, 4], 'rewritten_4': [5], 'rewritten_5': [6, 7, 8], 'rewritten_6': [9]}
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！咱们今天要聊个超级有意思的东西——虽然现在不能剧透具体内容，但相信我，绝对让你大开...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！咱们今天要聊个超级有意思的东西——虽然现在不能剧透具体内容，但相信我，绝对让你大开...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，万事万物都有个开头（就像你早上得先睁开眼才能起床对吧）...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，万事万物都有个开头（就像你早上得先睁开眼才能起床对吧）...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 然后这事儿就像叠叠乐一样，第二步必须稳稳接住第一步（不然可就全垮啦）...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 然后这事儿就像叠叠乐一样，第二步必须稳稳接住第一步（不然可就全垮啦）...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 说到第三第四步啊，简直就是相声里的捧哏和逗哏——缺了谁都不行！...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 说到第三第四步啊，简直就是相声里的捧哏和逗哏——缺了谁都不行！...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 重点来了！第五步可是隐藏的王者阶段，就像泡面里的调味包——看着不起眼但至关重要！...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 重点来了！第五步可是隐藏的王者阶段，就像泡面里的调味包——看着不起眼但至关重要！...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 六七八步其实是三兄弟组团打怪，它们偷偷搞了个‘连续暴击三联’...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 六七八步其实是三兄弟组团打怪，它们偷偷搞了个‘连续暴击三联’...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后两步更绝！就像吃薯片最后两片——虽然快没了但反而最珍贵！...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后两步更绝！就像吃薯片最后两片——虽然快没了但反而最珍贵！...
2025-08-21 02:18:20 - ai_video_editor - INFO - 映射关系显示更新完成: 7 组映射
2025-08-21 02:18:25 - ai_video_editor - INFO - 字幕改写成功: 10 -> 7
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！咱们今天要聊个超级有意思的东西——虽然现在不能剧透具体内容，但相信我，绝对让你大开...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿伙计们！咱们今天要聊个超级有意思的东西——虽然现在不能剧透具体内容，但相信我，绝对让你大开...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，万事万物都有个开头（就像你早上得先睁开眼才能起床对吧）...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 首先咱们得知道，万事万物都有个开头（就像你早上得先睁开眼才能起床对吧）...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 然后这事儿就像叠叠乐一样，第二步必须稳稳接住第一步（不然可就全垮啦）...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 然后这事儿就像叠叠乐一样，第二步必须稳稳接住第一步（不然可就全垮啦）...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 说到第三第四步啊，简直就是相声里的捧哏和逗哏——缺了谁都不行！...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 说到第三第四步啊，简直就是相声里的捧哏和逗哏——缺了谁都不行！...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 重点来了！第五步可是隐藏的王者阶段，就像泡面里的调味包——看着不起眼但至关重要！...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 重点来了！第五步可是隐藏的王者阶段，就像泡面里的调味包——看着不起眼但至关重要！...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 六七八步其实是三兄弟组团打怪，它们偷偷搞了个‘连续暴击三联’...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 六七八步其实是三兄弟组团打怪，它们偷偷搞了个‘连续暴击三联’...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后两步更绝！就像吃薯片最后两片——虽然快没了但反而最珍贵！...
2025-08-21 02:18:25 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 最后两步更绝！就像吃薯片最后两片——虽然快没了但反而最珍贵！...
2025-08-21 02:18:26 - ai_video_editor - INFO - 保存了 7 条字幕修改
2025-08-21 02:18:27 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 02:18:27 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 02:18:27 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 02:18:27 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 02:18:27 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 02:18:28 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 02:18:28 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 02:18:28 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3, 4], 'rewritten_4': [5], 'rewritten_5': [6, 7, 8], 'rewritten_6': [9]}
2025-08-21 02:18:28 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 7 条字幕
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 14:18:28
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: a283d85c75f347308593..., 剩余时间: 129600秒
2025-08-21 02:18:28 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '嘿伙计们！咱们今天要聊个超级有意思的东西——虽然现在不能剧透具体内容，但相信我，绝对让你大开眼界！首...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 02:18:33 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"126947ccf93b49b3b6cca719d9e1100f","task_id":"69e2103d5d5a4ad68fe999103c78637d","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 02:18:33 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 197136 字节
2025-08-21 02:18:33 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755713908.mp3
2025-08-21 02:18:34 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755713908.mp3
2025-08-21 02:18:34 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 49.884000秒
2025-08-21 02:18:34 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 49.884000秒
2025-08-21 02:18:34 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755713908.mp3, 时长: 49.884000秒
2025-08-21 02:18:34 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算37.35s → 实际49.28s (缩放1.320)
2025-08-21 02:18:35 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 02:18:36 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 02:18:36 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 02:18:37 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 02:18:38 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 02:18:39 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 02:18:40 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 02:18:41 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 02:18:42 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 02:18:43 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 02:18:44 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 02:18:44 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 02:18:44 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 02:18:44 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755713908.mp3
2025-08-21 02:18:44 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755713908.srt
2025-08-21 02:18:44 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 02:18:44 - ai_video_editor.ui.main_window - INFO - 标准化映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3, 4], 'rewritten_4': [5], 'rewritten_5': [6, 7, 8], 'rewritten_6': [9]}
2025-08-21 02:18:44 - ai_video_editor.ui.main_window - INFO - 使用的映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3, 4], 'rewritten_4': [5], 'rewritten_5': [6, 7, 8], 'rewritten_6': [9]}
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - INFO - 开始音画同步处理...
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - INFO - 计算字幕时长: 7 条
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_000.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_001.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_002.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_003.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_004.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_005.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_006.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_007.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_008.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - INFO - 计算视频时长: 7 条
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - INFO - 输出目录结构创建完成
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - INFO - 音频和字幕文件重命名完成: New_tts_audio_1755713908.mp3, New_tts_subtitles_1755713908.srt
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕0没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕1没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕2没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕3没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕4没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕5没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - WARNING - 字幕6没有匹配的视频片段
2025-08-21 02:18:44 - ai_video_editor.services.audio_video_sync - INFO - 音画同步处理完成
2025-08-21 02:18:44 - ai_video_editor - INFO - 已切换到音画匹配标签页 (索引: 3)
2025-08-21 02:18:46 - ai_video_editor - INFO - TTS和匹配完成: {'success': True, 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_audio_1755713908.mp3', 'subtitle_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_subtitles_1755713908.srt', 'match_data': [{'subtitle_index': 0, 'subtitle_text': '嘿伙计们！咱们今天要聊个超级有意思的东西——虽然现在不能剧透具体内容，但相信我，绝对让你大开眼界！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_000.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 3.833, 'speed_factor': 1.9165, 'time_match': False}, {'subtitle_index': 1, 'subtitle_text': '首先咱们得知道，万事万物都有个开头（就像你早上得先睁开眼才能起床对吧）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_001.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 2.0999999999999996, 'speed_factor': 1.0499999999999998, 'time_match': True}, {'subtitle_index': 2, 'subtitle_text': '然后这事儿就像叠叠乐一样，第二步必须稳稳接住第一步（不然可就全垮啦）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_002.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 2.2330000000000005, 'speed_factor': 1.1165000000000003, 'time_match': True}, {'subtitle_index': 3, 'subtitle_text': '说到第三第四步啊，简直就是相声里的捧哏和逗哏——缺了谁都不行！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_003.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 4.234, 'speed_factor': 2.117, 'time_match': False}, {'subtitle_index': 4, 'subtitle_text': '重点来了！第五步可是隐藏的王者阶段，就像泡面里的调味包——看着不起眼但至关重要！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_004.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 2.065999999999999, 'speed_factor': 1.0329999999999995, 'time_match': True}, {'subtitle_index': 5, 'subtitle_text': '六七八步其实是三兄弟组团打怪，它们偷偷搞了个‘连续暴击三联’', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_005.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 6.799999999999999, 'speed_factor': 3.3999999999999995, 'time_match': False}, {'subtitle_index': 6, 'subtitle_text': '最后两步更绝！就像吃薯片最后两片——虽然快没了但反而最珍贵！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_006.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_006\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_006\\video_001.mp4'], 'video_count': 2, 'total_video_duration': 1.2006670000000028, 'speed_factor': 0.6003335000000014, 'time_match': False}], 'match_count': 7, 'output_dir': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output', 'processing_time': 15.527404546737671}
2025-08-21 02:18:57 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 02:46:02 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 02:46:02 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 02:46:02 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 02:46:02 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 02:46:02 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 02:46:09 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 02:46:10 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 02:46:13 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 02:46:14 - ai_video_editor - INFO - 开始处理会话: session_4baede1c
2025-08-21 02:46:14 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 02:46:14 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_4baede1c
2025-08-21 02:46:14 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: 64b6536e-eb20-4de4-8ade-218b37a82a89
2025-08-21 02:46:14 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: 64b6536e-eb20-4de4-8ade-218b37a82a89
2025-08-21 02:46:16 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 02:46:16 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 02:46:16 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 02:46:16 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 02:46:36 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 5
2025-08-21 02:46:36 - ai_video_editor - INFO - 转换映射关系: 5 个对象 -> 5 个映射
2025-08-21 02:46:36 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7], 'rewritten_4': [8, 9]}
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们来聊个特别有意思的事儿——数字的奇妙旅行！从1开始数数谁都会，但您知道数字...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们来聊个特别有意思的事儿——数字的奇妙旅行！从1开始数数谁都会，但您知道数字...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 先看1和2，这俩是数学界的罗密欧与朱丽叶，既是所有数字的起点，又是相爱相杀的第一对相邻数字！...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 先看1和2，这俩是数学界的罗密欧与朱丽叶，既是所有数字的起点，又是相爱相杀的第一对相邻数字！...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接着3和4哥俩好——三角形稳定得能撑屋顶，四方形方方正正像块豆腐。要说谁更厉害？那得看您是搭...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接着3和4哥俩好——三角形稳定得能撑屋顶，四方形方方正正像块豆腐。要说谁更厉害？那得看您是搭...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 5和6这对组合可有意思了！五角星能画满整面国旗，六边形可是蜜蜂的数学老师——蜂巢告诉你什么叫...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 5和6这对组合可有意思了！五角星能画满整面国旗，六边形可是蜜蜂的数学老师——蜂巢告诉你什么叫...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后隆重请出7、8、9、10天团！7是幸运代言人，8横过来就是无限符号，9倒立变身6跟你玩魔...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后隆重请出7、8、9、10天团！7是幸运代言人，8横过来就是无限符号，9倒立变身6跟你玩魔...
2025-08-21 02:46:36 - ai_video_editor - INFO - 映射关系显示更新完成: 5 组映射
2025-08-21 02:46:42 - ai_video_editor - INFO - 字幕改写成功: 10 -> 5
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们来聊个特别有意思的事儿——数字的奇妙旅行！从1开始数数谁都会，但您知道数字...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们来聊个特别有意思的事儿——数字的奇妙旅行！从1开始数数谁都会，但您知道数字...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 先看1和2，这俩是数学界的罗密欧与朱丽叶，既是所有数字的起点，又是相爱相杀的第一对相邻数字！...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 先看1和2，这俩是数学界的罗密欧与朱丽叶，既是所有数字的起点，又是相爱相杀的第一对相邻数字！...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接着3和4哥俩好——三角形稳定得能撑屋顶，四方形方方正正像块豆腐。要说谁更厉害？那得看您是搭...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 接着3和4哥俩好——三角形稳定得能撑屋顶，四方形方方正正像块豆腐。要说谁更厉害？那得看您是搭...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 5和6这对组合可有意思了！五角星能画满整面国旗，六边形可是蜜蜂的数学老师——蜂巢告诉你什么叫...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 5和6这对组合可有意思了！五角星能画满整面国旗，六边形可是蜜蜂的数学老师——蜂巢告诉你什么叫...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后隆重请出7、8、9、10天团！7是幸运代言人，8横过来就是无限符号，9倒立变身6跟你玩魔...
2025-08-21 02:46:42 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 最后隆重请出7、8、9、10天团！7是幸运代言人，8横过来就是无限符号，9倒立变身6跟你玩魔...
2025-08-21 02:46:43 - ai_video_editor - INFO - 保存了 5 条字幕修改
2025-08-21 02:46:44 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 02:46:44 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 02:46:44 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 02:46:44 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 02:46:44 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 02:46:45 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 02:46:45 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 02:46:45 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7], 'rewritten_4': [8, 9]}
2025-08-21 02:46:45 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 5 条字幕
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 14:46:45
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: 6311e9d8d4b94e1b93e1..., 剩余时间: 129600秒
2025-08-21 02:46:45 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '嘿朋友们！今天咱们来聊个特别有意思的事儿——数字的奇妙旅行！从1开始数数谁都会，但您知道数字背......', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 02:46:50 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"dde311ac03244941ad3c87ce286057e6","task_id":"e4a9f3768dee4bbeb2e2e594526733b0","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 02:46:50 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 203904 字节
2025-08-21 02:46:50 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755715605.mp3
2025-08-21 02:46:51 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755715605.mp3
2025-08-21 02:46:51 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 51.576000秒
2025-08-21 02:46:51 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 51.576000秒
2025-08-21 02:46:51 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755715605.mp3, 时长: 51.576000秒
2025-08-21 02:46:51 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算36.90s → 实际50.98s (缩放1.381)
2025-08-21 02:46:51 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 02:46:52 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 02:46:53 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 02:46:54 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 02:46:55 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 02:46:56 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 02:46:57 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 02:46:58 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 02:46:58 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 02:47:00 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 02:47:00 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 02:47:00 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755715605.mp3
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755715605.srt
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO - 开始获取映射关系...
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO - 使用subtitle_mapping: <class 'dict'>
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO - 处理字典格式
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO - 标准化映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7], 'rewritten_4': [8, 9]}
2025-08-21 02:47:01 - ai_video_editor.ui.main_window - INFO - 使用的映射关系: {'rewritten_0': [0, 1], 'rewritten_1': [2, 3], 'rewritten_2': [4, 5], 'rewritten_3': [6, 7], 'rewritten_4': [8, 9]}
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 开始音画同步处理...
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 处理字典格式映射关系
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 标准化映射关系完成: 5 个映射
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 计算字幕时长: 5 条
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_000.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_001.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_002.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_003.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_004.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_005.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_006.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_007.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_008.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 计算视频时长: 5 条
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 输出目录结构创建完成
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 音频和字幕文件重命名完成: New_tts_audio_1755715605.mp3, New_tts_subtitles_1755715605.srt
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 字幕0没有匹配的视频片段
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 字幕1没有匹配的视频片段
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 字幕2没有匹配的视频片段
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 字幕3没有匹配的视频片段
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - WARNING - 字幕4没有匹配的视频片段
2025-08-21 02:47:01 - ai_video_editor.services.audio_video_sync - INFO - 音画同步处理完成
2025-08-21 02:47:01 - ai_video_editor - INFO - 已切换到音画匹配标签页 (索引: 3)
2025-08-21 02:47:10 - ai_video_editor - INFO - TTS和匹配完成: {'success': True, 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_audio_1755715605.mp3', 'subtitle_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_subtitles_1755715605.srt', 'match_data': [{'subtitle_index': 0, 'subtitle_text': '嘿朋友们！今天咱们来聊个特别有意思的事儿——数字的奇妙旅行！从1开始数数谁都会，但您知道数字背...', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_000.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 5.933, 'speed_factor': 2.9665, 'time_match': False}, {'subtitle_index': 1, 'subtitle_text': '先看1和2，这俩是数学界的罗密欧与朱丽叶，既是所有数字的起点，又是相爱相杀的第一对相邻数字！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_001.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 4.067, 'speed_factor': 2.0335, 'time_match': False}, {'subtitle_index': 2, 'subtitle_text': '接着3和4哥俩好——三角形稳定得能撑屋顶，四方形方方正正像块豆腐。要说谁更厉害？那得看您是搭金...', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_002.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 4.465999999999999, 'speed_factor': 2.2329999999999997, 'time_match': False}, {'subtitle_index': 3, 'subtitle_text': '5和6这对组合可有意思了！五角星能画满整面国旗，六边形可是蜜蜂的数学老师——蜂巢告诉你什么叫节...', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_003.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 4.000000000000002, 'speed_factor': 2.000000000000001, 'time_match': False}, {'subtitle_index': 4, 'subtitle_text': '最后隆重请出7、8、9、10天团！7是幸运代言人，8横过来就是无限符号，9倒立变身6跟你玩魔术...', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_004.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 4.000667, 'speed_factor': 2.0003335, 'time_match': False}], 'match_count': 5, 'output_dir': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output', 'processing_time': 15.47058367729187}
2025-08-21 02:47:18 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 10:41:34 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 10:41:35 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 10:41:35 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 10:41:35 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 10:41:35 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 10:41:43 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 10:41:45 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 10:41:48 - ai_video_editor - INFO - 选择输出目录: F:/下载中心
2025-08-21 10:41:49 - ai_video_editor - INFO - 开始处理会话: session_ccf7bad5
2025-08-21 10:41:49 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 10:41:49 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_ccf7bad5
2025-08-21 10:41:49 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: b94c66df-f598-4624-b237-e54043f10200
2025-08-21 10:41:49 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: b94c66df-f598-4624-b237-e54043f10200
2025-08-21 10:41:53 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 10:41:53 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 10:41:53 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 10:41:53 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 10:42:12 - ai_video_editor.services.deepseek_client - ERROR - JSON解析失败: Expecting ',' delimiter: line 1 column 399 (char 398)
2025-08-21 10:42:12 - ai_video_editor.services.deepseek_client - ERROR - 原始响应内容: { "rewritten_subtitles": [ {"index": 0, "text": "嘿朋友们！今天咱们来聊个特别有意思的东西——"}, {"index": 1, "text": "就是那个让你又爱又恨的...（敲黑板）注意听讲啊！"}, {"index": 2, "text": "首先咱们得知道，这玩意儿其实就跟煮泡面一样简单"}, {"index": 3, "text": "别看它现在高端大气上档次，刚出生时可是个战五渣"}, {"index": 4, "text": "举个栗子🌰——就像你家的智能音箱突然开始讲相声"}, {"index": 5, "text": "但是！重点来了啊（敲黑板啪啪啪）"}, {"index": 6, "text": "这可不是变魔术，背后藏着超级有趣的科学原理"}, {"index": 7, "text": "下次遇到这种情况，记得说："还有这种操作？！""}, {"index": 8, "text": "当然啦，要是翻车了...（耸肩）别说是跟我学的"}, {"index": 9, "text": "记住了没？没记住的赶紧点收藏，我们下...
2025-08-21 10:42:12 - ai_video_editor.services.deepseek_client - INFO - 修复后内容: { "rewritten_subtitles": [ {"index": 0, "text": "嘿朋友们！今天咱们来聊个特别有意思的东西——"}, {"index": 1, "text": "就是那个让你又爱又恨的...（敲黑板）注意听讲啊！"}, {"index": 2, "text": "首先咱们得知道，这玩意儿其实就跟煮泡面一样简单"}, {"index": 3, "text": "别看它现在高端大气上档次，刚出生时可是个战五渣"}, {"index": 4, "text": "举个栗子🌰——就像你家的智能音箱突然开始讲相声"}, {"index": 5, "text": "但是！重点来了啊（敲黑板啪啪啪）"}, {"index": 6, "text": "这可不是变魔术，背后藏着超级有趣的科学原理"}, {"index": 7, "text": "下次遇到这种情况，记得说："还有这种操作？！""}, {"index": 8, "text": "当然啦，要是翻车了...（耸肩）别说是跟我学的"}, {"index": 9, "text": "记住了没？没记住的赶紧点收藏，我们下...
2025-08-21 10:42:12 - ai_video_editor.services.deepseek_client - ERROR - JSON修复也失败: Expecting ',' delimiter: line 1 column 399 (char 398)
2025-08-21 10:42:54 - ai_video_editor - ERROR - 字幕改写失败: 响应解析失败: JSON解析失败: Expecting ',' delimiter: line 1 column 399 (char 398)，修复尝试失败: Expecting ',' delimiter: line 1 column 399 (char 398)
2025-08-21 10:42:56 - ai_video_editor - INFO - 主窗口正在关闭
2025-08-21 11:30:39 - ai_video_editor.main - INFO - 启动AI Video Editor...
2025-08-21 11:30:39 - ai_video_editor.workflow.workflow_manager - INFO - 工作流程管理器初始化完成，最大并发数: 3
2025-08-21 11:30:39 - ai_video_editor - INFO - 加载了 1 个改写风格
2025-08-21 11:30:39 - ai_video_editor - INFO - 主窗口初始化完成
2025-08-21 11:30:39 - ai_video_editor.main - INFO - 应用程序启动成功
2025-08-21 11:30:48 - ai_video_editor - INFO - 选择视频文件: F:/下载中心/项目测试/测试.mp4
2025-08-21 11:30:49 - ai_video_editor - INFO - 选择字幕文件: F:/下载中心/项目测试/测试.srt
2025-08-21 11:30:51 - ai_video_editor - INFO - 选择输出目录: F:/下载中心/项目测试
2025-08-21 11:30:52 - ai_video_editor - INFO - 开始处理会话: session_7fc1cde3
2025-08-21 11:30:52 - ai_video_editor.core.video_processor - INFO - FFmpeg可用
2025-08-21 11:30:52 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: parse_subtitle - 解析字幕文件
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: get_video_info - 获取视频信息
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: rewrite_subtitle - AI字幕改写
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: generate_audio - TTS语音合成
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: remove_original_audio - 移除原始音频
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: adjust_video_speed - 调整视频速度
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 添加任务: merge_final_video - 合并最终视频
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_manager - INFO - 开始视频编辑会话: session_7fc1cde3
2025-08-21 11:30:52 - ai_video_editor.workflow.video_editing_workflow - INFO - 开始视频编辑流程: b805b261-8cbd-4598-bb2d-1ce2f7463544
2025-08-21 11:30:52 - ai_video_editor.workflow.workflow_engine - INFO - 开始执行工作流程: b805b261-8cbd-4598-bb2d-1ce2f7463544
2025-08-21 11:30:55 - ai_video_editor - INFO - 开始AI字幕改写，风格: 科普解说风格
2025-08-21 11:30:55 - ai_video_editor.core.subtitle_processor - INFO - 成功使用 utf-8 编码读取文件
2025-08-21 11:30:55 - ai_video_editor.core.subtitle_processor - INFO - 成功解析 10 条SRT字幕
2025-08-21 11:30:55 - ai_video_editor.services.deepseek_client - INFO - 处理10条字幕，使用简化格式确保稳定传递
2025-08-21 11:31:25 - ai_video_editor.services.deepseek_client - INFO - 成功改写字幕: 10 -> 12 (尝试 1 次)
2025-08-21 11:31:25 - ai_video_editor - INFO - 转换映射关系: 12 个对象 -> 12 个映射
2025-08-21 11:31:25 - ai_video_editor - INFO - 保存映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3], 'rewritten_4': [4], 'rewritten_5': [5], 'rewritten_6': [6], 'rewritten_7': [7], 'rewritten_8': [8], 'rewritten_9': [9], 'rewritten_10': [9], 'rewritten_11': [9]}
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们要聊个特别基础但又超重要的概念...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们要聊个特别基础但又超重要的概念...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 就是数字1到10！别看它们简单，可是所有数学的祖宗级大佬...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 就是数字1到10！别看它们简单，可是所有数学的祖宗级大佬...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 先看1——孤独的王者，所有数字里最特立独行的一个...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 先看1——孤独的王者，所有数字里最特立独行的一个...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2就浪漫多了，成双成对，还是最小的质数（质数就是除了1和它自己不能被别的数整除）...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2就浪漫多了，成双成对，还是最小的质数（质数就是除了1和它自己不能被别的数整除）...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 3可是最稳定的结构，三角形听说过吧？三脚架为啥不倒？就因为它！...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 3可是最稳定的结构，三角形听说过吧？三脚架为啥不倒？就因为它！...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 4像个小桌子，四平八稳的。四季/四方/四海——全跟它有关...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 4像个小桌子，四平八稳的。四季/四方/四海——全跟它有关...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 5！我们的手指头数量！人类最早的计算器就是它...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 5！我们的手指头数量！人类最早的计算器就是它...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 6是完美数哦（自己的一半+三分之一+六分之一刚好等于自己，神奇吧？）...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 6是完美数哦（自己的一半+三分之一+六分之一刚好等于自己，神奇吧？）...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 7...幸运数字！一周七天，彩虹七色，连哈利波特都逃不开这个数...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 7...幸运数字！一周七天，彩虹七色，连哈利波特都逃不开这个数...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 8横过来就是无穷大！而且中国人超爱它——发发发！...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 8横过来就是无穷大！而且中国人超爱它——发发发！...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第11行的新字幕已修改: [10] 9作为最大个位数，可是有九五之尊的说法。乘以任何数最后都会回归自己（9×5=45→4+5=...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第11行的新字幕已修改: [10] 9作为最大个位数，可是有九五之尊的说法。乘以任何数最后都会回归自己（9×5=45→4+5=...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第12行的新字幕已修改: [11] 最后是10！咱们的十进制基础！数完手指就该它登场啦！...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射表格第12行的新字幕已修改: [11] 最后是10！咱们的十进制基础！数完手指就该它登场啦！...
2025-08-21 11:31:25 - ai_video_editor - INFO - 映射关系显示更新完成: 12 组映射
2025-08-21 11:31:28 - ai_video_editor - INFO - 字幕改写成功: 10 -> 12
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们要聊个特别基础但又超重要的概念...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第1行的新字幕已修改: [0] 嘿朋友们！今天咱们要聊个特别基础但又超重要的概念...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 就是数字1到10！别看它们简单，可是所有数学的祖宗级大佬...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第2行的新字幕已修改: [1] 就是数字1到10！别看它们简单，可是所有数学的祖宗级大佬...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 先看1——孤独的王者，所有数字里最特立独行的一个...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第3行的新字幕已修改: [2] 先看1——孤独的王者，所有数字里最特立独行的一个...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2就浪漫多了，成双成对，还是最小的质数（质数就是除了1和它自己不能被别的数整除）...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第4行的新字幕已修改: [3] 2就浪漫多了，成双成对，还是最小的质数（质数就是除了1和它自己不能被别的数整除）...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 3可是最稳定的结构，三角形听说过吧？三脚架为啥不倒？就因为它！...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第5行的新字幕已修改: [4] 3可是最稳定的结构，三角形听说过吧？三脚架为啥不倒？就因为它！...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 4像个小桌子，四平八稳的。四季/四方/四海——全跟它有关...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第6行的新字幕已修改: [5] 4像个小桌子，四平八稳的。四季/四方/四海——全跟它有关...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 5！我们的手指头数量！人类最早的计算器就是它...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第7行的新字幕已修改: [6] 5！我们的手指头数量！人类最早的计算器就是它...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 6是完美数哦（自己的一半+三分之一+六分之一刚好等于自己，神奇吧？）...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第8行的新字幕已修改: [7] 6是完美数哦（自己的一半+三分之一+六分之一刚好等于自己，神奇吧？）...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 7...幸运数字！一周七天，彩虹七色，连哈利波特都逃不开这个数...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第9行的新字幕已修改: [8] 7...幸运数字！一周七天，彩虹七色，连哈利波特都逃不开这个数...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 8横过来就是无穷大！而且中国人超爱它——发发发！...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第10行的新字幕已修改: [9] 8横过来就是无穷大！而且中国人超爱它——发发发！...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第11行的新字幕已修改: [10] 9作为最大个位数，可是有九五之尊的说法。乘以任何数最后都会回归自己（9×5=45→4+5=...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第11行的新字幕已修改: [10] 9作为最大个位数，可是有九五之尊的说法。乘以任何数最后都会回归自己（9×5=45→4+5=...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第12行的新字幕已修改: [11] 最后是10！咱们的十进制基础！数完手指就该它登场啦！...
2025-08-21 11:31:29 - ai_video_editor - INFO - 映射表格第12行的新字幕已修改: [11] 最后是10！咱们的十进制基础！数完手指就该它登场啦！...
2025-08-21 11:31:30 - ai_video_editor - INFO - 保存了 12 条字幕修改
2025-08-21 11:31:35 - ai_video_editor - INFO - 开始TTS和自动匹配检查...
2025-08-21 11:31:35 - ai_video_editor - INFO - video_file_path: F:/下载中心/项目测试/测试.mp4
2025-08-21 11:31:35 - ai_video_editor - INFO - current_rewritten_subtitles: True
2025-08-21 11:31:35 - ai_video_editor - INFO - current_subtitles: True
2025-08-21 11:31:35 - ai_video_editor - INFO - 原字幕数量: 10
2025-08-21 11:31:36 - ai_video_editor - INFO - 开始获取映射关系...
2025-08-21 11:31:36 - ai_video_editor - INFO - 找到current_mappings属性: <class 'dict'>
2025-08-21 11:31:36 - ai_video_editor - INFO - 使用保存的映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3], 'rewritten_4': [4], 'rewritten_5': [5], 'rewritten_6': [6], 'rewritten_7': [7], 'rewritten_8': [8], 'rewritten_9': [9], 'rewritten_10': [9], 'rewritten_11': [9]}
2025-08-21 11:31:36 - ai_video_editor - INFO - 开始TTS语音合成和音画匹配: 12 条字幕
2025-08-21 11:31:36 - ai_video_editor.services.aliyun_tts_client - INFO - 阿里云TTS客户端初始化完成
2025-08-21 11:31:36 - ai_video_editor.services.aliyun_token_manager - INFO - 阿里云Token管理器初始化完成
2025-08-21 11:31:36 - ai_video_editor.services.aliyun_token_manager - INFO - 开始刷新阿里云TTS Token...
2025-08-21 11:31:37 - ai_video_editor.services.aliyun_token_manager - INFO - Token刷新成功，过期时间: 2025-08-22 23:31:36
2025-08-21 11:31:37 - ai_video_editor.services.aliyun_token_manager - INFO - Token自动刷新线程已启动
2025-08-21 11:31:37 - ai_video_editor.services.aliyun_tts_client - INFO - 使用Token: c1609529b46f48f19600..., 剩余时间: 129599秒
2025-08-21 11:31:37 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成参数: {'text': '嘿朋友们！今天咱们要聊个特别基础但又超重要的概念就是数字1到10！别看它们简单，可是所有数学的祖宗级...', 'voice': 'zhixiaobai', 'aformat': 'mp3', 'sample_rate': 16000, 'volume': 50, 'speech_rate': 0, 'pitch_rate': 0, 'appkey': 'avn9JKysKy...'}
2025-08-21 11:31:44 - ai_video_editor.services.aliyun_tts_client - INFO - 合成完成: {"header":{"namespace":"SpeechSynthesizer","name":"SynthesisCompleted","status":20000000,"message_id":"c782a9a7bf2640018028088bd427c8e3","task_id":"41f7c35ce64f43d1bd278e187d39f38e","status_text":"Gateway:SUCCESS:Success."}}
2025-08-21 11:31:44 - ai_video_editor.services.aliyun_tts_client - INFO - TTS合成完成，音频数据大小: 276480 字节
2025-08-21 11:31:44 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件写入完成: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755747096.mp3
2025-08-21 11:31:44 - ai_video_editor.services.aliyun_tts_client - INFO - 音频文件检测通过: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755747096.mp3
2025-08-21 11:31:46 - ai_video_editor.services.aliyun_tts_client - INFO - FFprobe获取音频时长: 69.720000秒
2025-08-21 11:31:46 - ai_video_editor.services.aliyun_tts_client - INFO - 音频时长获取成功: 69.720000秒
2025-08-21 11:31:46 - ai_video_editor.services.aliyun_tts_client - INFO - 语音合成成功，输出文件: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755747096.mp3, 时长: 69.720000秒
2025-08-21 11:31:46 - ai_video_editor.ui.main_window - INFO - 重新计算字幕时长: 估算53.85s → 实际69.12s (缩放1.284)
2025-08-21 11:31:46 - ai_video_editor.services.video_processor - INFO - 创建视频片段 0: 0.00s-3.83s (3.83s) -> segment_000.mp4
2025-08-21 11:31:47 - ai_video_editor.services.video_processor - INFO - 创建视频片段 1: 3.83s-5.93s (2.10s) -> segment_001.mp4
2025-08-21 11:31:48 - ai_video_editor.services.video_processor - INFO - 创建视频片段 2: 5.93s-8.17s (2.23s) -> segment_002.mp4
2025-08-21 11:31:48 - ai_video_editor.services.video_processor - INFO - 创建视频片段 3: 8.17s-10.00s (1.83s) -> segment_003.mp4
2025-08-21 11:31:49 - ai_video_editor.services.video_processor - INFO - 创建视频片段 4: 10.00s-12.40s (2.40s) -> segment_004.mp4
2025-08-21 11:31:50 - ai_video_editor.services.video_processor - INFO - 创建视频片段 5: 12.40s-14.47s (2.07s) -> segment_005.mp4
2025-08-21 11:31:51 - ai_video_editor.services.video_processor - INFO - 创建视频片段 6: 14.47s-16.33s (1.87s) -> segment_006.mp4
2025-08-21 11:31:52 - ai_video_editor.services.video_processor - INFO - 创建视频片段 7: 16.33s-18.47s (2.13s) -> segment_007.mp4
2025-08-21 11:31:53 - ai_video_editor.services.video_processor - INFO - 创建视频片段 8: 18.47s-21.27s (2.80s) -> segment_008.mp4
2025-08-21 11:31:54 - ai_video_editor.services.video_processor - INFO - 创建视频片段 9: 21.27s-22.47s (1.20s) -> segment_009.mp4
2025-08-21 11:31:55 - ai_video_editor.services.video_processor - INFO - 视频分割完成，生成 10 个片段，按字幕序号编码(0,1,2...n)
2025-08-21 11:31:55 - ai_video_editor.services.video_processor - INFO - 分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO - 音画同步文件路径检查:
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO -   audio_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_audio_1755747096.mp3
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO -   subtitle_file: C:\Users\<USER>\Desktop\AI_Video_Output\tts_subtitles_1755747096.srt
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO -   video_segments_dir: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO - 开始获取映射关系...
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO - 使用subtitle_mapping: <class 'dict'>
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO - 处理字典格式
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO - 标准化映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3], 'rewritten_4': [4], 'rewritten_5': [5], 'rewritten_6': [6], 'rewritten_7': [7], 'rewritten_8': [8], 'rewritten_9': [9], 'rewritten_10': [9], 'rewritten_11': [9]}
2025-08-21 11:31:55 - ai_video_editor.ui.main_window - INFO - 使用的映射关系: {'rewritten_0': [0], 'rewritten_1': [1], 'rewritten_2': [2], 'rewritten_3': [3], 'rewritten_4': [4], 'rewritten_5': [5], 'rewritten_6': [6], 'rewritten_7': [7], 'rewritten_8': [8], 'rewritten_9': [9], 'rewritten_10': [9], 'rewritten_11': [9]}
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 开始音画同步处理...
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 处理字典格式映射关系
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 标准化映射关系完成: 12 个映射
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 计算字幕时长: 12 条
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_000.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_001.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_002.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_003.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_004.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_005.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_006.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_007.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_008.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 视频片段不存在: C:\Users\<USER>\Desktop\AI_Video_Output\video_segments\segment_009.mp4
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 计算视频时长: 12 条
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 输出目录结构创建完成
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 音频和字幕文件重命名完成: New_tts_audio_1755747096.mp3, New_tts_subtitles_1755747096.srt
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕0没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕1没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕2没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕3没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕4没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕5没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕6没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕7没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕8没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕9没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕10没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - WARNING - 字幕11没有匹配的视频片段
2025-08-21 11:31:55 - ai_video_editor.services.audio_video_sync - INFO - 音画同步处理完成
2025-08-21 11:31:55 - ai_video_editor - INFO - 已切换到音画匹配标签页 (索引: 3)
2025-08-21 11:31:57 - ai_video_editor - INFO - TTS和匹配完成: {'success': True, 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_audio_1755747096.mp3', 'subtitle_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\tts_subtitles_1755747096.srt', 'match_data': [{'subtitle_index': 0, 'subtitle_text': '嘿朋友们！今天咱们要聊个特别基础但又超重要的概念', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_000.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_000\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 3.833, 'speed_factor': 1.9165, 'time_match': False}, {'subtitle_index': 1, 'subtitle_text': '就是数字1到10！别看它们简单，可是所有数学的祖宗级大佬', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_001.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_001\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 2.0999999999999996, 'speed_factor': 1.0499999999999998, 'time_match': True}, {'subtitle_index': 2, 'subtitle_text': '先看1——孤独的王者，所有数字里最特立独行的一个', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_002.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_002\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 2.2330000000000005, 'speed_factor': 1.1165000000000003, 'time_match': True}, {'subtitle_index': 3, 'subtitle_text': '2就浪漫多了，成双成对，还是最小的质数（质数就是除了1和它自己不能被别的数整除）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_003.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_002.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_003\\video_003.mp4'], 'video_count': 4, 'total_video_duration': 1.8339999999999996, 'speed_factor': 0.9169999999999998, 'time_match': True}, {'subtitle_index': 4, 'subtitle_text': '3可是最稳定的结构，三角形听说过吧？三脚架为啥不倒？就因为它！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_004.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_004\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 2.4000000000000004, 'speed_factor': 1.2000000000000002, 'time_match': True}, {'subtitle_index': 5, 'subtitle_text': '4像个小桌子，四平八稳的。四季/四方/四海——全跟它有关', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_005.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_001.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_005\\video_002.mp4'], 'video_count': 3, 'total_video_duration': 2.065999999999999, 'speed_factor': 1.0329999999999995, 'time_match': True}, {'subtitle_index': 6, 'subtitle_text': '5！我们的手指头数量！人类最早的计算器就是它', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_006.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_006\\video_000.mp4', 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_006\\video_001.mp4'], 'video_count': 2, 'total_video_duration': 1.866999999999999, 'speed_factor': 0.9334999999999996, 'time_match': True}, {'subtitle_index': 7, 'subtitle_text': '6是完美数哦（自己的一半+三分之一+六分之一刚好等于自己，神奇吧？）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_007.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_007\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 2.1330000000000027, 'speed_factor': 1.0665000000000013, 'time_match': True}, {'subtitle_index': 8, 'subtitle_text': '7...幸运数字！一周七天，彩虹七色，连哈利波特都逃不开这个数', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_008.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_008\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 2.799999999999997, 'speed_factor': 1.3999999999999986, 'time_match': False}, {'subtitle_index': 9, 'subtitle_text': '8横过来就是无穷大！而且中国人超爱它——发发发！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_009.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_009\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 1.2006670000000028, 'speed_factor': 0.6003335000000014, 'time_match': False}, {'subtitle_index': 10, 'subtitle_text': '9作为最大个位数，可是有九五之尊的说法。乘以任何数最后都会回归自己（9×5=45→4+5=9）', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_010.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_010\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 1.2006670000000028, 'speed_factor': 0.6003335000000014, 'time_match': False}, {'subtitle_index': 11, 'subtitle_text': '最后是10！咱们的十进制基础！数完手指就该它登场啦！', 'audio_file': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\audio_segments\\audio_011.mp3', 'audio_duration': 2.0, 'video_files': ['C:\\Users\\<USER>\\Desktop\\AI_Video_Output\\match_results\\matched_video_segments\\segment_011\\video_000.mp4'], 'video_count': 1, 'total_video_duration': 1.2006670000000028, 'speed_factor': 0.6003335000000014, 'time_match': False}], 'match_count': 12, 'output_dir': 'C:\\Users\\<USER>\\Desktop\\AI_Video_Output', 'processing_time': 18.985095262527466}
2025-08-21 11:32:08 - ai_video_editor - INFO - 主窗口正在关闭
