#!/usr/bin/env python3
"""
应用启动脚本

启动AI智能视频剪辑软件
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("请使用Python 3.8或更高版本")
        return False
    else:
        print(f"✅ Python版本: {sys.version}")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
    else:
        print("⚠️  未检测到虚拟环境")
    
    # 检查关键依赖
    missing_deps = []
    
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
    except ImportError:
        missing_deps.append("PyQt6")
        print("❌ PyQt6 未安装")
    
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        missing_deps.append("requests")
        print("❌ requests 未安装")
    
    try:
        import aiohttp
        print("✅ aiohttp 已安装")
    except ImportError:
        missing_deps.append("aiohttp")
        print("❌ aiohttp 未安装")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True


def main():
    """主函数"""
    print("🚀 AI智能视频剪辑软件")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    print("\n🎬 启动应用...")
    
    try:
        # 导入并运行主程序
        from ai_video_editor.main import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有依赖都已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
