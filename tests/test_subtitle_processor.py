"""
字幕处理器测试

测试SRT字幕文件的解析、验证、导出功能
"""

import os
import tempfile
import pytest
from pathlib import Path

from ai_video_editor.core.subtitle_processor import SubtitleProcessor
from ai_video_editor.common.models import SubtitleCollection, SubtitleItem, TimeCode, SubtitleFormat


class TestSubtitleProcessor:
    """字幕处理器测试类"""
    
    @pytest.fixture
    def processor(self):
        """创建字幕处理器实例"""
        return SubtitleProcessor()
    
    @pytest.fixture
    def sample_srt_content(self):
        """示例SRT内容"""
        return """1
00:00:01,000 --> 00:00:03,500
这是第一条字幕

2
00:00:04,000 --> 00:00:06,500
这是第二条字幕

3
00:00:07,000 --> 00:00:10,000
这是第三条字幕，内容比较长一些
"""
    
    @pytest.fixture
    def sample_srt_file(self, sample_srt_content):
        """创建临时SRT文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
            f.write(sample_srt_content)
            temp_file = f.name
        
        yield temp_file
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    def test_parse_srt_file_success(self, processor, sample_srt_file):
        """测试成功解析SRT文件"""
        result, subtitles = processor.parse_subtitle_file(sample_srt_file)
        
        assert result.success
        assert subtitles is not None
        assert subtitles.total_count == 3
        assert subtitles.format == SubtitleFormat.SRT
        
        # 检查第一条字幕
        first_subtitle = subtitles.items[0]
        assert first_subtitle.index == 0
        assert first_subtitle.text == "这是第一条字幕"
        assert first_subtitle.start_time.to_seconds() == 1.0
        assert first_subtitle.end_time.to_seconds() == 3.5
    
    def test_parse_nonexistent_file(self, processor):
        """测试解析不存在的文件"""
        result, subtitles = processor.parse_subtitle_file("nonexistent.srt")
        
        assert not result.success
        assert subtitles is None
        assert result.error_code == "FILE_NOT_FOUND"
    
    def test_parse_unsupported_format(self, processor):
        """测试不支持的文件格式"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file = f.name
        
        try:
            result, subtitles = processor.parse_subtitle_file(temp_file)
            
            assert not result.success
            assert subtitles is None
            assert result.error_code == "UNSUPPORTED_FORMAT"
        finally:
            os.unlink(temp_file)
    
    def test_export_srt_file(self, processor, sample_srt_file):
        """测试导出SRT文件"""
        # 先解析文件
        result, subtitles = processor.parse_subtitle_file(sample_srt_file)
        assert result.success
        
        # 导出到新文件
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            output_file = f.name
        
        try:
            export_result = processor.export_subtitle_file(subtitles, output_file)
            
            assert export_result.success
            assert os.path.exists(output_file)
            
            # 验证导出的文件可以重新解析
            re_result, re_subtitles = processor.parse_subtitle_file(output_file)
            assert re_result.success
            assert re_subtitles.total_count == subtitles.total_count
            
        finally:
            if os.path.exists(output_file):
                os.unlink(output_file)
    
    def test_subtitle_validation(self, processor):
        """测试字幕验证"""
        # 创建有问题的字幕
        invalid_subtitle = SubtitleItem(
            index=0,
            start_time=TimeCode.from_string("00:00:03,000"),
            end_time=TimeCode.from_string("00:00:01,000"),  # 结束时间早于开始时间
            text="测试字幕"
        )
        
        errors = invalid_subtitle.validate()
        assert len(errors) > 0
        assert "开始时间不能大于或等于结束时间" in errors
    
    def test_merge_subtitles(self, processor, sample_srt_file):
        """测试字幕合并功能"""
        # 解析字幕
        result, subtitles = processor.parse_subtitle_file(sample_srt_file)
        assert result.success
        
        # 合并字幕
        merged_subtitles = processor.merge_subtitles(subtitles, merge_range=2)
        
        # 合并后的字幕数量应该减少
        assert merged_subtitles.total_count <= subtitles.total_count
    
    def test_time_code_conversion(self):
        """测试时间码转换"""
        # 测试从字符串创建时间码
        time_code = TimeCode.from_string("01:23:45,678")
        assert time_code.hours == 1
        assert time_code.minutes == 23
        assert time_code.seconds == 45
        assert time_code.milliseconds == 678
        
        # 测试转换为秒数
        total_seconds = time_code.to_seconds()
        expected_seconds = 1 * 3600 + 23 * 60 + 45 + 0.678
        assert abs(total_seconds - expected_seconds) < 0.001
        
        # 测试从秒数创建时间码
        time_code2 = TimeCode.from_seconds(total_seconds)
        assert time_code2.hours == 1
        assert time_code2.minutes == 23
        assert time_code2.seconds == 45
        assert abs(time_code2.milliseconds - 678) <= 1  # 允许1ms误差
    
    def test_invalid_srt_content(self, processor):
        """测试无效的SRT内容"""
        invalid_content = "这不是有效的SRT格式内容"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
            f.write(invalid_content)
            temp_file = f.name
        
        try:
            result, subtitles = processor.parse_subtitle_file(temp_file)
            
            assert not result.success
            assert subtitles is None
            assert result.error_code == "NO_SUBTITLE_ENTRIES"
        finally:
            os.unlink(temp_file)
    
    def test_empty_subtitle_file(self, processor):
        """测试空字幕文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
            f.write("")  # 空文件
            temp_file = f.name
        
        try:
            result, subtitles = processor.parse_subtitle_file(temp_file)
            
            assert not result.success
            assert subtitles is None
        finally:
            os.unlink(temp_file)
    
    def test_subtitle_collection_properties(self, processor, sample_srt_file):
        """测试字幕集合的属性"""
        result, subtitles = processor.parse_subtitle_file(sample_srt_file)
        assert result.success
        
        # 测试总数
        assert subtitles.total_count == 3
        
        # 测试总时长
        assert subtitles.total_duration > 0
        
        # 测试总字数
        assert subtitles.total_word_count > 0
        
        # 测试时间戳更新
        original_time = subtitles.updated_at
        subtitles.update_timestamp()
        assert subtitles.updated_at > original_time


def test_subtitle_processor_integration():
    """字幕处理器集成测试"""
    processor = SubtitleProcessor()
    
    # 创建测试SRT内容
    test_content = """1
00:00:00,500 --> 00:00:02,000
欢迎使用AI视频编辑器

2
00:00:02,500 --> 00:00:05,000
这是一个智能的视频剪辑工具

3
00:00:05,500 --> 00:00:08,000
支持字幕改写和语音合成
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        input_file = f.name
    
    try:
        # 解析字幕
        result, subtitles = processor.parse_subtitle_file(input_file)
        assert result.success
        assert subtitles.total_count == 3
        
        # 验证字幕
        errors = subtitles.validate()
        assert len(errors) == 0  # 应该没有验证错误
        
        # 导出字幕
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            output_file = f.name
        
        export_result = processor.export_subtitle_file(subtitles, output_file)
        assert export_result.success
        
        # 验证导出的文件
        assert os.path.exists(output_file)
        
        # 重新解析导出的文件
        re_result, re_subtitles = processor.parse_subtitle_file(output_file)
        assert re_result.success
        assert re_subtitles.total_count == subtitles.total_count
        
        # 清理
        os.unlink(output_file)
        
    finally:
        os.unlink(input_file)


if __name__ == "__main__":
    # 运行简单测试
    test_subtitle_processor_integration()
    print("✅ 字幕处理器集成测试通过！")
