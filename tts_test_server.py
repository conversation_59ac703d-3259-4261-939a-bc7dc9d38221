#!/usr/bin/env python3
"""
TTS测试服务器

提供简单的Web界面测试TTS功能
"""

import sys
import os
import asyncio
import tempfile
import json
from pathlib import Path
from flask import Flask, request, jsonify, render_template_string, send_file

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from ai_video_editor.services.aliyun_tts_client import AliyunTTSClient
    from config.config_manager import ConfigManager
    print("✅ TTS模块导入成功")
except ImportError as e:
    print(f"❌ TTS模块导入失败: {e}")
    sys.exit(1)

app = Flask(__name__)

# 全局TTS客户端
tts_client = None
temp_dir = None

def init_tts_client():
    """初始化TTS客户端"""
    global tts_client, temp_dir
    try:
        config_manager = ConfigManager()
        api_config = config_manager.get_api_config()
        tts_config = api_config.get("aliyun_tts", {})
        
        tts_client = AliyunTTSClient(tts_config)
        temp_dir = tempfile.mkdtemp(prefix="tts_test_server_")
        
        print(f"✅ TTS客户端初始化成功")
        print(f"✅ 临时目录: {temp_dir}")
        return True
    except Exception as e:
        print(f"❌ TTS客户端初始化失败: {e}")
        return False

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>TTS测试服务</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        textarea { height: 100px; resize: vertical; }
        button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .audio-player { margin-top: 15px; }
        .config-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .loading { display: none; text-align: center; margin: 20px 0; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 TTS语音合成测试服务</h1>
        
        <div class="config-info">
            <h3>📋 当前配置</h3>
            <p><strong>语音:</strong> {{ config.voice }}</p>
            <p><strong>格式:</strong> {{ config.format }}</p>
            <p><strong>采样率:</strong> {{ config.sample_rate }}Hz</p>
            <p><strong>状态:</strong> <span style="color: green;">✅ 已连接</span></p>
        </div>
        
        <form id="ttsForm">
            <div class="form-group">
                <label for="text">要合成的文本:</label>
                <textarea id="text" name="text" placeholder="请输入要合成语音的文本..." required>人工智能技术正在快速发展，它在各个领域都有广泛的应用。</textarea>
            </div>
            
            <div class="form-group">
                <label for="voice">语音类型:</label>
                <select id="voice" name="voice">
                    <option value="longxiaochun_v2">龙小春（女声）</option>
                    <option value="longwan">龙婉（女声）</option>
                    <option value="longxiaoxia">龙小夏（女声）</option>
                    <option value="longxiaobai">龙小白（男声）</option>
                </select>
            </div>
            
            <button type="submit">🎵 开始语音合成</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在进行语音合成，请稍候...</p>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const text = formData.get('text');
            const voice = formData.get('voice');
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').innerHTML = '';
            
            try {
                const response = await fetch('/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text, voice: voice })
                });
                
                const result = await response.json();
                
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                
                if (result.success) {
                    document.getElementById('result').innerHTML = `
                        <div class="result success">
                            <h3>✅ 语音合成成功</h3>
                            <p><strong>文本长度:</strong> ${result.text_length} 字符</p>
                            <p><strong>音频时长:</strong> ${result.duration.toFixed(3)} 秒</p>
                            <p><strong>文件大小:</strong> ${result.file_size} 字节</p>
                            <div class="audio-player">
                                <audio controls>
                                    <source src="/audio/${result.filename}" type="audio/wav">
                                    您的浏览器不支持音频播放。
                                </audio>
                            </div>
                            <p><a href="/audio/${result.filename}" download>📥 下载音频文件</a></p>
                        </div>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <div class="result error">
                            <h3>❌ 语音合成失败</h3>
                            <p><strong>错误信息:</strong> ${result.message}</p>
                            <p><strong>错误代码:</strong> ${result.error_code || 'UNKNOWN'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('result').innerHTML = `
                    <div class="result error">
                        <h3>❌ 请求失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    if not tts_client:
        return "TTS客户端未初始化", 500
    
    config = {
        'voice': tts_client.config.get('voice', 'longxiaochun_v2'),
        'format': tts_client.config.get('format', 'wav'),
        'sample_rate': tts_client.config.get('sample_rate', 24000)
    }
    
    return render_template_string(HTML_TEMPLATE, config=config)

@app.route('/synthesize', methods=['POST'])
def synthesize():
    """语音合成API"""
    if not tts_client:
        return jsonify({
            'success': False,
            'message': 'TTS客户端未初始化',
            'error_code': 'CLIENT_NOT_INITIALIZED'
        }), 500
    
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        voice = data.get('voice', 'longxiaochun_v2')
        
        if not text:
            return jsonify({
                'success': False,
                'message': '文本内容不能为空',
                'error_code': 'EMPTY_TEXT'
            }), 400
        
        # 生成输出文件路径
        import time
        timestamp = int(time.time())
        output_file = os.path.join(temp_dir, f"tts_{timestamp}.wav")
        
        # 临时修改语音配置
        original_voice = tts_client.config.get('voice')
        tts_client.config['voice'] = voice
        
        try:
            # 执行TTS合成
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(
                tts_client.synthesize_text(text, output_file)
            )
            loop.close()
            
            if result.success:
                # 获取文件信息
                file_size = os.path.getsize(output_file)
                filename = os.path.basename(output_file)
                
                return jsonify({
                    'success': True,
                    'message': '语音合成成功',
                    'text_length': len(text),
                    'duration': result.duration,
                    'file_size': file_size,
                    'filename': filename
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result.message,
                    'error_code': result.error_code
                }), 500
                
        finally:
            # 恢复原始语音配置
            if original_voice:
                tts_client.config['voice'] = original_voice
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}',
            'error_code': 'SERVER_ERROR'
        }), 500

@app.route('/audio/<filename>')
def serve_audio(filename):
    """提供音频文件"""
    try:
        file_path = os.path.join(temp_dir, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=False, mimetype='audio/wav')
        else:
            return "文件不存在", 404
    except Exception as e:
        return f"文件服务错误: {str(e)}", 500

@app.route('/status')
def status():
    """服务状态"""
    return jsonify({
        'status': 'running',
        'tts_client': tts_client is not None,
        'temp_dir': temp_dir
    })

if __name__ == '__main__':
    print("🚀 启动TTS测试服务器...")
    
    if not init_tts_client():
        print("❌ TTS客户端初始化失败，退出")
        sys.exit(1)
    
    print("✅ TTS测试服务器启动成功")
    print("🌐 访问地址: http://localhost:5000")
    print("📋 状态检查: http://localhost:5000/status")
    print("⏹️  按 Ctrl+C 停止服务")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n⏹️  服务器已停止")
    finally:
        # 清理临时文件
        if temp_dir and os.path.exists(temp_dir):
            import shutil
            shutil.rmtree(temp_dir)
            print(f"✅ 清理临时目录: {temp_dir}")
