"""
Configuration Manager - 配置管理器

负责管理应用程序的所有配置，包括API配置、应用设置、Prompt风格等
"""

import os
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from ai_video_editor.common.models import BaseResult


class ConfigManager:
    """配置管理器核心类"""
    
    def __init__(self, base_dir: str = None):
        # 获取项目根目录
        if base_dir:
            self.base_dir = base_dir
        else:
            # 从config/config_manager.py 向上一级到项目根目录
            current_file = os.path.abspath(__file__)
            self.base_dir = os.path.dirname(os.path.dirname(current_file))

        self.config_dir = os.path.join(self.base_dir, "config")
        self.prompts_dir = os.path.join(self.config_dir, "prompts")

        # 初始化日志
        import logging
        self.logger = logging.getLogger(__name__)

        # 加载环境变量
        self._load_env_file()

        # 配置缓存
        self._api_config = None
        self._app_settings = None
        self._prompt_styles = {}

        self._ensure_directories()
        self._load_default_configs()

    def _load_env_file(self):
        """加载.env文件"""
        try:
            from dotenv import load_dotenv
            env_file = os.path.join(self.base_dir, ".env")
            if os.path.exists(env_file):
                load_dotenv(env_file)
                self.logger.info(f"已加载环境变量文件: {env_file}")
            else:
                self.logger.warning(f".env文件不存在: {env_file}")
        except ImportError:
            self.logger.warning("python-dotenv未安装，无法加载.env文件")
    
    def _ensure_directories(self):
        """确保配置目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.prompts_dir, exist_ok=True)
    
    def _load_default_configs(self):
        """加载默认配置"""
        self._load_api_config()
        self._load_app_settings()
        self._scan_prompt_styles()
    
    # API配置管理
    def _load_api_config(self):
        """加载API配置"""
        config_file = os.path.join(self.config_dir, "api_config.json")

        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                self._api_config = json.load(f)
        else:
            # 创建默认API配置
            self._api_config = self._get_default_api_config()

        # 总是从环境变量更新API密钥（优先级高于配置文件）
        self._update_api_config_from_env()

        # 保存更新后的配置
        self.save_api_config(self._api_config)

    def _update_api_config_from_env(self):
        """从环境变量更新API配置"""
        if not self._api_config:
            return

        # 更新DeepSeek配置
        if "deepseek" in self._api_config:
            deepseek_key = os.getenv("DEEPSEEK_API_KEY")
            if deepseek_key:
                self._api_config["deepseek"]["api_key"] = deepseek_key
                self.logger.info("从环境变量更新DeepSeek API密钥")

        # 更新阿里云TTS配置
        if "aliyun_tts" in self._api_config:
            aliyun_ak_id = os.getenv("ALIYUN_ACCESS_KEY_ID") or os.getenv("ALIYUN_AK_ID")
            aliyun_ak_secret = os.getenv("ALIYUN_ACCESS_KEY_SECRET") or os.getenv("ALIYUN_AK_SECRET")
            aliyun_appkey = os.getenv("ALIYUN_TTS_APPKEY")

            if aliyun_ak_id:
                self._api_config["aliyun_tts"]["access_key_id"] = aliyun_ak_id
                self.logger.info("从环境变量更新阿里云Access Key ID")

            if aliyun_ak_secret:
                self._api_config["aliyun_tts"]["access_key_secret"] = aliyun_ak_secret
                self.logger.info("从环境变量更新阿里云Access Key Secret")

            if aliyun_appkey:
                self._api_config["aliyun_tts"]["appkey"] = aliyun_appkey
                self.logger.info("从环境变量更新阿里云TTS AppKey")
    
    def _get_default_api_config(self) -> Dict:
        """获取默认API配置"""
        return {
            "deepseek": {
                "api_key": os.getenv("DEEPSEEK_API_KEY", ""),
                "base_url": "https://api.deepseek.com",
                "model": "deepseek-chat",
                "max_tokens": 4000,
                "temperature": 0.7,
                "max_retries": 3,
                "timeout": 60
            },
            "aliyun_tts": {
                "access_key_id": os.getenv("ALIYUN_AK_ID", ""),
                "access_key_secret": os.getenv("ALIYUN_AK_SECRET", ""),
                "appkey": os.getenv("ALIYUN_TTS_APPKEY", ""),
                "url": "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1",
                "voice": "longxiaochun_v2",
                "format": "wav",
                "sample_rate": 24000,
                "volume": 50,
                "speech_rate": 0,
                "pitch_rate": 0
            }
        }
    
    def get_api_config(self, service: str = None) -> Dict:
        """获取API配置"""
        if service:
            return self._api_config.get(service, {})
        return self._api_config
    
    def save_api_config(self, config: Dict):
        """保存API配置"""
        config_file = os.path.join(self.config_dir, "api_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        self._api_config = config
    
    def update_api_config(self, service: str, updates: Dict):
        """更新特定服务的API配置"""
        if service in self._api_config:
            self._api_config[service].update(updates)
            self.save_api_config(self._api_config)
    
    # 应用设置管理
    def _load_app_settings(self):
        """加载应用设置"""
        settings_file = os.path.join(self.config_dir, "app_settings.json")
        
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                self._app_settings = json.load(f)
        else:
            self._app_settings = self._get_default_app_settings()
            self.save_app_settings(self._app_settings)
    
    def _get_default_app_settings(self) -> Dict:
        """获取默认应用设置"""
        return {
            "general": {
                "temp_dir": "temp",
                "max_file_size": 500 * 1024 * 1024,  # 500MB
                "auto_cleanup": True,
                "cleanup_interval": 24  # 小时
            },
            "video": {
                "supported_formats": [".mp4", ".avi", ".mov", ".mkv"],
                "default_codec": "libx264",
                "default_quality": "medium",
                "max_resolution": "1920x1080"
            },
            "subtitle": {
                "supported_formats": [".srt"],
                "max_length": 100,
                "default_encoding": "utf-8"
            },
            "processing": {
                "max_subtitle_merge_range": 10,
                "default_silence_duration": 0.6,
                "speed_adjustment_range": [0.5, 2.0],
                "quality_presets": {
                    "high": {"crf": 18, "preset": "slow"},
                    "medium": {"crf": 23, "preset": "medium"},
                    "low": {"crf": 28, "preset": "fast"}
                }
            },
            "ui": {
                "theme": "light",
                "language": "zh_CN",
                "auto_save_interval": 300,  # 秒
                "show_advanced_options": False
            }
        }
    
    def get_app_settings(self, category: str = None) -> Dict:
        """获取应用设置"""
        if category:
            return self._app_settings.get(category, {})
        return self._app_settings
    
    def save_app_settings(self, settings: Dict):
        """保存应用设置"""
        settings_file = os.path.join(self.config_dir, "app_settings.json")
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        self._app_settings = settings
    
    def update_app_settings(self, category: str, updates: Dict):
        """更新特定类别的应用设置"""
        if category in self._app_settings:
            self._app_settings[category].update(updates)
            self.save_app_settings(self._app_settings)
    
    # Prompt风格管理
    def _scan_prompt_styles(self):
        """扫描可用的Prompt风格"""
        self._prompt_styles = {}

        if not os.path.exists(self.prompts_dir):
            self.logger.warning(f"Prompts目录不存在: {self.prompts_dir}")
            self._create_default_prompt_styles()
            return

        self.logger.info(f"扫描Prompt风格目录: {self.prompts_dir}")

        # 扫描JSON文件和目录
        try:
            items = os.listdir(self.prompts_dir)
            self.logger.info(f"找到 {len(items)} 个项目: {items}")

            for item in items:
                item_path = os.path.join(self.prompts_dir, item)

                if item.endswith('.json') and os.path.isfile(item_path):
                    # 处理JSON文件格式
                    style_name = item[:-5]  # 移除.json扩展名
                    self.logger.info(f"加载JSON风格: {style_name} from {item}")
                    style_config = self._load_prompt_style_json(item_path)
                    if style_config:
                        self._prompt_styles[style_name] = style_config
                        self.logger.info(f"成功加载风格: {style_name}")
                    else:
                        self.logger.warning(f"加载风格失败: {style_name}")

                elif os.path.isdir(item_path):
                    # 处理目录格式
                    self.logger.info(f"加载目录风格: {item}")
                    style_config = self._load_prompt_style_dir(item)
                    if style_config:
                        self._prompt_styles[item] = style_config
                        self.logger.info(f"成功加载目录风格: {item}")
                    else:
                        self.logger.warning(f"加载目录风格失败: {item}")

        except Exception as e:
            self.logger.error(f"扫描Prompt风格时发生错误: {e}")

        self.logger.info(f"总共加载了 {len(self._prompt_styles)} 个风格")
    
    def _create_default_prompt_styles(self):
        """创建默认的Prompt风格"""
        default_styles = [
            {
                "name": "style_1",
                "display_name": "科普解说风格",
                "description": "适合科普类视频，语言通俗易懂，逻辑清晰，注重知识点的传达。",
                "prompt": self._get_science_prompt(),
                "reference": self._get_science_reference()
            },
            {
                "name": "style_2", 
                "display_name": "娱乐搞笑风格",
                "description": "适合娱乐类视频，语言轻松幽默，富有感染力，注重观众的情绪调动。",
                "prompt": self._get_entertainment_prompt(),
                "reference": self._get_entertainment_reference()
            },
            {
                "name": "style_3",
                "display_name": "专业技术风格", 
                "description": "适合技术类视频，语言严谨准确，术语规范，注重技术细节的表达。",
                "prompt": self._get_technical_prompt(),
                "reference": self._get_technical_reference()
            }
        ]
        
        for style in default_styles:
            self._create_prompt_style(style)
    
    def _create_prompt_style(self, style_config: Dict):
        """创建Prompt风格目录和文件"""
        style_dir = os.path.join(self.prompts_dir, style_config["name"])
        os.makedirs(style_dir, exist_ok=True)
        
        # 创建配置文件
        config_file = os.path.join(style_dir, "config.json")
        config_data = {
            "display_name": style_config["display_name"],
            "description": style_config["description"],
            "version": "1.0",
            "created_at": datetime.now().isoformat(),
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 4000,
                "merge_range": 10
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        # 创建prompt文件
        prompt_file = os.path.join(style_dir, "prompt.txt")
        with open(prompt_file, 'w', encoding='utf-8') as f:
            f.write(style_config["prompt"])
        
        # 创建参考文案文件
        reference_file = os.path.join(style_dir, "reference.txt")
        with open(reference_file, 'w', encoding='utf-8') as f:
            f.write(style_config["reference"])
    
    def _load_prompt_style_json(self, json_file_path: str) -> Optional[Dict]:
        """从JSON文件加载Prompt风格"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 验证必需字段
            required_fields = ['name', 'prompt']
            if not all(field in config for field in required_fields):
                self.logger.warning(f"JSON风格文件缺少必需字段: {json_file_path}")
                return None

            return config

        except Exception as e:
            self.logger.error(f"加载JSON风格文件失败 {json_file_path}: {e}")
            return None

    def _load_prompt_style_dir(self, style_name: str) -> Optional[Dict]:
        """从目录加载Prompt风格（原有格式）"""
        style_dir = os.path.join(self.prompts_dir, style_name)

        # 检查必要文件是否存在
        config_file = os.path.join(style_dir, "config.json")
        prompt_file = os.path.join(style_dir, "prompt.txt")
        reference_file = os.path.join(style_dir, "reference.txt")

        if not all(os.path.exists(f) for f in [config_file, prompt_file, reference_file]):
            return None

        try:
            # 加载配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 加载prompt
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read()

            # 加载参考文案
            with open(reference_file, 'r', encoding='utf-8') as f:
                reference = f.read()

            return {
                "name": style_name,
                "config": config,
                "prompt": prompt,
                "reference": reference,
                "path": style_dir
            }

        except Exception as e:
            self.logger.error(f"加载目录风格 {style_name} 失败: {e}")
            return None
    
    def get_prompt_styles(self) -> Dict[str, Dict]:
        """获取所有Prompt风格"""
        return self._prompt_styles
    
    def get_prompt_style(self, style_name: str) -> Optional[Dict]:
        """获取特定的Prompt风格"""
        return self._prompt_styles.get(style_name)
    
    def get_style_list(self) -> List[str]:
        """获取风格名称列表（用于UI显示）"""
        return list(self._prompt_styles.keys())

    def get_style_list_detailed(self) -> List[Dict]:
        """获取详细的风格列表（用于UI显示）"""
        styles = []
        for name, style in self._prompt_styles.items():
            # 兼容两种格式：JSON格式和目录格式
            if "config" in style:
                # 目录格式
                display_name = style["config"].get("display_name", name)
                description = style["config"].get("description", "")
            else:
                # JSON格式
                display_name = style.get("name", name)
                description = style.get("description", "")

            styles.append({
                "name": name,
                "display_name": display_name,
                "description": description
            })
        return styles
    
    # 默认Prompt内容
    def _get_science_prompt(self) -> str:
        return """你是一个专业的科普视频文案改写专家。请根据以下要求改写字幕：

1. 改写风格要求：
   - 使用通俗易懂的语言，避免过于专业的术语
   - 保持逻辑清晰，层次分明
   - 适当使用比喻和类比来解释复杂概念
   - 语言要生动有趣，能够吸引观众注意力

2. 改写规则：
   - 可以对原字幕进行合并、拆分，但要保持内容的完整性
   - 允许在10条原字幕范围内进行逻辑重排
   - 使用中文口语化表达方式

3. 输出格式：
   请严格按照以下JSON格式输出：
   {
     "rewritten_subtitles": [
       {"index": 0, "text": "改写后的字幕内容"},
       {"index": 1, "text": "改写后的字幕内容"}
     ],
     "mapping": {
       "rewritten_0": [0, 1],
       "rewritten_1": [2, 3, 4]
     }
   }

原字幕内容：
{original_subtitles}

请开始改写："""

    def _get_science_reference(self) -> str:
        return """科普解说风格参考文案：

原文：量子力学是研究微观粒子运动规律的物理学分支。
改写：想象一下，如果我们能够缩小到原子的世界里，会发现那里的规则和我们日常生活完全不同。这就是量子力学要告诉我们的神奇世界。

原文：DNA双螺旋结构由沃森和克里克发现。
改写：1953年，两位科学家就像解开了生命密码一样，发现了DNA的秘密——它就像一个扭转的梯子，承载着我们所有的遗传信息。

特点：
- 用比喻和类比（"像扭转的梯子"）
- 增加故事性（"解开生命密码"）
- 语言通俗易懂
- 保持科学准确性"""

    def _get_entertainment_prompt(self) -> str:
        return """你是一个专业的娱乐视频文案改写专家。请根据以下要求改写字幕：

1. 改写风格要求：
   - 语言轻松幽默，富有感染力
   - 适当使用网络流行语和梗
   - 增加互动性，引导观众参与
   - 营造轻松愉快的观看氛围

2. 改写规则：
   - 可以对原字幕进行合并、拆分
   - 允许在10条原字幕范围内进行逻辑重排
   - 使用口语化、网络化的表达方式

3. 输出格式：
   请严格按照以下JSON格式输出：
   {
     "rewritten_subtitles": [
       {"index": 0, "text": "改写后的字幕内容"},
       {"index": 1, "text": "改写后的字幕内容"}
     ],
     "mapping": {
       "rewritten_0": [0, 1],
       "rewritten_1": [2, 3, 4]
     }
   }

原字幕内容：
{original_subtitles}

请开始改写："""

    def _get_entertainment_reference(self) -> str:
        return """娱乐搞笑风格参考文案：

原文：今天我们来学习如何制作蛋糕。
改写：兄弟们！今天咱们要搞个大事情——做蛋糕！保证比外面买的还香，关键是成本只要一半！

原文：这个方法很简单，大家可以试试。
改写：这个方法简单到什么程度？连我这种手残党都能成功，你们还怕什么？赶紧试试吧！

特点：
- 使用网络用语（"兄弟们"、"搞个大事情"）
- 增加互动感（"你们还怕什么？"）
- 语言生动有趣
- 营造亲近感"""

    def _get_technical_prompt(self) -> str:
        return """你是一个专业的技术视频文案改写专家。请根据以下要求改写字幕：

1. 改写风格要求：
   - 语言严谨准确，术语使用规范
   - 逻辑清晰，步骤明确
   - 注重技术细节的准确表达
   - 保持专业性和权威性

2. 改写规则：
   - 可以对原字幕进行合并、拆分
   - 允许在10条原字幕范围内进行逻辑重排
   - 突出关键技术点和实现细节
   - 保持技术术语的准确性

3. 输出格式：
   请严格按照以下JSON格式输出：
   {
     "rewritten_subtitles": [
       {"index": 0, "text": "改写后的字幕内容"},
       {"index": 1, "text": "改写后的字幕内容"}
     ],
     "mapping": {
       "rewritten_0": [0, 1],
       "rewritten_1": [2, 3, 4]
     }
   }

原字幕内容：
{original_subtitles}

请开始改写："""

    def _get_technical_reference(self) -> str:
        return """专业技术风格参考文案：

原文：我们使用Python来处理数据。
改写：在数据处理环节，我们选择Python作为主要编程语言，利用其丰富的数据科学库生态系统来实现高效的数据分析。

原文：这个算法很快。
改写：该算法的时间复杂度为O(n log n)，在处理大规模数据集时表现出色，相比传统方法性能提升约40%。

特点：
- 使用准确的技术术语
- 提供具体的技术指标
- 逻辑严谨，表达精确
- 保持专业权威性"""
