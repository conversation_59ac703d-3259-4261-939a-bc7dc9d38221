# 阿里云TTS配置指南

本文档详细说明如何配置阿里云TTS服务，包括获取必要的凭证和配置环境变量。

## 前提条件

1. 拥有阿里云账号
2. 已开通智能语音交互服务
3. 已获取AccessKey ID和AccessKey Secret

## 获取配置信息

### 1. 获取AccessKey ID和AccessKey Secret

1. 登录[阿里云控制台](https://ecs.console.aliyun.com)
2. 点击右上角头像，选择"AccessKey管理"
3. 创建AccessKey或查看现有AccessKey
4. 记录AccessKey ID和AccessKey Secret

**安全提示**：
- AccessKey具有账户完全权限，请妥善保管
- 建议使用RAM用户的AccessKey，并授予最小必要权限
- 定期轮换AccessKey以提高安全性

### 2. 获取TTS AppKey

1. 登录[智能语音交互控制台](https://nls-portal.console.aliyun.com/)
2. 在左侧导航栏选择"项目管理"
3. 创建新项目或选择现有项目
4. 在项目详情页面找到"AppKey"
5. 记录AppKey值

### 3. 开通TTS服务

1. 在智能语音交互控制台
2. 选择"语音合成" -> "实时语音合成"
3. 确保服务状态为"已开通"
4. 查看可用的语音列表和配额

## 环境变量配置

在项目根目录的`.env`文件中配置以下环境变量：

```env
# 阿里云TTS配置
# 重要：这些是阿里云访问凭证，请妥善保管
ALIYUN_AK_ID=your_aliyun_access_key_id_here
ALIYUN_AK_SECRET=your_aliyun_access_key_secret_here
ALIYUN_TTS_APPKEY=your_aliyun_tts_appkey_here

# 阿里云TTS服务配置
ALIYUN_TTS_URL=wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1
ALIYUN_TTS_VOICE=longxiaochun_v2
ALIYUN_TTS_FORMAT=wav
ALIYUN_TTS_SAMPLE_RATE=24000
```

### 配置说明

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| ALIYUN_AK_ID | 阿里云AccessKey ID | LTAI5t... |
| ALIYUN_AK_SECRET | 阿里云AccessKey Secret | voNF9O... |
| ALIYUN_TTS_APPKEY | TTS应用的AppKey | 12345678 |
| ALIYUN_TTS_URL | TTS服务地址 | wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1 |
| ALIYUN_TTS_VOICE | 默认语音 | longxiaochun_v2 |
| ALIYUN_TTS_FORMAT | 音频格式 | wav |
| ALIYUN_TTS_SAMPLE_RATE | 采样率 | 24000 |

## 支持的语音列表

| 语音名称 | 描述 | 性别 | 语言 |
|----------|------|------|------|
| longxiaochun_v2 | 龙小春 | 女 | 中文 |
| xiaoyun | 小云 | 女 | 中文 |
| xiaogang | 小刚 | 男 | 中文 |
| ruoxi | 若汐 | 女 | 中文 |
| siqi | 思琪 | 女 | 中文 |
| sijia | 思佳 | 女 | 中文 |
| sicheng | 思诚 | 男 | 中文 |
| aiqi | 艾琪 | 女 | 中文 |
| aijia | 艾佳 | 女 | 中文 |
| aicheng | 艾诚 | 男 | 中文 |

## 测试配置

配置完成后，可以使用以下代码测试TTS服务：

```python
import asyncio
from ai_video_editor.services.aliyun_tts_client import AliyunTTSClient, TTSConfig

async def test_tts():
    # 创建TTS配置
    config = TTSConfig(
        voice="longxiaochun_v2",
        format="wav",
        sample_rate=24000,
        volume=50
    )
    
    # 创建TTS客户端
    client = AliyunTTSClient(config)
    
    # 测试文本
    test_text = "你好，这是阿里云TTS服务测试。"
    
    # 合成语音
    result = await client.synthesize_text(test_text)
    
    if result.success:
        print(f"TTS测试成功！")
        print(f"输出文件: {result.audio_file}")
        print(f"音频时长: {result.duration:.2f}秒")
    else:
        print(f"TTS测试失败: {result.error_message}")

# 运行测试
asyncio.run(test_tts())
```

## 常见问题

### 1. Token获取失败

**错误信息**: "无法获取有效的Token"

**解决方案**:
- 检查ALIYUN_AK_ID和ALIYUN_AK_SECRET是否正确
- 确认阿里云账户有智能语音交互服务权限
- 检查网络连接是否正常

### 2. AppKey错误

**错误信息**: "缺少ALIYUN_TTS_APPKEY环境变量"

**解决方案**:
- 确认已在智能语音交互控制台创建项目
- 检查AppKey是否正确配置在环境变量中
- 确认项目状态为"正常"

### 3. 语音合成失败

**错误信息**: "语音合成失败"

**解决方案**:
- 检查文本内容是否符合要求（长度、字符等）
- 确认选择的语音是否可用
- 检查账户余额和配额是否充足

### 4. 网络连接问题

**错误信息**: "连接超时"或"网络错误"

**解决方案**:
- 检查网络连接
- 确认防火墙设置允许WebSocket连接
- 尝试更换网络环境

## 安全最佳实践

1. **环境变量管理**:
   - 不要将包含真实密钥的.env文件提交到版本控制
   - 在生产环境使用更安全的密钥管理服务

2. **权限控制**:
   - 使用RAM用户而非主账户的AccessKey
   - 授予最小必要权限
   - 定期审查和轮换密钥

3. **监控和审计**:
   - 启用API调用日志
   - 监控异常调用模式
   - 设置费用预警

## 技术支持

如果遇到问题，可以通过以下方式获取帮助：

1. 查看[阿里云智能语音交互文档](https://help.aliyun.com/product/30413.html)
2. 提交[工单](https://selfservice.console.aliyun.com/ticket)
3. 访问[开发者社区](https://developer.aliyun.com/group/nls)

## 相关链接

- [智能语音交互控制台](https://nls-portal.console.aliyun.com/)
- [API文档](https://help.aliyun.com/document_detail/84435.html)
- [SDK下载](https://help.aliyun.com/document_detail/120681.html)
- [价格说明](https://www.aliyun.com/price/product#/nls/detail)
