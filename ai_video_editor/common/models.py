"""
Common models - 通用数据模型

定义项目中使用的核心数据结构
"""

import re
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from datetime import datetime

from .types import (
    FilePathType, TimestampType, DurationSecondsType, ProgressPercentType,
    SessionIdType, ProcessStatus, ErrorSeverity, FileType, SubtitleFormat,
    SubtitleStatus, EventType
)


@dataclass
class BaseResult:
    """基础结果类"""
    success: bool
    message: str = ""
    error_code: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    execution_time: DurationSecondsType = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def duration(self) -> float:
        """获取时长（兼容性属性）"""
        return self.metadata.get('duration', self.execution_time)


@dataclass
class FileInfo:
    """文件信息"""
    path: FilePathType
    name: str
    size: int  # bytes
    type: FileType
    format: str  # 文件格式，如 "mp4", "srt"
    created_at: datetime
    checksum: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TimeCode:
    """时间码"""
    hours: int
    minutes: int
    seconds: int
    milliseconds: int
    
    def __str__(self) -> str:
        return f"{self.hours:02d}:{self.minutes:02d}:{self.seconds:02d},{self.milliseconds:03d}"
    
    def to_seconds(self) -> float:
        """转换为秒数"""
        return self.hours * 3600 + self.minutes * 60 + self.seconds + self.milliseconds / 1000
    
    @classmethod
    def from_string(cls, time_str: str) -> 'TimeCode':
        """从字符串解析时间码"""
        pattern = r'(\d{2}):(\d{2}):(\d{2})[,.](\d{3})'
        match = re.match(pattern, time_str)
        if not match:
            raise ValueError(f"无效的时间码格式: {time_str}")
        
        return cls(
            hours=int(match.group(1)),
            minutes=int(match.group(2)),
            seconds=int(match.group(3)),
            milliseconds=int(match.group(4))
        )
    
    @classmethod
    def from_seconds(cls, seconds: float) -> 'TimeCode':
        """从秒数创建时间码"""
        total_ms = int(seconds * 1000)
        ms = total_ms % 1000
        total_seconds = total_ms // 1000
        s = total_seconds % 60
        total_minutes = total_seconds // 60
        m = total_minutes % 60
        h = total_minutes // 60
        
        return cls(hours=h, minutes=m, seconds=s, milliseconds=ms)


@dataclass
class SubtitleItem:
    """字幕条目"""
    index: int
    start_time: TimeCode
    end_time: TimeCode
    text: str
    status: SubtitleStatus = SubtitleStatus.ORIGINAL
    confidence: float = 1.0  # 置信度 0.0-1.0
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> float:
        """获取持续时间（秒）"""
        return self.end_time.to_seconds() - self.start_time.to_seconds()
    
    @property
    def word_count(self) -> int:
        """获取字数"""
        return len(self.text.replace(' ', ''))
    
    def validate(self) -> List[str]:
        """验证字幕条目"""
        errors = []
        
        if self.start_time.to_seconds() >= self.end_time.to_seconds():
            errors.append("开始时间不能大于或等于结束时间")
        
        if not self.text.strip():
            errors.append("字幕内容不能为空")
        
        if self.duration < 0.1:
            errors.append("字幕持续时间过短")
        
        if self.duration > 10.0:
            errors.append("字幕持续时间过长")
        
        return errors


@dataclass
class SubtitleCollection:
    """字幕集合"""
    items: List[SubtitleItem]
    format: SubtitleFormat = SubtitleFormat.SRT
    language: str = "zh-CN"
    encoding: str = "utf-8"
    source_file: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def total_count(self) -> int:
        """总条目数"""
        return len(self.items)
    
    @property
    def total_duration(self) -> float:
        """总时长（秒）"""
        if not self.items:
            return 0.0
        return self.items[-1].end_time.to_seconds()
    
    @property
    def total_word_count(self) -> int:
        """总字数"""
        return sum(item.word_count for item in self.items)
    
    def validate(self) -> List[str]:
        """验证字幕集合"""
        errors = []
        
        # 检查索引连续性
        for i, item in enumerate(self.items):
            if item.index != i:
                errors.append(f"字幕索引不连续: 期望 {i}, 实际 {item.index}")
        
        # 检查时间重叠
        for i in range(len(self.items) - 1):
            current = self.items[i]
            next_item = self.items[i + 1]
            
            if current.end_time.to_seconds() > next_item.start_time.to_seconds():
                errors.append(f"字幕时间重叠: 第 {i} 条和第 {i + 1} 条")
        
        # 验证每个条目
        for item in self.items:
            item_errors = item.validate()
            errors.extend([f"第 {item.index} 条字幕: {error}" for error in item_errors])
        
        return errors
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()


@dataclass
class MappingRelation:
    """映射关系（已弃用，使用字典格式）"""
    rewritten_index: int
    original_indices: List[int]
    confidence: float = 1.0  # 映射置信度 0.0-1.0
    notes: str = ""


@dataclass
class SubtitleMapping:
    """字幕映射关系（字典格式）"""
    original_subtitles: Dict[int, str]  # {0: "原字幕内容1", 1: "原字幕内容2", ...}
    rewritten_subtitles: Dict[int, str]  # {0: "新字幕内容1", 1: "新字幕内容2", ...}
    mapping_relations: Dict[int, List[int]]  # {新字幕索引: [原字幕索引列表], ...}
    video_segments: Dict[int, str] = None  # {原字幕索引: 视频片段路径, ...}


@dataclass
class VideoSegment:
    """视频片段"""
    index: int
    start_time: float
    end_time: float
    duration: float
    file_path: str
    subtitle_index: int = 0
    original_subtitle: str = ""


@dataclass
class MatchedSegment:
    """匹配的音视频片段"""
    new_subtitle_index: int
    new_subtitle_text: str
    audio_start_time: float
    audio_end_time: float
    audio_duration: float
    video_segments: List[Dict[str, Any]]
    total_video_duration: float
    speed_factor: float


@dataclass
class ProcessingContext:
    """处理上下文"""
    session_id: SessionIdType
    input_files: Dict[FileType, FileInfo]
    config: Dict[str, Any]
    temp_directory: FilePathType
    output_directory: FilePathType
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    shared_data: Dict[str, Any] = field(default_factory=dict)
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()


@dataclass
class Event:
    """事件数据"""
    type: EventType
    source: str  # 事件源
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    session_id: Optional[SessionIdType] = None


@dataclass
class ProcessingError:
    """处理错误信息"""
    code: str
    message: str
    severity: ErrorSeverity
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    traceback: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "code": self.code,
            "message": self.message,
            "severity": self.severity.value,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback
        }
