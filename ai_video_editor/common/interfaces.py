"""
Common interfaces - 通用接口定义

定义项目中使用的核心接口和协议
"""

from abc import ABC, abstractmethod
from typing import Protocol, runtime_checkable, Dict, List, Optional, Any, Callable

from .types import (
    FilePathType, DurationSecondsType, ProgressPercentType,
    ProcessStatus
)
from .models import (
    BaseResult, ProcessingContext, SubtitleCollection, 
    MappingRelation, ProcessingError
)


@runtime_checkable
class Processor(Protocol):
    """处理器接口协议"""
    
    async def process(self, context: ProcessingContext) -> BaseResult:
        """处理主方法"""
        ...
    
    def validate_input(self, context: ProcessingContext) -> BaseResult:
        """验证输入数据"""
        ...
    
    def get_progress(self) -> ProgressPercentType:
        """获取处理进度"""
        ...
    
    def cancel(self) -> bool:
        """取消处理"""
        ...


@runtime_checkable
class ConfigurableProcessor(Processor, Protocol):
    """可配置处理器接口"""
    
    def load_config(self, config: Dict[str, Any]) -> BaseResult:
        """加载配置"""
        ...
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        ...
    
    def validate_config(self, config: Dict[str, Any]) -> BaseResult:
        """验证配置"""
        ...


class BaseProcessor(ABC):
    """处理器基类"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.status = ProcessStatus.IDLE
        self.progress = 0.0
        self.error_handler: Optional[Callable] = None
        self.progress_callback: Optional[Callable] = None
    
    @abstractmethod
    async def _process_impl(self, context: ProcessingContext) -> BaseResult:
        """子类实现的具体处理逻辑"""
        pass
    
    async def process(self, context: ProcessingContext) -> BaseResult:
        """处理主方法"""
        try:
            # 前置验证
            validation_result = self.validate_input(context)
            if not validation_result.success:
                return validation_result
            
            # 更新状态
            self.status = ProcessStatus.RUNNING
            self.progress = 0.0
            
            # 执行处理
            result = await self._process_impl(context)
            
            # 更新状态
            if result.success:
                self.status = ProcessStatus.COMPLETED
                self.progress = 100.0
            else:
                self.status = ProcessStatus.FAILED
            
            return result
            
        except Exception as e:
            self.status = ProcessStatus.FAILED
            return BaseResult(
                success=False,
                message=f"处理器 {self.name} 执行失败",
                error_code="PROCESSOR_ERROR",
                metadata={"exception": str(e)}
            )
    
    def validate_input(self, context: ProcessingContext) -> BaseResult:
        """验证输入数据"""
        if not context.session_id:
            return BaseResult(
                success=False,
                message="缺少会话ID",
                error_code="MISSING_SESSION_ID"
            )
        
        return BaseResult(success=True, message="输入验证通过")
    
    def get_progress(self) -> ProgressPercentType:
        """获取处理进度"""
        return self.progress
    
    def update_progress(self, progress: ProgressPercentType, message: str = ""):
        """更新进度"""
        self.progress = max(0.0, min(100.0, progress))
        if self.progress_callback:
            self.progress_callback(self.name, self.progress, message)
    
    def cancel(self) -> bool:
        """取消处理"""
        if self.status == ProcessStatus.RUNNING:
            self.status = ProcessStatus.CANCELLED
            return True
        return False


# 专用接口定义
@runtime_checkable
class SubtitleProcessor(ConfigurableProcessor, Protocol):
    """字幕处理器接口"""
    
    def parse_subtitle_file(self, file_path: FilePathType) -> tuple[BaseResult, Optional[SubtitleCollection]]:
        """解析字幕文件"""
        ...
    
    def export_subtitle_file(self, subtitles: SubtitleCollection, file_path: FilePathType) -> BaseResult:
        """导出字幕文件"""
        ...
    
    def generate_timeline(self, audio_file: FilePathType, subtitles: SubtitleCollection) -> tuple[BaseResult, Optional[SubtitleCollection]]:
        """生成时间轴"""
        ...


@runtime_checkable
class VideoProcessor(ConfigurableProcessor, Protocol):
    """视频处理器接口"""
    
    def get_video_info(self, file_path: FilePathType) -> tuple[BaseResult, Optional[Dict[str, Any]]]:
        """获取视频信息"""
        ...
    
    def remove_audio(self, input_path: FilePathType, output_path: FilePathType) -> BaseResult:
        """移除音频轨道"""
        ...
    
    def split_video(self, input_path: FilePathType, segments: List[tuple[DurationSecondsType, DurationSecondsType]]) -> tuple[BaseResult, List[Any]]:
        """分割视频"""
        ...
    
    def adjust_speed(self, input_path: FilePathType, output_path: FilePathType, speed_factor: float) -> BaseResult:
        """调整播放速度"""
        ...


@runtime_checkable
class AIService(ConfigurableProcessor, Protocol):
    """AI服务接口"""
    
    def rewrite_subtitles(self, subtitles: SubtitleCollection, style_config: Dict[str, Any]) -> tuple[BaseResult, Optional[SubtitleCollection], Optional[List[MappingRelation]]]:
        """改写字幕"""
        ...
    
    def review_content(self, content: str, context: Dict[str, Any]) -> tuple[BaseResult, Optional[Dict[str, Any]]]:
        """内容审核"""
        ...
    
    def validate_response(self, response: str) -> tuple[bool, Optional[Dict[str, Any]]]:
        """验证AI响应"""
        ...


@runtime_checkable
class TTSService(ConfigurableProcessor, Protocol):
    """TTS服务接口"""
    
    def synthesize_speech(self, text: str, config: Dict[str, Any]) -> tuple[BaseResult, Optional[Any]]:
        """语音合成"""
        ...
    
    def get_supported_voices(self) -> List[str]:
        """获取支持的语音列表"""
        ...
    
    def estimate_duration(self, text: str) -> DurationSecondsType:
        """估算语音时长"""
        ...
