"""
Common types - 通用类型定义

定义项目中使用的基础类型和枚举
"""

from typing import Dict, List, Optional, Union, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import uuid

# 基础类型别名
FilePathType = str
TimestampType = str  # 格式: "HH:MM:SS,mmm"
DurationSecondsType = float
ProgressPercentType = float  # 0.0 - 100.0
SessionIdType = str

# 状态枚举
class ProcessStatus(Enum):
    """处理状态枚举"""
    IDLE = "idle"
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class ErrorSeverity(Enum):
    """错误严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class FileType(Enum):
    """文件类型枚举"""
    VIDEO = "video"
    AUDIO = "audio"
    SUBTITLE = "subtitle"
    CONFIG = "config"
    TEMP = "temp"
    OUTPUT = "output"

class SubtitleFormat(Enum):
    """字幕格式枚举"""
    SRT = "srt"
    VTT = "vtt"
    ASS = "ass"
    SSA = "ssa"

class SubtitleStatus(Enum):
    """字幕状态"""
    ORIGINAL = "original"
    REWRITTEN = "rewritten"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"

class EventType(Enum):
    """事件类型"""
    PROCESSING_STARTED = "processing_started"
    PROCESSING_PROGRESS = "processing_progress"
    PROCESSING_COMPLETED = "processing_completed"
    PROCESSING_FAILED = "processing_failed"
    PROCESSING_CANCELLED = "processing_cancelled"
    
    FILE_UPLOADED = "file_uploaded"
    FILE_PROCESSED = "file_processed"
    
    CONFIG_UPDATED = "config_updated"
    ERROR_OCCURRED = "error_occurred"

# 回调函数类型
EventHandler = Callable[['Event'], None]
ProgressCallback = Callable[[str, float, str], None]
ErrorCallback = Callable[[str, str, Dict[str, Any]], None]
