#!/usr/bin/env python3
"""
音画匹配服务

实现音频与视频片段的智能匹配
"""

import os
import json
import logging
import tempfile
import subprocess
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from ..common.models import BaseResult


@dataclass
class MatchedSegment:
    """匹配的音视频片段"""
    new_subtitle_index: int
    new_subtitle_text: str
    audio_start_time: float
    audio_end_time: float
    audio_duration: float
    video_segments: List[Dict]  # 匹配到的视频片段列表
    total_video_duration: float
    speed_factor: float  # 需要的速度调整因子


class AudioVideoMatcher:
    """音画匹配器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def match_audio_video(self,
                         audio_file: str,
                         video_segments_dir: str,
                         subtitles,
                         original_subtitles,
                         mapping_relations: Dict[str, List[int]],
                         output_dir: str) -> BaseResult:
        """
        执行音画匹配

        Args:
            audio_file: 音频文件路径
            video_segments_dir: 视频片段目录
            subtitles: 新字幕集合
            original_subtitles: 原字幕集合
            mapping_relations: 映射关系
            output_dir: 输出目录

        Returns:
            BaseResult: 匹配结果
        """
        try:
            self.logger.info("开始音画匹配...")

            # 1. 首先创建匹配片段对象
            matched_segments = self._create_matched_segments_from_subtitles(
                subtitles, mapping_relations
            )

            if not matched_segments:
                return BaseResult(
                    success=False,
                    message="无法创建匹配片段"
                )

            # 2. 提取音频片段
            audio_extraction_result = self._extract_audio_segments(
                audio_file, matched_segments, output_dir
            )

            if not audio_extraction_result.success:
                return audio_extraction_result

            # 3. 提取匹配的视频片段
            video_extraction_result = self._extract_matched_video_segments(
                matched_segments, output_dir
            )

            if not video_extraction_result.success:
                return video_extraction_result

            self.logger.info(f"音画匹配完成，生成{len(matched_segments)}个匹配段")

            return BaseResult(
                success=True,
                message=f"音画匹配完成，生成{len(matched_segments)}个匹配段",
                metadata={
                    'matched_segments': matched_segments,
                    'total_matches': len(matched_segments),
                    'output_dir': output_dir,
                    'audio_segments_dir': audio_extraction_result.metadata.get('output_dir'),
                    'video_segments_dir': video_extraction_result.metadata.get('output_dir')
                }
            )

        except Exception as e:
            self.logger.error(f"音画匹配失败: {e}")
            return BaseResult(
                success=False,
                message=f"音画匹配异常: {str(e)}"
            )
    
    def match_audio_video_segments(self, new_subtitles: List[Dict], 
                                  old_subtitles: List[Dict],
                                  subtitle_mapping: Dict[str, List[int]],
                                  video_segments: List[Dict],
                                  audio_file: str,
                                  output_dir: str) -> BaseResult:
        """匹配音频和视频片段
        
        Args:
            new_subtitles: 新字幕列表
            old_subtitles: 原字幕列表
            subtitle_mapping: 新旧字幕映射关系
            video_segments: 视频片段列表
            audio_file: 音频文件路径
            output_dir: 输出目录
            
        Returns:
            匹配结果
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            matched_segments = []
            
            # 为每个新字幕匹配音频段和视频片段
            for i, new_subtitle in enumerate(new_subtitles):
                # 计算音频时间段
                audio_start = new_subtitle['start_time']
                audio_end = new_subtitle['end_time']

                # 获取实际的音频时长（从提取的音频片段文件）
                audio_file_path = os.path.join(output_dir, "audio_segments", f"audio_{i:03d}.mp3")
                audio_duration = self._get_actual_audio_duration(audio_file_path)
                
                # 根据映射关系找到对应的视频片段
                mapping_key = f"rewritten_{i}"
                if mapping_key in subtitle_mapping:
                    old_indices = subtitle_mapping[mapping_key]

                    # 收集对应的视频片段
                    matched_video_segments = []
                    total_video_duration = 0.0

                    for old_index in old_indices:
                        # 查找对应索引的视频片段
                        video_segment = self._find_video_segment_by_index(video_segments, old_index)
                        if video_segment:
                            matched_video_segments.append(video_segment)
                            total_video_duration += video_segment.get('duration', 0.0)
                        else:
                            self.logger.warning(f"未找到索引为 {old_index} 的视频片段")
                    
                    # 不进行时长匹配，速度因子固定为1.0
                    speed_factor = 1.0

                    matched_segment = MatchedSegment(
                        new_subtitle_index=i,
                        new_subtitle_text=new_subtitle['text'],
                        audio_start_time=audio_start,
                        audio_end_time=audio_end,
                        audio_duration=audio_duration,
                        video_segments=matched_video_segments,
                        total_video_duration=total_video_duration,
                        speed_factor=speed_factor
                    )
                    
                    matched_segments.append(matched_segment)
                else:
                    self.logger.warning(f"新字幕 {i} 没有找到映射关系")
            
            # 提取匹配的音频片段
            audio_extraction_result = self._extract_audio_segments(
                audio_file, matched_segments, output_dir
            )
            
            if not audio_extraction_result.success:
                return audio_extraction_result
            
            # 提取匹配的视频片段
            video_extraction_result = self._extract_matched_video_segments(
                matched_segments, output_dir
            )
            
            if not video_extraction_result.success:
                return video_extraction_result
            
            return BaseResult(
                success=True,
                message=f"音画匹配完成，生成{len(matched_segments)}个匹配段",
                metadata={
                    'matched_segments': matched_segments,  # 保持对象格式，不转换为字典
                    'total_matches': len(matched_segments),
                    'output_dir': output_dir,
                    'audio_segments_dir': audio_extraction_result.metadata.get('output_dir'),
                    'video_segments_dir': video_extraction_result.metadata.get('output_dir')
                }
            )
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"音画匹配异常: {str(e)}",
                error_code="AUDIO_VIDEO_MATCH_ERROR"
            )
    
    def _extract_audio_segments(self, audio_file: str, 
                               matched_segments: List[MatchedSegment],
                               output_dir: str) -> BaseResult:
        """提取音频片段"""
        try:
            audio_output_dir = os.path.join(output_dir, "audio_segments")
            os.makedirs(audio_output_dir, exist_ok=True)
            
            extracted_files = []
            
            for segment in matched_segments:
                output_filename = f"audio_{segment.new_subtitle_index:03d}.mp3"
                output_path = os.path.join(audio_output_dir, output_filename)
                
                cmd = [
                    'ffmpeg', '-y',
                    '-i', audio_file,
                    '-ss', str(segment.audio_start_time),
                    '-t', str(segment.audio_duration),
                    '-acodec', 'libmp3lame',
                    '-ar', '16000',
                    '-ac', '1',
                    '-b:a', '128k',
                    output_path
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0 and os.path.exists(output_path):
                    extracted_files.append({
                        'index': segment.new_subtitle_index,
                        'file_path': output_path,
                        'duration': segment.audio_duration
                    })
                else:
                    self.logger.error(f"音频片段提取失败: {result.stderr}")
            
            return BaseResult(
                success=len(extracted_files) > 0,
                message=f"音频片段提取完成，成功{len(extracted_files)}个",
                metadata={
                    'extracted_files': extracted_files,
                    'output_dir': audio_output_dir
                }
            )
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"音频片段提取异常: {str(e)}",
                error_code="AUDIO_EXTRACTION_ERROR"
            )
    
    def _extract_matched_video_segments(self, matched_segments: List[MatchedSegment],
                                       output_dir: str) -> BaseResult:
        """提取匹配的视频片段"""
        try:
            video_output_dir = os.path.join(output_dir, "matched_video_segments")
            os.makedirs(video_output_dir, exist_ok=True)
            
            extracted_segments = []
            
            for segment in matched_segments:
                segment_dir = os.path.join(video_output_dir, f"segment_{segment.new_subtitle_index:03d}")
                os.makedirs(segment_dir, exist_ok=True)
                
                segment_files = []
                
                # 复制匹配到的视频片段
                for j, video_segment in enumerate(segment.video_segments):
                    source_path = video_segment['file_path']
                    if os.path.exists(source_path):
                        target_filename = f"video_{j:03d}.mp4"
                        target_path = os.path.join(segment_dir, target_filename)
                        
                        # 复制文件
                        import shutil
                        shutil.copy2(source_path, target_path)
                        
                        segment_files.append({
                            'index': j,
                            'file_path': target_path,
                            'duration': video_segment['duration'],
                            'original_subtitle_index': video_segment['subtitle_index']
                        })
                
                extracted_segments.append({
                    'new_subtitle_index': segment.new_subtitle_index,
                    'segment_dir': segment_dir,
                    'video_files': segment_files,
                    'total_duration': segment.total_video_duration,
                    'speed_factor': segment.speed_factor
                })
            
            return BaseResult(
                success=len(extracted_segments) > 0,
                message=f"视频片段提取完成，成功{len(extracted_segments)}个",
                metadata={
                    'extracted_segments': extracted_segments,
                    'output_dir': video_output_dir
                }
            )
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"视频片段提取异常: {str(e)}",
                error_code="VIDEO_EXTRACTION_ERROR"
            )
    
    def create_preview_data(self, matched_segments: List,
                           audio_segments_dir: str,
                           video_segments_dir: str) -> List[Dict]:
        """创建预览数据

        Args:
            matched_segments: 匹配的片段列表（可以是MatchedSegment对象或字典）
            audio_segments_dir: 音频片段目录
            video_segments_dir: 视频片段目录

        Returns:
            预览数据列表
        """
        preview_data = []

        for segment in matched_segments:
            # 兼容处理：支持对象和字典两种格式
            if hasattr(segment, 'new_subtitle_index'):
                # MatchedSegment对象
                subtitle_index = segment.new_subtitle_index
                subtitle_text = segment.new_subtitle_text
                audio_duration = segment.audio_duration
                total_video_duration = segment.total_video_duration
                speed_factor = segment.speed_factor
            elif isinstance(segment, dict):
                # 字典格式
                subtitle_index = segment.get('new_subtitle_index', 0)
                subtitle_text = segment.get('new_subtitle_text', '')
                audio_duration = segment.get('audio_duration', 0.0)
                total_video_duration = segment.get('total_video_duration', 0.0)
                speed_factor = segment.get('speed_factor', 1.0)
            else:
                # 未知格式，跳过
                continue

            # 音频文件路径
            audio_file = os.path.join(audio_segments_dir, f"audio_{subtitle_index:03d}.mp3")

            # 获取实际的音频时长
            actual_audio_duration = self._get_actual_audio_duration(audio_file)

            # 视频片段目录
            video_dir = os.path.join(video_segments_dir, f"segment_{subtitle_index:03d}")

            # 收集视频文件
            video_files = []
            if os.path.exists(video_dir):
                for file in os.listdir(video_dir):
                    if file.endswith('.mp4'):
                        video_files.append(os.path.join(video_dir, file))

            preview_item = {
                'subtitle_index': subtitle_index,
                'subtitle_text': subtitle_text,
                'audio_file': audio_file if os.path.exists(audio_file) else None,
                'audio_duration': actual_audio_duration,  # 使用实际测量的音频时长
                'video_files': video_files,
                'video_count': len(video_files),
                'total_video_duration': total_video_duration,
                'speed_factor': 1.0,  # 移除自动时长匹配，固定为1.0
                'time_match': False  # 移除时长匹配功能
            }

            preview_data.append(preview_item)

        return preview_data





    def _create_matched_segments(self, subtitles, mapping_relations: Dict[str, List[int]], audio_metadata: dict, video_metadata: dict) -> List:
        """创建匹配片段对象"""
        try:
            from ..common.models import MatchedSegment

            matched_segments = []

            for rewritten_key, original_indices in mapping_relations.items():
                rewritten_index = int(rewritten_key.split('_')[1])

                if rewritten_index < len(subtitles.items):
                    subtitle_item = subtitles.items[rewritten_index]

                    # 计算音频时长
                    start_time = subtitle_item.start_time.to_seconds() if hasattr(subtitle_item.start_time, 'to_seconds') else 0.0
                    end_time = subtitle_item.end_time.to_seconds() if hasattr(subtitle_item.end_time, 'to_seconds') else start_time + 2.0
                    audio_duration = end_time - start_time

                    # 获取视频片段信息
                    video_segments = []
                    total_video_duration = 0.0

                    for video_info in video_metadata.get('segments', []):
                        if video_info['rewritten_index'] == rewritten_index:
                            for file_info in video_info['files']:
                                # 估算视频时长
                                duration = 2.0
                                video_segments.append({
                                    'file_path': file_info['file_path'],
                                    'duration': duration
                                })
                                total_video_duration += duration

                    # 计算速度因子
                    speed_factor = total_video_duration / audio_duration if audio_duration > 0 else 1.0

                    matched_segment = MatchedSegment(
                        new_subtitle_index=rewritten_index,
                        new_subtitle_text=subtitle_item.text,
                        audio_start_time=start_time,
                        audio_end_time=end_time,
                        audio_duration=audio_duration,
                        video_segments=video_segments,
                        total_video_duration=total_video_duration,
                        speed_factor=speed_factor
                    )

                    matched_segments.append(matched_segment)

            return matched_segments

        except Exception as e:
            self.logger.error(f"创建匹配片段失败: {e}")
            return []

    def _create_matched_segments_from_subtitles(self, subtitles, mapping_relations: Dict[str, List[int]]) -> List:
        """从字幕创建匹配片段对象"""
        try:
            from ..common.models import MatchedSegment

            matched_segments = []

            # 处理不同的字幕数据格式
            subtitle_items = []
            if hasattr(subtitles, 'items'):
                # SubtitleCollection对象
                subtitle_items = subtitles.items
            elif isinstance(subtitles, list):
                # 列表格式
                subtitle_items = subtitles
            else:
                self.logger.error(f"不支持的字幕数据格式: {type(subtitles)}")
                return []

            for rewritten_key, original_indices in mapping_relations.items():
                # 处理不同格式的键
                if isinstance(rewritten_key, str):
                    if rewritten_key.startswith('rewritten_'):
                        rewritten_index = int(rewritten_key.split('_')[1])
                    else:
                        rewritten_index = int(rewritten_key)
                else:
                    rewritten_index = int(rewritten_key)

                if rewritten_index < len(subtitle_items):
                    subtitle_item = subtitle_items[rewritten_index]

                    # 处理不同的字幕项格式
                    if hasattr(subtitle_item, 'start_time') and hasattr(subtitle_item, 'end_time'):
                        # SubtitleItem对象
                        if hasattr(subtitle_item.start_time, 'to_seconds'):
                            start_time = subtitle_item.start_time.to_seconds()
                        else:
                            start_time = 0.0

                        if hasattr(subtitle_item.end_time, 'to_seconds'):
                            end_time = subtitle_item.end_time.to_seconds()
                        else:
                            # 如果没有时间码，尝试从TTS结果中获取实际时长
                            end_time = start_time + self._get_actual_audio_duration(rewritten_index)

                        text = subtitle_item.text
                    elif isinstance(subtitle_item, dict):
                        # 字典格式
                        start_time = subtitle_item.get('start_time', 0.0)
                        end_time = subtitle_item.get('end_time', start_time + 2.0)
                        text = subtitle_item.get('text', '')
                    else:
                        self.logger.warning(f"不支持的字幕项格式: {type(subtitle_item)}")
                        continue

                    audio_duration = end_time - start_time

                    # 创建匹配片段（暂时使用空的video_segments，实际应该从视频分割结果中获取）
                    matched_segment = MatchedSegment(
                        new_subtitle_index=rewritten_index,
                        new_subtitle_text=text,
                        audio_start_time=start_time,
                        audio_end_time=end_time,
                        audio_duration=audio_duration,
                        video_segments=[{
                            'file_path': f'segment_{rewritten_index:03d}.mp4',
                            'duration': audio_duration,
                            'subtitle_index': rewritten_index
                        }],  # 模拟视频片段
                        total_video_duration=audio_duration,  # 暂时使用音频时长
                        speed_factor=1.0  # 默认速度
                    )

                    matched_segments.append(matched_segment)

            self.logger.info(f"从字幕创建了{len(matched_segments)}个匹配片段")
            return matched_segments

        except Exception as e:
            self.logger.error(f"从字幕创建匹配片段失败: {e}")
            return []

    def _get_actual_audio_duration(self, audio_file_path: str) -> float:
        """获取实际的音频时长"""
        try:
            if not audio_file_path or not os.path.exists(audio_file_path):
                self.logger.warning(f"音频文件不存在: {audio_file_path}")
                return 2.0

            # 使用VideoUtils获取音频时长
            from ..utils.video_utils import VideoUtils
            video_utils = VideoUtils()

            # FFmpeg可以处理音频文件
            duration = video_utils.get_video_duration(audio_file_path)
            if duration > 0:
                self.logger.info(f"获取音频时长: {audio_file_path} = {duration:.2f}秒")
                return duration
            else:
                self.logger.warning(f"无法获取音频时长，使用默认值: {audio_file_path}")
                return 2.0  # 默认值

        except Exception as e:
            self.logger.warning(f"获取音频时长失败: {e}")
            return 2.0  # 回退到默认值

    def _find_video_segment_by_index(self, video_segments: List[Dict], target_index: int) -> Dict:
        """根据索引查找视频片段"""
        try:
            # 方法1: 直接通过索引访问（如果video_segments是按索引排序的）
            if 0 <= target_index < len(video_segments):
                segment = video_segments[target_index]
                # 验证这个片段的索引是否匹配
                if segment.get('index') == target_index or segment.get('subtitle_index') == target_index:
                    return segment

            # 方法2: 遍历查找匹配的索引
            for segment in video_segments:
                if (segment.get('index') == target_index or
                    segment.get('subtitle_index') == target_index or
                    segment.get('segment_index') == target_index):
                    return segment

            # 方法3: 如果没有找到，尝试通过文件名匹配
            for segment in video_segments:
                file_path = segment.get('file_path', '')
                if f"segment_{target_index:03d}" in file_path:
                    return segment

            self.logger.warning(f"未找到索引为 {target_index} 的视频片段")
            return None

        except Exception as e:
            self.logger.error(f"查找视频片段失败: {e}")
            return None
    
    def generate_final_output(self, matched_segments: List[MatchedSegment],
                             audio_file: str, subtitle_file: str,
                             output_dir: str) -> BaseResult:
        """生成最终输出文件
        
        Args:
            matched_segments: 匹配的片段列表
            audio_file: 完整音频文件
            subtitle_file: 完整字幕文件
            output_dir: 输出目录
            
        Returns:
            生成结果
        """
        try:
            # 创建输出目录结构
            final_output_dir = os.path.join(output_dir, "final_output")
            os.makedirs(final_output_dir, exist_ok=True)
            
            # 复制并重命名音频和字幕文件
            import shutil
            
            # 音频文件
            audio_basename = os.path.basename(audio_file)
            new_audio_name = f"New_{audio_basename}"
            new_audio_path = os.path.join(final_output_dir, new_audio_name)
            shutil.copy2(audio_file, new_audio_path)
            
            # 字幕文件
            subtitle_basename = os.path.basename(subtitle_file)
            new_subtitle_name = f"New_{subtitle_basename}"
            new_subtitle_path = os.path.join(final_output_dir, new_subtitle_name)
            shutil.copy2(subtitle_file, new_subtitle_path)
            
            # 创建视频片段目录
            original_segments_dir = os.path.join(final_output_dir, "original_video_segments")
            adjusted_segments_dir = os.path.join(final_output_dir, "speed_adjusted_segments")
            os.makedirs(original_segments_dir, exist_ok=True)
            os.makedirs(adjusted_segments_dir, exist_ok=True)
            
            # 处理视频片段
            original_files = []
            adjusted_files = []
            
            for segment in matched_segments:
                for j, video_segment in enumerate(segment.video_segments):
                    # 原始片段
                    original_name = f"{segment.new_subtitle_index}_{os.path.basename(video_segment['file_path'])}"
                    original_path = os.path.join(original_segments_dir, original_name)
                    shutil.copy2(video_segment['file_path'], original_path)
                    original_files.append(original_path)
                    
                    # 调速片段
                    adjusted_name = f"{segment.new_subtitle_index}_{j}.mp4"
                    adjusted_path = os.path.join(adjusted_segments_dir, adjusted_name)
                    
                    # 使用VideoProcessor调整速度
                    from .video_processor import VideoProcessor
                    processor = VideoProcessor()
                    
                    speed_result = processor.adjust_video_speed(
                        video_segment['file_path'], 
                        segment.speed_factor, 
                        adjusted_path
                    )
                    
                    if speed_result.success:
                        adjusted_files.append(adjusted_path)
            
            return BaseResult(
                success=True,
                message="最终输出生成完成",
                metadata={
                    'output_dir': final_output_dir,
                    'audio_file': new_audio_path,
                    'subtitle_file': new_subtitle_path,
                    'original_segments_dir': original_segments_dir,
                    'adjusted_segments_dir': adjusted_segments_dir,
                    'original_files_count': len(original_files),
                    'adjusted_files_count': len(adjusted_files)
                }
            )
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"最终输出生成异常: {str(e)}",
                error_code="FINAL_OUTPUT_ERROR"
            )
