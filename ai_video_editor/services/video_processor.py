#!/usr/bin/env python3
"""
视频处理服务

实现视频预处理、分割、编码等功能
"""

import os
import json
import logging
import tempfile
import subprocess
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from ..common.models import BaseResult


@dataclass
class VideoSegment:
    """视频片段信息"""
    index: int
    start_time: float
    end_time: float
    duration: float
    file_path: str
    subtitle_index: int
    original_subtitle: str


class VideoProcessor:
    """视频处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def remove_audio_from_video(self, video_path: str, output_path: str) -> BaseResult:
        """清除视频音频
        
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            
        Returns:
            处理结果
        """
        try:
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-c:v', 'copy',  # 复制视频流，不重新编码
                '-an',  # 移除音频
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return BaseResult(
                    success=True,
                    message="视频音频清除成功",
                    metadata={'output_file': output_path}
                )
            else:
                error_msg = result.stderr
                return BaseResult(
                    success=False,
                    message=f"视频音频清除失败: {error_msg}",
                    error_code="AUDIO_REMOVAL_FAILED"
                )
                
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"视频音频清除异常: {str(e)}",
                error_code="AUDIO_REMOVAL_ERROR"
            )
    
    def split_video_by_subtitles(self, video_path: str, subtitles: List[Dict], 
                                output_dir: str) -> BaseResult:
        """根据字幕分割视频
        
        Args:
            video_path: 视频文件路径
            subtitles: 字幕列表，每个字幕包含start_time, end_time, text等
            output_dir: 输出目录
            
        Returns:
            分割结果，包含所有视频片段信息
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 获取视频总时长
            video_duration = self._get_video_duration(video_path)
            if video_duration is None:
                return BaseResult(
                    success=False,
                    message="无法获取视频时长",
                    error_code="VIDEO_DURATION_ERROR"
                )
            
            segments = []

            # 按照修改后的要求实现视频分割逻辑：
            # 1. 第一段：从视频开始到第二条字幕开始时间点
            # 2. 中间段：当前字幕开始到下一条字幕开始时间点
            # 3. 最后一段：最后一条字幕开始时间点到视频结束

            if subtitles:
                # 第一段：从视频开始(0)到第二条字幕开始时间点
                if len(subtitles) > 1:
                    # 有多条字幕，第一段到第二条字幕开始
                    second_start = subtitles[1]['start_time']
                    segment = self._create_video_segment(
                        video_path, 0.0, second_start, 0, output_dir,
                        0, subtitles[0]['text']
                    )
                    if segment:
                        segments.append(segment)

                    # 中间段：当前字幕开始到下一条字幕开始时间点
                    for i in range(1, len(subtitles) - 1):
                        curr_start = subtitles[i]['start_time']
                        next_start = subtitles[i + 1]['start_time']

                        segment = self._create_video_segment(
                            video_path, curr_start, next_start, i, output_dir,
                            i, subtitles[i]['text']
                        )
                        if segment:
                            segments.append(segment)

                    # 最后一段：最后一条字幕开始时间点到视频结束
                    last_start = subtitles[-1]['start_time']
                    segment = self._create_video_segment(
                        video_path, last_start, video_duration,
                        len(subtitles) - 1, output_dir,
                        len(subtitles) - 1, subtitles[-1]['text']
                    )
                    if segment:
                        segments.append(segment)

                else:
                    # 只有一条字幕，从视频开始到视频结束
                    segment = self._create_video_segment(
                        video_path, 0.0, video_duration, 0, output_dir,
                        0, subtitles[0]['text']
                    )
                    if segment:
                        segments.append(segment)

            self.logger.info(f"视频分割完成，生成 {len(segments)} 个片段，按字幕序号编码(0,1,2...n)")
            self.logger.info(f"分割规则：按字幕开始时间点分割，每条字幕对应一段视频画面")
            
            # 创建视频片段字典 {原字幕索引: 视频片段路径}
            video_segments_dict = {}
            for i, segment in enumerate(segments):
                video_segments_dict[i] = segment.file_path

            return BaseResult(
                success=True,
                message=f"视频分割完成，生成{len(segments)}个片段",
                metadata={
                    'segments': [segment.__dict__ for segment in segments],
                    'video_segments_dict': video_segments_dict,
                    'total_segments': len(segments),
                    'output_dir': output_dir
                }
            )
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"视频分割异常: {str(e)}",
                error_code="VIDEO_SPLIT_ERROR"
            )
    
    def _get_video_duration(self, video_path: str) -> Optional[float]:
        """获取视频时长"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return float(data['format']['duration'])
            return None
            
        except Exception as e:
            self.logger.error(f"获取视频时长失败: {e}")
            return None
    
    def _create_video_segment(self, video_path: str, start_time: float, 
                             end_time: float, index: int, output_dir: str,
                             subtitle_index: int, subtitle_text: str) -> Optional[VideoSegment]:
        """创建视频片段"""
        try:
            duration = end_time - start_time
            if duration <= 0:
                return None
            
            # 生成输出文件名，按照字幕序号编码(0,1,2...n)
            output_filename = f"segment_{index:03d}.mp4"
            output_path = os.path.join(output_dir, output_filename)

            self.logger.info(f"创建视频片段 {index}: {start_time:.2f}s-{end_time:.2f}s ({duration:.2f}s) -> {output_filename}")
            
            # 使用FFmpeg提取片段
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-avoid_negative_ts', 'make_zero',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0 and os.path.exists(output_path):
                return VideoSegment(
                    index=index,
                    start_time=start_time,
                    end_time=end_time,
                    duration=duration,
                    file_path=output_path,
                    subtitle_index=subtitle_index,
                    original_subtitle=subtitle_text
                )
            else:
                self.logger.error(f"视频片段创建失败: {result.stderr}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建视频片段异常: {e}")
            return None
    
    def adjust_video_speed(self, video_path: str, speed_factor: float, 
                          output_path: str) -> BaseResult:
        """调整视频播放速度
        
        Args:
            video_path: 输入视频路径
            speed_factor: 速度因子（1.0=正常，2.0=2倍速，0.5=0.5倍速）
            output_path: 输出视频路径
            
        Returns:
            处理结果
        """
        try:
            # 计算PTS和音频速度参数
            video_pts = 1.0 / speed_factor
            audio_tempo = speed_factor
            
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-filter_complex', 
                f'[0:v]setpts={video_pts}*PTS[v];[0:a]atempo={audio_tempo}[a]',
                '-map', '[v]',
                '-map', '[a]',
                '-c:v', 'libx264',
                '-c:a', 'aac',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return BaseResult(
                    success=True,
                    message=f"视频速度调整成功（{speed_factor}x）",
                    metadata={
                        'output_file': output_path,
                        'speed_factor': speed_factor
                    }
                )
            else:
                error_msg = result.stderr
                return BaseResult(
                    success=False,
                    message=f"视频速度调整失败: {error_msg}",
                    error_code="SPEED_ADJUSTMENT_FAILED"
                )
                
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"视频速度调整异常: {str(e)}",
                error_code="SPEED_ADJUSTMENT_ERROR"
            )
    
    def batch_adjust_video_speeds(self, video_segments: List[Dict], 
                                 speed_factors: List[float], 
                                 output_dir: str) -> BaseResult:
        """批量调整视频片段速度
        
        Args:
            video_segments: 视频片段列表
            speed_factors: 对应的速度因子列表
            output_dir: 输出目录
            
        Returns:
            批量处理结果
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            adjusted_segments = []
            failed_segments = []
            
            for i, (segment, speed_factor) in enumerate(zip(video_segments, speed_factors)):
                input_path = segment['file_path']
                output_filename = f"adjusted_{i:03d}.mp4"
                output_path = os.path.join(output_dir, output_filename)
                
                result = self.adjust_video_speed(input_path, speed_factor, output_path)
                
                if result.success:
                    adjusted_segments.append({
                        'index': i,
                        'original_segment': segment,
                        'speed_factor': speed_factor,
                        'output_file': output_path
                    })
                else:
                    failed_segments.append({
                        'index': i,
                        'segment': segment,
                        'error': result.message
                    })
            
            success_rate = len(adjusted_segments) / len(video_segments)
            
            return BaseResult(
                success=success_rate > 0.8,  # 80%以上成功才算成功
                message=f"批量速度调整完成，成功{len(adjusted_segments)}/{len(video_segments)}个",
                metadata={
                    'adjusted_segments': adjusted_segments,
                    'failed_segments': failed_segments,
                    'success_rate': success_rate,
                    'output_dir': output_dir
                }
            )
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"批量速度调整异常: {str(e)}",
                error_code="BATCH_SPEED_ADJUSTMENT_ERROR"
            )
