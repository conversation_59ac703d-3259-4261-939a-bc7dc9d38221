#!/usr/bin/env python3
"""
阿里云流式TTS客户端
实现每条字幕单独合成音频，获取准确的音频时长
"""

import os
import time
import asyncio
import logging
import tempfile
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass

try:
    import nls
except ImportError:
    nls = None

from ..common.models import BaseResult


@dataclass
class StreamTTSConfig:
    """流式TTS配置"""
    access_key_id: str = ""
    access_key_secret: str = ""
    appkey: str = ""
    url: str = "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1"
    voice: str = "longxiaochun"
    aformat: str = "wav"
    sample_rate: int = 24000
    volume: int = 50
    speech_rate: int = 0
    pitch_rate: int = 0


@dataclass
class StreamTTSSegmentResult:
    """流式TTS单段结果"""
    success: bool
    audio_file: Optional[str] = None
    duration: float = 0.0
    text: str = ""
    index: int = 0
    error_message: Optional[str] = None


class AliyunStreamTTSClient:
    """阿里云流式TTS客户端"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化流式TTS客户端
        
        Args:
            config: TTS配置字典
        """
        self.logger = logging.getLogger(__name__)
        
        if nls is None:
            raise ImportError("请安装阿里云NLS SDK: pip install alibabacloud-nls")
        
        # 解析配置
        config = config or {}
        self.config = StreamTTSConfig(
            access_key_id=config.get("access_key_id", ""),
            access_key_secret=config.get("access_key_secret", ""),
            appkey=config.get("appkey", ""),
            url=config.get("url", "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1"),
            voice=config.get("voice", "longxiaochun"),
            aformat=config.get("format", "wav"),
            sample_rate=config.get("sample_rate", 24000),
            volume=config.get("volume", 50),
            speech_rate=config.get("speech_rate", 0),
            pitch_rate=config.get("pitch_rate", 0)
        )
        
        # 启用NLS日志（可选）
        nls.enableTrace(False)
        
        self.logger.info("阿里云流式TTS客户端初始化完成")
    
    async def synthesize_segments(self, 
                                 texts: List[str], 
                                 output_dir: str,
                                 progress_callback: Callable[[float, str], None] = None) -> BaseResult:
        """
        分段合成语音
        
        Args:
            texts: 文本列表，每个元素对应一条字幕
            output_dir: 输出目录
            progress_callback: 进度回调函数
            
        Returns:
            BaseResult: 合成结果，包含所有音频片段信息
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            if progress_callback:
                progress_callback(5.0, "开始流式TTS合成...")
            
            # 获取Token
            token = await self._get_token()
            if not token:
                return BaseResult(
                    success=False,
                    message="获取阿里云Token失败",
                    error_code="TOKEN_ERROR"
                )
            
            segment_results = []
            total_duration = 0.0
            
            # 为每条文本单独合成音频
            for i, text in enumerate(texts):
                if progress_callback:
                    progress = 10.0 + (i / len(texts)) * 80.0
                    progress_callback(progress, f"合成第 {i+1}/{len(texts)} 段音频...")
                
                # 生成输出文件路径
                audio_file = os.path.join(output_dir, f"audio_{i:03d}.{self.config.aformat}")
                
                # 合成单段音频
                segment_result = await self._synthesize_single_segment(
                    text=text,
                    output_file=audio_file,
                    token=token,
                    index=i
                )
                
                if segment_result.success:
                    segment_results.append(segment_result)
                    total_duration += segment_result.duration
                    self.logger.info(f"段{i}合成成功: {segment_result.duration:.2f}秒")
                else:
                    self.logger.error(f"段{i}合成失败: {segment_result.error_message}")
                    return BaseResult(
                        success=False,
                        message=f"第{i+1}段合成失败: {segment_result.error_message}",
                        error_code="SEGMENT_SYNTHESIS_ERROR"
                    )
            
            if progress_callback:
                progress_callback(100.0, f"流式TTS合成完成，共{len(segment_results)}段")
            
            self.logger.info(f"流式TTS合成完成: {len(segment_results)}段，总时长: {total_duration:.2f}秒")
            
            return BaseResult(
                success=True,
                message=f"流式TTS合成完成，共{len(segment_results)}段",
                metadata={
                    'segment_results': segment_results,
                    'total_segments': len(segment_results),
                    'total_duration': total_duration,
                    'output_dir': output_dir
                }
            )
            
        except Exception as e:
            self.logger.error(f"流式TTS合成异常: {e}")
            return BaseResult(
                success=False,
                message=f"流式TTS合成异常: {str(e)}",
                error_code="STREAM_TTS_ERROR"
            )
    
    async def _synthesize_single_segment(self, 
                                       text: str, 
                                       output_file: str, 
                                       token: str, 
                                       index: int) -> StreamTTSSegmentResult:
        """合成单个音频片段"""
        try:
            # 合成状态
            synthesis_result = {
                'completed': False,
                'error': None,
                'audio_data': bytearray()
            }
            
            # 回调函数
            def on_data(data, *args):
                """接收音频数据"""
                synthesis_result['audio_data'].extend(data)
            
            def on_sentence_begin(message, *args):
                """句子开始"""
                self.logger.debug(f"段{index}开始合成: {message}")
            
            def on_completed(message, *args):
                """合成完成"""
                synthesis_result['completed'] = True
                self.logger.debug(f"段{index}合成完成: {message}")
            
            def on_error(message, *args):
                """合成错误"""
                synthesis_result['error'] = message
                synthesis_result['completed'] = True
                self.logger.error(f"段{index}合成错误: {message}")
            
            def on_close(*args):
                """连接关闭"""
                self.logger.debug(f"段{index}连接关闭")
            
            # 创建流式TTS合成器
            synthesizer = nls.NlsStreamInputTtsSynthesizer(
                url=self.config.url,
                token=token,
                appkey=self.config.appkey,
                on_data=on_data,
                on_sentence_begin=on_sentence_begin,
                on_sentence_synthesis=lambda msg, *args: None,  # 时间戳预留接口
                on_sentence_end=lambda msg, *args: None,        # 时间戳预留接口
                on_completed=on_completed,
                on_error=on_error,
                on_close=on_close,
                callback_args=[]
            )
            
            # 开始流式合成
            synthesizer.startStreamInputTts(
                voice=self.config.voice,
                aformat=self.config.aformat,
                sample_rate=self.config.sample_rate,
                volume=self.config.volume,
                speech_rate=self.config.speech_rate,
                pitch_rate=self.config.pitch_rate
            )
            
            # 发送文本
            synthesizer.sendStreamInputTts(text)
            
            # 停止合成
            synthesizer.stopStreamInputTts()
            
            # 等待合成完成
            max_wait_time = 30  # 最大等待30秒
            wait_interval = 0.1
            waited_time = 0
            
            while not synthesis_result['completed'] and waited_time < max_wait_time:
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval
            
            if synthesis_result['error']:
                return StreamTTSSegmentResult(
                    success=False,
                    text=text,
                    index=index,
                    error_message=synthesis_result['error']
                )
            
            if not synthesis_result['completed']:
                return StreamTTSSegmentResult(
                    success=False,
                    text=text,
                    index=index,
                    error_message="合成超时"
                )
            
            # 保存音频数据
            with open(output_file, 'wb') as f:
                f.write(synthesis_result['audio_data'])
            
            # 获取音频时长
            duration = self._get_audio_duration(output_file)
            
            return StreamTTSSegmentResult(
                success=True,
                audio_file=output_file,
                duration=duration,
                text=text,
                index=index
            )
            
        except Exception as e:
            return StreamTTSSegmentResult(
                success=False,
                text=text,
                index=index,
                error_message=str(e)
            )
    
    async def _get_token(self) -> Optional[str]:
        """获取阿里云Token"""
        try:
            from alibabacloud_nls20180518.client import Client as NlsClient
            from alibabacloud_tea_openapi import models as open_api_models
            from alibabacloud_nls20180518 import models as nls_models
            
            # 创建配置
            config = open_api_models.Config(
                access_key_id=self.config.access_key_id,
                access_key_secret=self.config.access_key_secret,
                endpoint='nls-meta.cn-shanghai.aliyuncs.com'
            )
            
            # 创建客户端
            client = NlsClient(config)
            
            # 创建请求
            request = nls_models.CreateTokenRequest()
            
            # 发送请求
            response = await asyncio.get_event_loop().run_in_executor(
                None, client.create_token, request
            )
            
            if response.body and response.body.token:
                token = response.body.token.id
                self.logger.info("阿里云Token获取成功")
                return token
            else:
                self.logger.error("Token响应为空")
                return None
                
        except Exception as e:
            self.logger.error(f"获取Token失败: {e}")
            return None
    
    def _get_audio_duration(self, audio_file: str) -> float:
        """获取音频文件时长"""
        try:
            from ..utils.video_utils import VideoUtils
            video_utils = VideoUtils()
            duration = video_utils.get_video_duration(audio_file)
            return duration if duration > 0 else 0.0
        except Exception as e:
            self.logger.warning(f"获取音频时长失败: {e}")
            return 0.0
