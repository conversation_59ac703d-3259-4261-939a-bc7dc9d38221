"""
阿里云TTS Token管理器

实现阿里云TTS的Token获取、缓存和自动刷新机制
基于阿里云官方SDK，支持通过环境变量配置AccessKey
"""

import os
import json
import time
import threading
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

try:
    from aliyunsdkcore.client import AcsClient
    from aliyunsdkcore.request import CommonRequest
except ImportError:
    # 如果没有安装阿里云SDK，提供一个模拟实现
    class AcsClient:
        def __init__(self, *args, **kwargs):
            pass
        
        def do_action_with_exception(self, request):
            return '{"Token": {"Id": "mock_token", "ExpireTime": 1234567890}}'
    
    class CommonRequest:
        def set_method(self, method): pass
        def set_domain(self, domain): pass
        def set_version(self, version): pass
        def set_action_name(self, action): pass


@dataclass
class TokenInfo:
    """Token信息"""
    token: str
    expire_time: int  # 时间戳（秒）
    created_at: datetime
    
    @property
    def is_expired(self) -> bool:
        """检查Token是否已过期"""
        # 提前5分钟刷新Token
        buffer_time = 300  # 5分钟
        return time.time() >= (self.expire_time - buffer_time)
    
    @property
    def remaining_time(self) -> int:
        """获取剩余有效时间（秒）"""
        return max(0, self.expire_time - int(time.time()))


class AliyunTokenManager:
    """阿里云TTS Token管理器"""
    
    def __init__(self, access_key_id: str = None, access_key_secret: str = None):
        """
        初始化Token管理器
        
        Args:
            access_key_id: 阿里云AccessKey ID，如果为None则从环境变量读取
            access_key_secret: 阿里云AccessKey Secret，如果为None则从环境变量读取
        """
        self.access_key_id = access_key_id or os.getenv('ALIYUN_AK_ID')
        self.access_key_secret = access_key_secret or os.getenv('ALIYUN_AK_SECRET')
        
        if not self.access_key_id or not self.access_key_secret:
            raise ValueError("阿里云AccessKey ID和Secret不能为空，请设置环境变量ALIYUN_AK_ID和ALIYUN_AK_SECRET")
        
        self.logger = logging.getLogger(__name__)
        self._token_info: Optional[TokenInfo] = None
        self._lock = threading.Lock()
        self._auto_refresh = True
        self._refresh_thread: Optional[threading.Thread] = None
        
        # 阿里云TTS Token服务配置
        self.region_id = "cn-shanghai"
        self.domain = "nls-meta.cn-shanghai.aliyuncs.com"
        self.api_version = "2019-02-28"
        self.action_name = "CreateToken"
        
        # 创建阿里云客户端
        self._client = AcsClient(
            self.access_key_id,
            self.access_key_secret,
            self.region_id
        )
        
        self.logger.info("阿里云Token管理器初始化完成")
    
    def get_token(self, force_refresh: bool = False) -> str:
        """
        获取有效的Token
        
        Args:
            force_refresh: 是否强制刷新Token
            
        Returns:
            str: 有效的Token
            
        Raises:
            Exception: Token获取失败时抛出异常
        """
        with self._lock:
            # 检查是否需要获取新Token
            if force_refresh or self._token_info is None or self._token_info.is_expired:
                self._refresh_token()
            
            if self._token_info is None:
                raise Exception("无法获取有效的Token")
            
            return self._token_info.token
    
    def _refresh_token(self):
        """刷新Token"""
        try:
            self.logger.info("开始刷新阿里云TTS Token...")
            
            # 创建请求
            request = CommonRequest()
            request.set_method('POST')
            request.set_domain(self.domain)
            request.set_version(self.api_version)
            request.set_action_name(self.action_name)
            
            # 发送请求
            response = self._client.do_action_with_exception(request)
            
            # 解析响应
            response_data = json.loads(response)
            
            if 'Token' in response_data and 'Id' in response_data['Token']:
                token = response_data['Token']['Id']
                expire_time = response_data['Token']['ExpireTime']
                
                self._token_info = TokenInfo(
                    token=token,
                    expire_time=expire_time,
                    created_at=datetime.now()
                )
                
                # 转换过期时间为可读格式
                expire_date = datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d %H:%M:%S')
                
                self.logger.info(f"Token刷新成功，过期时间: {expire_date}")
                
                # 启动自动刷新线程
                if self._auto_refresh and (self._refresh_thread is None or not self._refresh_thread.is_alive()):
                    self._start_auto_refresh()
                
            else:
                raise Exception(f"Token响应格式错误: {response_data}")
                
        except Exception as e:
            self.logger.error(f"Token刷新失败: {e}")
            raise
    
    def _start_auto_refresh(self):
        """启动自动刷新线程"""
        if self._token_info is None:
            return
        
        def auto_refresh_worker():
            while self._auto_refresh and self._token_info is not None:
                try:
                    # 计算下次刷新时间（提前5分钟）
                    refresh_time = self._token_info.expire_time - 300 - int(time.time())
                    
                    if refresh_time > 0:
                        self.logger.debug(f"下次Token刷新将在 {refresh_time} 秒后进行")
                        time.sleep(refresh_time)
                        
                        # 检查是否仍需要刷新
                        if self._auto_refresh and self._token_info.is_expired:
                            with self._lock:
                                self._refresh_token()
                    else:
                        # 如果Token已经过期，立即刷新
                        with self._lock:
                            self._refresh_token()
                        
                except Exception as e:
                    self.logger.error(f"自动刷新Token失败: {e}")
                    time.sleep(60)  # 失败后等待1分钟再重试
        
        self._refresh_thread = threading.Thread(target=auto_refresh_worker, daemon=True)
        self._refresh_thread.start()
        self.logger.info("Token自动刷新线程已启动")
    
    def get_token_info(self) -> Optional[Dict[str, Any]]:
        """
        获取Token信息
        
        Returns:
            Dict: Token信息，包含token、过期时间、剩余时间等
        """
        if self._token_info is None:
            return None
        
        return {
            "token": self._token_info.token,
            "expire_time": self._token_info.expire_time,
            "expire_date": datetime.fromtimestamp(self._token_info.expire_time).strftime('%Y-%m-%d %H:%M:%S'),
            "remaining_time": self._token_info.remaining_time,
            "is_expired": self._token_info.is_expired,
            "created_at": self._token_info.created_at.isoformat()
        }
    
    def set_auto_refresh(self, enabled: bool):
        """
        设置是否启用自动刷新
        
        Args:
            enabled: 是否启用自动刷新
        """
        self._auto_refresh = enabled
        if enabled and self._token_info is not None:
            self._start_auto_refresh()
        self.logger.info(f"Token自动刷新已{'启用' if enabled else '禁用'}")
    
    def stop(self):
        """停止Token管理器"""
        self._auto_refresh = False
        if self._refresh_thread and self._refresh_thread.is_alive():
            self._refresh_thread.join(timeout=5)
        self.logger.info("Token管理器已停止")
    
    def __del__(self):
        """析构函数"""
        self.stop()


# 全局Token管理器实例
_global_token_manager: Optional[AliyunTokenManager] = None


def get_global_token_manager() -> AliyunTokenManager:
    """
    获取全局Token管理器实例
    
    Returns:
        AliyunTokenManager: 全局Token管理器实例
    """
    global _global_token_manager
    
    if _global_token_manager is None:
        _global_token_manager = AliyunTokenManager()
    
    return _global_token_manager


def get_aliyun_token(force_refresh: bool = False) -> str:
    """
    获取阿里云TTS Token的便捷函数
    
    Args:
        force_refresh: 是否强制刷新Token
        
    Returns:
        str: 有效的Token
    """
    manager = get_global_token_manager()
    return manager.get_token(force_refresh)


def get_token_info() -> Optional[Dict[str, Any]]:
    """
    获取Token信息的便捷函数
    
    Returns:
        Dict: Token信息
    """
    manager = get_global_token_manager()
    return manager.get_token_info()


# 使用示例
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 创建Token管理器
        token_manager = AliyunTokenManager()
        
        # 获取Token
        token = token_manager.get_token()
        print(f"获取到Token: {token[:20]}...")
        
        # 获取Token信息
        info = token_manager.get_token_info()
        print(f"Token信息: {info}")
        
        # 等待一段时间测试自动刷新
        print("等待自动刷新...")
        time.sleep(10)
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        if 'token_manager' in locals():
            token_manager.stop()
