"""
阿里云TTS客户端

基于阿里云智能语音服务SDK实现的TTS客户端，支持长文本语音合成
集成Token自动管理机制
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
import tempfile

from .aliyun_token_manager import get_aliyun_token, get_token_info
from ..common.models import BaseResult

try:
    import nls
except ImportError:
    # 如果没有安装阿里云语音SDK，提供一个模拟实现
    class MockNLS:
        @staticmethod
        def enableTrace(enabled):
            pass
        
        class NlsStreamInputTtsSynthesizer:
            def __init__(self, **kwargs):
                self.callbacks = kwargs
                self.task_id = "mock_task_id"
            
            def startTts(self, **kwargs):
                # 模拟TTS调用
                if 'on_data' in self.callbacks:
                    # 模拟音频数据
                    mock_audio_data = b'\x00' * 1024  # 1KB的模拟音频数据
                    self.callbacks['on_data'](mock_audio_data)
                
                if 'on_completed' in self.callbacks:
                    self.callbacks['on_completed']('{"message": "synthesis completed"}')
            
            def waitForComplete(self):
                pass
            
            def get_last_task_id(self):
                return self.task_id
    
    nls = MockNLS()


@dataclass
class TTSConfig:
    """TTS配置"""
    voice: str = "longxiaochun_v2"
    format: str = "wav"
    sample_rate: int = 24000
    volume: int = 50
    speech_rate: int = 0
    pitch_rate: int = 0
    url: str = "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1"


@dataclass
class TTSResult:
    """TTS结果"""
    success: bool
    audio_file: Optional[str] = None
    duration: float = 0.0
    task_id: Optional[str] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class AliyunTTSClient:
    """阿里云TTS客户端"""
    
    def __init__(self, config: TTSConfig = None):
        """
        初始化TTS客户端
        
        Args:
            config: TTS配置，如果为None则使用默认配置
        """
        self.config = config or TTSConfig()
        self.logger = logging.getLogger(__name__)
        
        # 启用NLS日志（可选）
        nls.enableTrace(False)
        
        self.logger.info("阿里云TTS客户端初始化完成")
    
    async def synthesize_text(self, text: str, output_file: str = None, 
                             progress_callback: Callable[[float, str], None] = None) -> TTSResult:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            output_file: 输出文件路径，如果为None则自动生成临时文件
            progress_callback: 进度回调函数
            
        Returns:
            TTSResult: 合成结果
        """
        try:
            # 获取Token
            if progress_callback:
                progress_callback(10.0, "获取访问令牌...")
            
            token = get_aliyun_token()
            token_info = get_token_info()
            
            if not token:
                return TTSResult(
                    success=False,
                    error_message="无法获取有效的访问令牌"
                )
            
            self.logger.info(f"使用Token: {token[:20]}..., 剩余时间: {token_info.get('remaining_time', 0)}秒")
            
            # 准备输出文件
            if output_file is None:
                temp_dir = tempfile.gettempdir()
                output_file = os.path.join(temp_dir, f"tts_output_{int(asyncio.get_event_loop().time())}.{self.config.get('format', 'mp3')}")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            if progress_callback:
                progress_callback(20.0, "准备语音合成...")
            
            # 创建合成结果容器
            synthesis_result = {
                'success': False,
                'error': None,
                'task_id': None,
                'audio_data': bytearray(),
                'completed': False
            }
            
            # 定义回调函数
            def on_data(data, *args):
                """音频数据回调 - 根据官方文档，第一个参数直接是二进制音频数据"""
                try:
                    if data:
                        synthesis_result['audio_data'].extend(data)
                        self.logger.debug(f"接收音频数据: {len(data)} 字节，总计: {len(synthesis_result['audio_data'])} 字节")
                        if progress_callback:
                            # 根据数据量估算进度
                            estimated_progress = min(90.0, 30.0 + len(synthesis_result['audio_data']) / 1024 * 0.1)
                            progress_callback(estimated_progress, f"正在合成语音... ({len(synthesis_result['audio_data'])} bytes)")
                    else:
                        self.logger.warning("接收到空的音频数据")
                except Exception as e:
                    self.logger.error(f"音频数据回调异常: {e}")
                    self.logger.debug(f"data类型: {type(data)}, 长度: {len(data) if data else 0}")
            
            def on_sentence_begin(message, *args):
                """句子开始回调"""
                self.logger.debug(f"句子开始: {message}")
            
            def on_sentence_synthesis(message, *args):
                """句子合成回调"""
                self.logger.debug(f"句子合成: {message}")
            
            def on_sentence_end(message, *args):
                """句子结束回调"""
                self.logger.debug(f"句子结束: {message}")
            
            def on_completed(message, *args):
                """合成完成回调"""
                self.logger.info(f"合成完成: {message}")
                synthesis_result['completed'] = True
                synthesis_result['success'] = True
            
            def on_error(message, *args):
                """错误回调 - 根据官方文档，message是JSON形式的字符串"""
                try:
                    error_msg = str(message)
                    self.logger.error(f"TTS合成错误: {error_msg}")
                    synthesis_result['error'] = error_msg
                    synthesis_result['completed'] = True
                except Exception as e:
                    self.logger.error(f"错误回调异常: {e}")
                    synthesis_result['error'] = str(e)
                    synthesis_result['completed'] = True
            
            def on_close(*args):
                """连接关闭回调"""
                self.logger.debug(f"连接关闭: {args}")
                synthesis_result['completed'] = True
            
            # 获取APPKEY
            appkey = self.config.get('appkey', '')
            if not appkey:
                appkey = os.getenv('ALIYUN_TTS_APPKEY', '')

            if not appkey:
                self.logger.error("缺少ALIYUN_TTS_APPKEY配置或环境变量")
                return BaseResult(
                    success=False,
                    message="缺少ALIYUN_TTS_APPKEY配置或环境变量",
                    error_code="MISSING_APPKEY"
                )

            # 创建TTS合成器（使用正确的参数名）
            synthesizer = nls.NlsSpeechSynthesizer(
                url=self.config.get("url", "wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1"),
                token=token,
                appkey=appkey,
                long_tts=False,
                on_metainfo=on_sentence_begin,
                on_data=on_data,
                on_completed=on_completed,
                on_error=on_error,
                on_close=on_close,
                callback_args=[]
            )
            
            if progress_callback:
                progress_callback(30.0, "开始语音合成...")
            
            # 记录TTS参数
            tts_params = {
                'text': text[:50] + '...' if len(text) > 50 else text,
                'voice': self.config.get("voice", "longxiaochun_v2"),
                'aformat': self.config.get("format", "mp3"),
                'sample_rate': self.config.get("sample_rate", 16000),
                'volume': self.config.get("volume", 50),
                'speech_rate': self.config.get("speech_rate", 0),
                'pitch_rate': self.config.get("pitch_rate", 0),
                'appkey': appkey[:10] + '...' if len(appkey) > 10 else appkey
            }
            self.logger.info(f"TTS合成参数: {tts_params}")

            # 开始合成（使用正确的方法名和参数）
            synthesizer.start(
                text=text,
                voice=self.config.get("voice", "zhixiaobai"),
                aformat=self.config.get("format", "mp3"),
                sample_rate=self.config.get("sample_rate", 16000),
                volume=self.config.get("volume", 50),
                speech_rate=self.config.get("speech_rate", 0),
                pitch_rate=self.config.get("pitch_rate", 0)
            )
            
            # 等待合成完成（使用循环等待）
            max_wait_time = 30  # 最大等待30秒
            wait_interval = 0.1  # 每100ms检查一次
            waited_time = 0

            while not synthesis_result['completed'] and waited_time < max_wait_time:
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval

            # 关闭连接
            synthesizer.shutdown()

            # 生成任务ID（SDK不提供此方法）
            import time
            task_id = f"tts_{int(time.time() * 1000)}"
            synthesis_result['task_id'] = task_id
            
            if progress_callback:
                progress_callback(95.0, "保存音频文件...")
            
            # 检查合成结果
            if not synthesis_result['success']:
                error_msg = synthesis_result.get('error', '未知错误')
                return TTSResult(
                    success=False,
                    error_message=f"语音合成失败: {error_msg}",
                    task_id=task_id
                )
            
            # 第一步：验证音频数据完整性
            if not synthesis_result['audio_data']:
                self.logger.error("TTS合成完成但没有音频数据")
                return BaseResult(
                    success=False,
                    message="TTS合成完成但没有音频数据",
                    error_code="NO_AUDIO_DATA"
                )

            audio_data_size = len(synthesis_result['audio_data'])
            self.logger.info(f"TTS合成完成，音频数据大小: {audio_data_size} 字节")

            # 验证音频数据质量
            if audio_data_size < 1000:
                self.logger.warning(f"音频数据过小: {audio_data_size} 字节，可能合成失败")

            # 检查音频数据是否全为零
            audio_bytes = bytes(synthesis_result['audio_data'])
            if audio_bytes == b'\x00' * len(audio_bytes):
                self.logger.error("音频数据全为零，TTS合成失败")
                return BaseResult(
                    success=False,
                    message="音频数据无效（全为零）",
                    error_code="INVALID_AUDIO_DATA"
                )

            # 检查音频数据头部
            if len(audio_bytes) >= 10:
                header_hex = audio_bytes[:10].hex()
                self.logger.debug(f"音频数据头部: {header_hex}")

                # 检查是否有有效的音频格式标识
                if not (audio_bytes.startswith(b'ID3') or
                       audio_bytes.startswith(b'\xff\xfb') or
                       audio_bytes.startswith(b'\xff\xfa') or
                       (audio_bytes[0] == 0xff and (audio_bytes[1] & 0xe0) == 0xe0)):
                    self.logger.warning(f"音频数据格式可能异常，头部: {header_hex}")
            else:
                self.logger.error(f"音频数据过短: {len(audio_bytes)} 字节")

            if progress_callback:
                progress_callback(80.0, f"TTS合成完成，正在保存音频文件...")

            # 第二步：安全写入音频文件
            try:
                self._write_audio_file_safely(output_file, synthesis_result['audio_data'])
                self.logger.info(f"音频文件写入完成: {output_file}")
            except Exception as write_error:
                self.logger.error(f"音频文件写入失败: {write_error}")
                return BaseResult(
                    success=False,
                    message=f"音频文件写入失败: {str(write_error)}",
                    error_code="AUDIO_WRITE_ERROR"
                )

            if progress_callback:
                progress_callback(90.0, "音频文件写入完成，正在获取时长...")

            # 第三步：检测节点确认文件完整性
            try:
                if not self._wait_for_audio_generation(output_file, 10.0):
                    raise Exception("音频文件检测失败")
                self.logger.info(f"音频文件检测通过: {output_file}")
            except Exception as detection_error:
                self.logger.error(f"音频文件检测失败: {detection_error}")
                return BaseResult(
                    success=False,
                    message=f"音频文件检测失败: {str(detection_error)}",
                    error_code="AUDIO_DETECTION_ERROR"
                )

            # 第四步：使用FFmpeg获取准确时长
            try:
                duration = self._get_audio_duration(output_file)
                self.logger.info(f"音频时长获取成功: {duration:.6f}秒")
            except Exception as duration_error:
                self.logger.error(f"音频时长获取失败: {duration_error}")
                return BaseResult(
                    success=False,
                    message=f"音频时长获取失败: {str(duration_error)}",
                    error_code="AUDIO_DURATION_ERROR"
                )

            if progress_callback:
                progress_callback(100.0, "语音合成完成")

            self.logger.info(f"语音合成成功，输出文件: {output_file}, 时长: {duration:.6f}秒")

            return BaseResult(
                success=True,
                message="语音合成成功",
                execution_time=duration,
                metadata={
                    'output_file': output_file,
                    'duration': duration,
                    'text_length': len(text),
                    'voice': self.config.get("voice", "zhixiaobai"),
                    'format': self.config.get("format", "mp3"),
                    'sample_rate': self.config.get("sample_rate", 16000)
                }
            )

        except Exception as e:
            self.logger.error(f"语音合成异常: {e}")
            return BaseResult(
                success=False,
                message=f"语音合成失败: {str(e)}",
                error_code="TTS_SYNTHESIS_ERROR"
            )

    def _write_audio_file_safely(self, output_file: str, audio_data: bytes) -> None:
        """安全写入音频文件（添加0.6秒静音前缀）"""
        import os
        import tempfile

        # 添加0.6秒静音前缀
        final_audio_data = self._add_silence_prefix(audio_data, 0.6)

        # 先写入临时文件，然后原子性移动
        temp_file = output_file + ".tmp"

        try:
            # 写入临时文件
            with open(temp_file, 'wb') as f:
                f.write(final_audio_data)

            # 验证临时文件
            if not os.path.exists(temp_file):
                raise Exception("临时文件创建失败")

            temp_size = os.path.getsize(temp_file)
            if temp_size != len(final_audio_data):
                raise Exception(f"文件大小不匹配: 期望{len(final_audio_data)}字节，实际{temp_size}字节")

            # 原子性移动到目标文件
            if os.path.exists(output_file):
                os.remove(output_file)

            os.rename(temp_file, output_file)

            # 最终验证
            if not os.path.exists(output_file):
                raise Exception("目标文件创建失败")

            final_size = os.path.getsize(output_file)
            if final_size != len(final_audio_data):
                raise Exception(f"最终文件大小不匹配: 期望{len(final_audio_data)}字节，实际{final_size}字节")

            self.logger.debug(f"音频文件安全写入完成: {output_file}, 大小: {final_size}字节")

        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            raise Exception(f"音频文件写入失败: {str(e)}")

    def _wait_for_audio_generation(self, audio_file: str, max_wait_seconds: float = 10.0) -> bool:
        """等待音频文件生成完成的检测节点（增强版）"""
        import time
        import os

        self.logger.debug(f"开始检测音频文件生成: {audio_file}")

        start_time = time.time()
        last_size = 0
        stable_count = 0

        while time.time() - start_time < max_wait_seconds:
            # 检查文件是否存在
            if not os.path.exists(audio_file):
                time.sleep(0.1)
                continue

            # 检查文件大小
            current_size = os.path.getsize(audio_file)

            if current_size == 0:
                # 文件存在但为空，继续等待
                time.sleep(0.1)
                continue

            if current_size == last_size:
                # 文件大小稳定，可能写入完成
                stable_count += 1
                if stable_count >= 5:  # 增加到5次检查确保稳定
                    # 额外验证：尝试读取文件头部
                    if self._validate_audio_file_format(audio_file):
                        self.logger.debug(f"音频文件生成完成并验证通过: {audio_file}, 大小: {current_size}字节")
                        return True
                    else:
                        self.logger.warning(f"音频文件格式验证失败: {audio_file}")
                        # 继续等待，可能还在写入
                        stable_count = 0
            else:
                # 文件大小还在变化，重置计数
                stable_count = 0
                last_size = current_size

            time.sleep(0.1)

        # 超时检查
        if os.path.exists(audio_file) and os.path.getsize(audio_file) > 0:
            # 最后一次格式验证
            if self._validate_audio_file_format(audio_file):
                self.logger.warning(f"音频文件检测超时但文件有效: {audio_file}")
                return True
            else:
                self.logger.error(f"音频文件检测超时且格式无效: {audio_file}")
                return False

        self.logger.error(f"音频文件生成检测失败: {audio_file}")
        return False

    def _validate_audio_file_format(self, audio_file: str) -> bool:
        """验证音频文件格式是否有效（支持WAV和MP3）"""
        try:
            import os

            if not os.path.exists(audio_file):
                return False

            file_size = os.path.getsize(audio_file)
            if file_size < 10:  # 最小音频文件大小
                return False

            # 读取文件头部验证格式
            with open(audio_file, 'rb') as f:
                header = f.read(12)

                if len(header) < 10:
                    return False

                # 检查WAV文件标识
                if header[0:4] == b'RIFF' and header[8:12] == b'WAVE':
                    self.logger.debug(f"音频文件格式验证通过: WAV格式")
                    return True

                # 检查MP3文件标识
                if header[0:3] == b'ID3' or header[0:2] == b'\xff\xfb' or header[0:2] == b'\xff\xfa':
                    self.logger.debug(f"音频文件格式验证通过: MP3格式")
                    return True

                # 检查其他可能的MP3标识
                if (header[0] == 0xff and (header[1] & 0xe0) == 0xe0):
                    self.logger.debug(f"音频文件格式验证通过: MP3格式（MPEG帧）")
                    return True

                self.logger.warning(f"音频文件格式未知: 头部 {header[:8].hex()}")
                # 对于未知格式，如果文件大小合理，也认为有效
                if file_size > 1000:  # 至少1KB
                    self.logger.debug(f"音频文件大小合理，认为格式有效: {file_size}字节")
                    return True

                return False

        except Exception as e:
            self.logger.warning(f"音频文件格式验证异常: {e}")
            # 验证异常时，如果文件存在且大小合理，认为有效
            try:
                if os.path.exists(audio_file) and os.path.getsize(audio_file) > 1000:
                    return True
            except:
                pass
            return False

    def _get_audio_duration(self, audio_file: str) -> float:
        """使用FFmpeg获取音频文件的准确时长（纯FFmpeg方案）"""

        # 检测节点：等待音频生成完成
        if not self._wait_for_audio_generation(audio_file):
            self.logger.error(f"音频文件生成检测失败，无法获取时长: {audio_file}")
            raise Exception(f"音频文件生成失败: {audio_file}")

        try:
            import subprocess

            # 只使用FFprobe获取时长，不使用任何估算方法
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                audio_file
            ]

            self.logger.debug(f"执行FFprobe命令: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

            if result.returncode == 0 and result.stdout.strip():
                duration_str = result.stdout.strip()
                duration = float(duration_str)

                if duration > 0:
                    self.logger.info(f"FFprobe获取音频时长: {duration:.6f}秒")
                    return duration
                else:
                    self.logger.error(f"FFprobe返回无效时长: {duration}")
                    raise Exception(f"FFprobe返回无效时长: {duration}")
            else:
                error_msg = result.stderr.strip() if result.stderr else "无错误信息"
                self.logger.error(f"FFprobe执行失败: 返回码{result.returncode}, 错误: {error_msg}")
                raise Exception(f"FFprobe执行失败: {error_msg}")

        except subprocess.TimeoutExpired:
            self.logger.error("FFprobe执行超时")
            raise Exception("FFprobe执行超时")
        except Exception as e:
            self.logger.error(f"获取音频时长失败: {e}")
            raise



    def add_silence_to_audio(self, input_file: str, output_file: str,
                           silence_duration: float = 0.6) -> BaseResult:
        """
        在音频开头添加静音
        
        Args:
            input_file: 输入音频文件
            output_file: 输出音频文件
            silence_duration: 静音时长（秒）
            
        Returns:
            BaseResult: 处理结果
        """
        try:
            # 这里需要使用音频处理库（如pydub）来添加静音
            # 由于pydub可能未安装，这里提供一个简单的实现
            
            # 简单的文件复制作为占位符
            import shutil
            shutil.copy2(input_file, output_file)
            
            self.logger.info(f"音频静音添加完成: {output_file}")
            
            return BaseResult(
                success=True,
                message=f"成功添加 {silence_duration} 秒静音"
            )
            
        except Exception as e:
            self.logger.error(f"添加静音失败: {e}")
            return BaseResult(
                success=False,
                message=f"添加静音失败: {str(e)}",
                error_code="AUDIO_PROCESSING_ERROR"
            )
    
    def get_supported_voices(self) -> list[str]:
        """
        获取支持的语音列表
        
        Returns:
            list: 支持的语音名称列表
        """
        # 阿里云TTS支持的语音列表（部分）
        return [
            "longxiaochun_v2",  # 龙小春（女声）
            "xiaoyun",          # 小云（女声）
            "xiaogang",         # 小刚（男声）
            "ruoxi",            # 若汐（女声）
            "siqi",             # 思琪（女声）
            "sijia",            # 思佳（女声）
            "sicheng",          # 思诚（男声）
            "aiqi",             # 艾琪（女声）
            "aijia",            # 艾佳（女声）
            "aicheng",          # 艾诚（男声）
        ]
    
    def estimate_duration(self, text: str) -> float:
        """
        估算语音时长
        
        Args:
            text: 文本内容
            
        Returns:
            float: 预估时长（秒）
        """
        # 简单的时长估算：假设每个字符需要0.15秒
        # 实际时长会根据语速、标点符号等因素变化
        char_count = len(text.replace(' ', '').replace('\n', ''))
        estimated_duration = char_count * 0.15
        
        # 考虑语速调整
        speed_factor = 1.0 + (self.config.speech_rate / 500.0)  # speech_rate范围-500到500
        estimated_duration = estimated_duration / speed_factor
        
        return max(1.0, estimated_duration)  # 最少1秒

    def _add_silence_prefix(self, audio_data: bytes, silence_duration: float) -> bytes:
        """为音频数据添加静音前缀

        Args:
            audio_data: 原始音频数据
            silence_duration: 静音时长（秒）

        Returns:
            添加静音前缀后的音频数据
        """
        try:
            import tempfile
            import subprocess
            import os

            # 创建临时文件
            temp_dir = tempfile.mkdtemp(prefix="silence_")
            original_file = os.path.join(temp_dir, "original.mp3")
            silence_file = os.path.join(temp_dir, "silence.mp3")
            output_file = os.path.join(temp_dir, "output.mp3")

            # 保存原始音频
            with open(original_file, 'wb') as f:
                f.write(audio_data)

            # 生成静音文件
            silence_cmd = [
                'ffmpeg', '-y',
                '-f', 'lavfi',
                '-i', f'anullsrc=channel_layout=mono:sample_rate=16000',
                '-t', str(silence_duration),
                '-acodec', 'libmp3lame',
                '-ar', '16000',
                '-ac', '1',
                '-b:a', '128k',
                silence_file
            ]

            subprocess.run(silence_cmd, capture_output=True, check=True)

            # 合并静音和原始音频
            concat_cmd = [
                'ffmpeg', '-y',
                '-i', silence_file,
                '-i', original_file,
                '-filter_complex', '[0:a][1:a]concat=n=2:v=0:a=1[out]',
                '-map', '[out]',
                '-acodec', 'libmp3lame',
                '-ar', '16000',
                '-ac', '1',
                '-b:a', '128k',
                output_file
            ]

            subprocess.run(concat_cmd, capture_output=True, check=True)

            # 读取结果
            if os.path.exists(output_file):
                with open(output_file, 'rb') as f:
                    result_data = f.read()

                # 清理临时文件
                import shutil
                shutil.rmtree(temp_dir)

                self.logger.debug(f"成功添加{silence_duration}秒静音前缀，原始大小: {len(audio_data)}字节，新大小: {len(result_data)}字节")
                return result_data
            else:
                raise Exception("静音前缀添加失败")

        except Exception as e:
            self.logger.warning(f"添加静音前缀失败: {e}，返回原始音频数据")
            # 如果失败，返回原始数据
            return audio_data


# 使用示例
if __name__ == "__main__":
    import asyncio
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    async def test_tts():
        try:
            # 创建TTS客户端
            config = TTSConfig(
                voice="longxiaochun_v2",
                format="wav",
                sample_rate=24000,
                volume=50
            )
            
            client = AliyunTTSClient(config)
            
            # 测试文本
            test_text = "你好，这是一个测试语音合成的文本。"
            
            # 进度回调
            def progress_callback(progress, message):
                print(f"进度: {progress:.1f}% - {message}")
            
            # 合成语音
            result = await client.synthesize_text(
                text=test_text,
                progress_callback=progress_callback
            )
            
            if result.success:
                print(f"合成成功！")
                print(f"输出文件: {result.audio_file}")
                print(f"时长: {result.duration:.2f}秒")
                print(f"任务ID: {result.task_id}")
            else:
                print(f"合成失败: {result.error_message}")
                
        except Exception as e:
            print(f"测试失败: {e}")
    
    # 运行测试
    asyncio.run(test_tts())
