"""
DeepSeek AI客户端

集成DeepSeek API，实现字幕智能改写功能
支持多种改写风格和自定义Prompt
"""

import json
import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
import aiohttp

from ..common.models import (
    BaseResult, SubtitleCollection, SubtitleItem, MappingRelation,
    ProcessingError, ErrorSeverity, ProcessingContext, SubtitleStatus
)
from ..common.interfaces import BaseProcessor


class DeepSeekClient(BaseProcessor):
    """DeepSeek AI客户端"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("DeepSeekClient", config)
        self.logger = logging.getLogger(__name__)
        
        # API配置
        self.api_key = config.get("api_key", "") if config else ""
        self.base_url = config.get("base_url", "https://api.deepseek.com") if config else "https://api.deepseek.com"
        self.model = config.get("model", "deepseek-chat") if config else "deepseek-chat"
        self.max_tokens = config.get("max_tokens", 4000) if config else 4000
        self.temperature = config.get("temperature", 0.7) if config else 0.7
        self.timeout = config.get("timeout", 60) if config else 60
        self.max_retries = config.get("max_retries", 3) if config else 3
        
        if not self.api_key:
            self.logger.warning("DeepSeek API密钥未配置")
    
    async def _process_impl(self, context: ProcessingContext) -> BaseResult:
        """处理字幕改写的主要逻辑"""
        try:
            # 从上下文获取原始字幕
            original_subtitles = context.shared_data.get("original_subtitles")
            if not original_subtitles:
                return BaseResult(
                    success=False,
                    message="未找到原始字幕数据",
                    error_code="ORIGINAL_SUBTITLES_NOT_FOUND"
                )
            
            # 获取改写风格配置
            style_config = context.config.get("style_config", {})
            
            self.update_progress(10, "准备字幕改写...")
            
            # 执行字幕改写
            result, rewritten_subtitles, mappings = await self.rewrite_subtitles(
                original_subtitles, style_config
            )
            
            if not result.success:
                return result
            
            # 保存结果到上下文
            context.shared_data["rewritten_subtitles"] = rewritten_subtitles
            context.shared_data["subtitle_mappings"] = mappings
            
            self.update_progress(100, "字幕改写完成")
            
            return BaseResult(
                success=True,
                message=f"成功改写 {len(original_subtitles.items)} -> {len(rewritten_subtitles.items)} 条字幕",
                metadata={
                    "original_count": len(original_subtitles.items),
                    "rewritten_count": len(rewritten_subtitles.items),
                    "mapping_count": len(mappings),
                    "style": style_config.get("name", "未知")
                }
            )
            
        except Exception as e:
            self.logger.error(f"字幕改写失败: {e}")
            return BaseResult(
                success=False,
                message=f"字幕改写失败: {str(e)}",
                error_code="SUBTITLE_REWRITE_ERROR"
            )
    
    async def rewrite_subtitles(self, subtitles: SubtitleCollection, style_config: Dict[str, Any]) -> Tuple[BaseResult, Optional[SubtitleCollection], Optional[List[MappingRelation]]]:
        """
        改写字幕
        
        Args:
            subtitles: 原始字幕集合
            style_config: 改写风格配置
            
        Returns:
            tuple: (处理结果, 改写后字幕集合, 映射关系列表)
        """
        try:
            if not self.api_key:
                return BaseResult(
                    success=False,
                    message="DeepSeek API密钥未配置",
                    error_code="API_KEY_NOT_CONFIGURED"
                ), None, None
            
            self.update_progress(20, "构建改写请求...")

            # 对于大量字幕，使用优化的单次处理（已移除时间码，大幅减少Token消耗）
            self.logger.info(f"处理{len(subtitles.items)}条字幕，使用简化格式确保稳定传递")

            # 构建Prompt
            prompt = self._build_rewrite_prompt(subtitles, style_config)
            
            # 重试机制：最多尝试3次
            max_retries = 3
            last_error = None

            for attempt in range(max_retries):
                try:
                    self.update_progress(40 + attempt * 10, f"发送AI改写请求 (尝试 {attempt + 1}/{max_retries})...")

                    # 调用DeepSeek API
                    api_result = await self._call_deepseek_api(prompt, style_config)

                    if not api_result["success"]:
                        last_error = f"API调用失败: {api_result['error']}"
                        self.logger.warning(f"第{attempt + 1}次尝试失败: {last_error}")
                        continue

                    self.update_progress(70, "解析AI响应...")

                    # 解析AI响应
                    parse_result = self._parse_ai_response(api_result["response"], "rewrite")

                    if not parse_result["success"]:
                        last_error = f"响应解析失败: {parse_result['error']}"
                        self.logger.warning(f"第{attempt + 1}次尝试解析失败: {last_error}")
                        continue

                    # 验证响应格式
                    validation_result = self._validate_rewrite_response(parse_result["data"])
                    if not validation_result["success"]:
                        last_error = f"响应格式验证失败: {validation_result['error_message']}"
                        self.logger.warning(f"第{attempt + 1}次尝试验证失败: {last_error}")
                        continue

                    self.update_progress(90, "构建改写结果...")

                    # 构建改写后的字幕集合和映射关系
                    rewritten_subtitles, mappings = self._build_rewritten_subtitles(
                        subtitles, parse_result["data"]
                    )

                    self.logger.info(f"成功改写字幕: {len(subtitles.items)} -> {len(rewritten_subtitles.items)} (尝试 {attempt + 1} 次)")

                    return BaseResult(
                        success=True,
                        message=f"字幕改写成功 (尝试 {attempt + 1} 次)"
                    ), rewritten_subtitles, mappings

                except Exception as e:
                    last_error = f"第{attempt + 1}次尝试异常: {str(e)}"
                    self.logger.warning(last_error)
                    continue

            # 所有重试都失败了
            error_report = f"字幕改写失败，已尝试 {max_retries} 次。最后错误: {last_error}"
            self.logger.error(error_report)

            return BaseResult(
                success=False,
                message=error_report,
                error_code="SUBTITLE_REWRITE_FAILED_AFTER_RETRIES",
                metadata={
                    "retry_count": max_retries,
                    "last_error": last_error
                }
            ), None, None
            
        except Exception as e:
            self.logger.error(f"字幕改写异常: {e}")
            return BaseResult(
                success=False,
                message=f"字幕改写异常: {str(e)}",
                error_code="REWRITE_EXCEPTION"
            ), None, None
    
    def _build_rewrite_prompt(self, subtitles: SubtitleCollection, style_config: Dict[str, Any]) -> str:
        """构建改写Prompt"""
        # 获取风格模板
        prompt_template = style_config.get("prompt", "")
        if not prompt_template:
            # 使用默认模板
            prompt_template = self._get_default_prompt_template()
        
        # 构建原始字幕文本
        original_subtitles_text = ""
        for i, item in enumerate(subtitles.items):
            original_subtitles_text += f"{i}: {item.text}\n"
        
        # 替换占位符
        prompt = prompt_template.replace("{original_subtitles}", original_subtitles_text)
        
        # 添加参数说明
        merge_range = style_config.get("merge_range", 10)
        prompt += f"\n\n注意：可以在{merge_range}条原字幕范围内进行合并重组。"
        
        return prompt
    
    def _get_default_prompt_template(self) -> str:
        """获取默认Prompt模板（数组格式映射关系）"""
        return """你是专业的字幕改写专家。请将以下字幕改写为通俗易懂、生动有趣的风格。

改写要求：
1. 保持原意不变，使用更通俗易懂的表达
2. 可以适当合并相关内容，提高表达效果
3. 保持专业术语准确性
4. 确保内容流畅自然、逻辑清晰
5. 明确标注新旧字幕的映射关系

原字幕内容（编号: 内容）：
{original_subtitles}

请严格按照以下JSON格式返回完整的改写结果：
{{
  "rewritten_subtitles": [
    {{"index": 0, "text": "改写后的第一条字幕"}},
    {{"index": 1, "text": "改写后的第二条字幕"}}
  ],
  "mapping": {{
    "rewritten_0": [0, 1],
    "rewritten_1": [2, 3, 4]
  }}
}}

重要说明：
- rewritten_subtitles: 数组格式，每个元素包含index和text字段
- mapping: 字典格式，键为"rewritten_X"（X为新字幕索引），值为对应的原字幕索引数组
- 必须返回完整的JSON，不能截断
- 确保JSON格式正确，所有括号和引号都要匹配
- 不要生成时间码，只包含序号和文本内容

请开始改写："""
    
    async def _call_deepseek_api(self, prompt: str, style_config: Dict[str, Any]) -> Dict[str, Any]:
        """调用DeepSeek API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 构建请求数据
        request_data = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": style_config.get("max_tokens", self.max_tokens),
            "temperature": style_config.get("temperature", self.temperature),
            "stream": False
        }
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(
                        f"{self.base_url}/v1/chat/completions",
                        headers=headers,
                        json=request_data
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()
                            
                            if "choices" in result and len(result["choices"]) > 0:
                                content = result["choices"][0]["message"]["content"]
                                
                                return {
                                    "success": True,
                                    "response": content,
                                    "usage": result.get("usage", {})
                                }
                            else:
                                return {
                                    "success": False,
                                    "error": "API响应格式错误"
                                }
                        else:
                            error_text = await response.text()
                            self.logger.warning(f"API调用失败 (尝试 {attempt + 1}/{self.max_retries}): {response.status} - {error_text}")
                            
                            if attempt == self.max_retries - 1:
                                return {
                                    "success": False,
                                    "error": f"HTTP {response.status}: {error_text}"
                                }
                            
                            # 等待后重试
                            await asyncio.sleep(2 ** attempt)
                            
            except asyncio.TimeoutError:
                self.logger.warning(f"API调用超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt == self.max_retries - 1:
                    return {
                        "success": False,
                        "error": "API调用超时"
                    }
                await asyncio.sleep(2 ** attempt)
                
            except Exception as e:
                self.logger.warning(f"API调用异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    return {
                        "success": False,
                        "error": f"API调用异常: {str(e)}"
                    }
                await asyncio.sleep(2 ** attempt)
        
        return {
            "success": False,
            "error": "所有重试都失败"
        }
    
    def _parse_ai_response(self, response: str, response_type: str = "rewrite") -> Dict[str, Any]:
        """解析AI响应"""
        try:
            # 尝试提取JSON部分
            response = response.strip()

            # 查找JSON开始和结束位置
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                return {
                    "success": False,
                    "error": "响应中未找到JSON格式数据"
                }

            json_str = response[json_start:json_end]

            # 尝试修复常见的JSON错误
            json_str = self._fix_common_json_errors(json_str)

            # 解析JSON
            data = json.loads(json_str)

            # 根据响应类型验证不同的字段
            if response_type == "rewrite":
                # 验证改写响应的必要字段
                if "rewritten_subtitles" not in data:
                    return {
                        "success": False,
                        "error": "响应中缺少 rewritten_subtitles 字段"
                    }

                # 处理新的字典格式映射关系
                if "mapping_relations" in data:
                    # 新格式：mapping_relations
                    data["mapping"] = data["mapping_relations"]
                elif "mapping" not in data:
                    # 如果缺少映射字段，创建默认映射
                    self.logger.warning("响应中缺少映射字段，创建默认1:1映射")
                    mapping = {}

                    # 处理字典格式的改写字幕
                    if isinstance(data["rewritten_subtitles"], dict):
                        for key in data["rewritten_subtitles"].keys():
                            mapping[key] = [int(key)]
                    else:
                        # 处理列表格式的改写字幕
                        for i, item in enumerate(data["rewritten_subtitles"]):
                            mapping[str(i)] = [i]

                    data["mapping"] = mapping
            elif response_type == "review":
                # 审核响应不需要特定字段验证，任何有效JSON都可以
                pass

            return {
                "success": True,
                "data": data
            }

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            self.logger.error(f"原始响应内容: {json_str[:500]}...")

            # 尝试修复常见的JSON错误
            try:
                fixed_text = self._fix_common_json_errors(json_str)
                self.logger.info(f"修复后内容: {fixed_text[:500]}...")

                data = json.loads(fixed_text)

                # 根据响应类型验证不同的字段
                if response_type == "rewrite":
                    # 验证改写响应的必要字段
                    if "rewritten_subtitles" not in data:
                        return {
                            "success": False,
                            "error": "响应中缺少 rewritten_subtitles 字段"
                        }

                    # 处理新的字典格式映射关系
                    if "mapping_relations" in data:
                        # 新格式：mapping_relations
                        data["mapping"] = data["mapping_relations"]
                    elif "mapping" not in data:
                        # 如果缺少映射字段，创建默认映射
                        self.logger.warning("响应中缺少映射字段，创建默认1:1映射")
                        mapping = {}

                        # 处理字典格式的改写字幕
                        if isinstance(data["rewritten_subtitles"], dict):
                            for key in data["rewritten_subtitles"].keys():
                                mapping[key] = [int(key)]
                        else:
                            # 处理列表格式的改写字幕
                            for i, item in enumerate(data["rewritten_subtitles"]):
                                mapping[str(i)] = [i]

                        data["mapping"] = mapping

                self.logger.info("JSON修复成功")
                return {
                    "success": True,
                    "data": data
                }

            except Exception as fix_error:
                self.logger.error(f"JSON修复也失败: {fix_error}")

                # 尝试激进修复
                try:
                    self.logger.info("尝试激进JSON修复...")
                    aggressive_fixed = self._aggressive_json_fix(json_str)
                    data = json.loads(aggressive_fixed)

                    # 验证激进修复的结果
                    if response_type == "rewrite":
                        if "rewritten_subtitles" not in data:
                            return {
                                "success": False,
                                "error": "激进修复后仍缺少 rewritten_subtitles 字段"
                            }

                        # 确保有映射关系
                        if "mapping" not in data:
                            self.logger.warning("激进修复后缺少映射字段，创建默认映射")
                            mapping = {}
                            for i, item in enumerate(data["rewritten_subtitles"]):
                                mapping[f"rewritten_{i}"] = [item.get("index", i)]
                            data["mapping"] = mapping

                    self.logger.info("激进JSON修复成功")
                    return {
                        "success": True,
                        "data": data
                    }

                except Exception as aggressive_error:
                    self.logger.error(f"激进修复也失败: {aggressive_error}")
                    return {
                        "success": False,
                        "error": f"JSON解析失败: {str(e)}，常规修复失败: {str(fix_error)}，激进修复失败: {str(aggressive_error)}"
                    }
        except Exception as e:
            return {
                "success": False,
                "error": f"响应解析异常: {str(e)}"
            }

    def _fix_common_json_errors(self, json_str: str) -> str:
        """修复常见的JSON错误（增强版）"""
        try:
            import re

            # 移除可能的前后缀文本，只保留JSON部分
            json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()

            # 检查JSON是否被截断（缺少结束符）
            if not json_str.rstrip().endswith('}'):
                self.logger.warning("检测到JSON响应被截断，尝试修复...")

                # 查找最后一个完整的字幕条目
                last_complete_match = None
                for match in re.finditer(r'\{"index":\s*\d+,\s*"text":\s*"[^"]*"\}', json_str):
                    last_complete_match = match

                if last_complete_match:
                    # 截取到最后一个完整条目
                    truncate_pos = last_complete_match.end()
                    json_str = json_str[:truncate_pos]

                    # 添加缺失的结束符
                    if '"rewritten_subtitles"' in json_str and not json_str.rstrip().endswith(']}'):
                        json_str = json_str.rstrip() + ']}'
                    elif not json_str.rstrip().endswith('}'):
                        json_str = json_str.rstrip() + '}'
                else:
                    # 如果找不到完整条目，尝试基本修复
                    json_str = json_str.rstrip()
                    if json_str.endswith(','):
                        json_str = json_str[:-1]
                    if not json_str.endswith('}'):
                        json_str += '}'

            # 修复缺少逗号的问题: "key": "value" "key2": "value2"
            json_str = re.sub(r'"\s*"([^"]+)"\s*:', r'", "\1":', json_str)
            json_str = re.sub(r'}\s*"([^"]+)"\s*:', r'}, "\1":', json_str)
            json_str = re.sub(r']\s*"([^"]+)"\s*:', r'], "\1":', json_str)

            # 修复对象间缺少逗号: } {
            json_str = re.sub(r'}\s*{', '}, {', json_str)

            # 修复数组间缺少逗号: ] [
            json_str = re.sub(r']\s*\[', '], [', json_str)

            # 修复对象中的尾随逗号: {"key": "value",}
            json_str = re.sub(r',(\s*})', r'\1', json_str)

            # 修复数组中的尾随逗号: ["item1", "item2",]
            json_str = re.sub(r',(\s*])', r'\1', json_str)

            # 修复多个连续逗号
            json_str = re.sub(r',+', ',', json_str)

            # 修复缺少引号的键名
            json_str = re.sub(r'(\w+)(\s*):', r'"\1"\2:', json_str)

            # 移除注释
            json_str = re.sub(r'//.*?\n', '\n', json_str)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

            # 修复单引号为双引号
            json_str = re.sub(r"'([^']*)'", r'"\1"', json_str)

            # 清理多余的空白字符
            json_str = re.sub(r'\s+', ' ', json_str)
            json_str = json_str.strip()

            return json_str

        except Exception as e:
            self.logger.warning(f"JSON修复失败: {e}")
            return json_str

    def _aggressive_json_fix(self, json_str: str) -> str:
        """更激进的JSON修复方法，重新构建JSON"""
        try:
            import re

            # 提取所有完整的字幕条目
            pattern = r'\{\s*"index"\s*:\s*(\d+)\s*,\s*"text"\s*:\s*"([^"]*?)"\s*\}'
            matches = re.findall(pattern, json_str)

            if matches:
                self.logger.info(f"激进修复：找到 {len(matches)} 个完整的字幕条目")

                # 重新构建JSON
                rewritten_subtitles = []
                mapping = {}

                for i, (index, text) in enumerate(matches):
                    rewritten_subtitles.append({
                        "index": int(index),
                        "text": text
                    })
                    mapping[f"rewritten_{i}"] = [int(index)]

                result = {
                    "rewritten_subtitles": rewritten_subtitles,
                    "mapping": mapping
                }

                return json.dumps(result, ensure_ascii=False)
            else:
                self.logger.warning("激进修复：未找到任何完整的字幕条目")
                return json_str

        except Exception as e:
            self.logger.warning(f"激进JSON修复失败: {e}")
            return json_str

    def _validate_rewrite_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证改写响应的格式和内容"""
        try:
            errors = []

            # 检查必要字段
            if "rewritten_subtitles" not in data:
                errors.append("缺少 rewritten_subtitles 字段")

            if "mapping" not in data and "mapping_relations" not in data:
                errors.append("缺少 mapping 或 mapping_relations 字段")

            # 验证 rewritten_subtitles 格式
            if "rewritten_subtitles" in data:
                rewritten = data["rewritten_subtitles"]
                if not isinstance(rewritten, list):
                    errors.append("rewritten_subtitles 必须是数组格式")
                else:
                    for i, item in enumerate(rewritten):
                        if not isinstance(item, dict):
                            errors.append(f"rewritten_subtitles[{i}] 必须是对象格式")
                            continue
                        if "index" not in item:
                            errors.append(f"rewritten_subtitles[{i}] 缺少 index 字段")
                        if "text" not in item:
                            errors.append(f"rewritten_subtitles[{i}] 缺少 text 字段")
                        if "text" in item and not isinstance(item["text"], str):
                            errors.append(f"rewritten_subtitles[{i}].text 必须是字符串")

            # 验证映射关系格式
            mapping_field = "mapping_relations" if "mapping_relations" in data else "mapping"
            if mapping_field in data:
                mapping = data[mapping_field]
                if not isinstance(mapping, dict):
                    errors.append(f"{mapping_field} 必须是对象格式")
                else:
                    for key, value in mapping.items():
                        if not isinstance(value, list):
                            errors.append(f"{mapping_field}.{key} 必须是数组格式")
                        elif not all(isinstance(x, int) for x in value):
                            errors.append(f"{mapping_field}.{key} 数组元素必须是整数")

            if errors:
                return {
                    "success": False,
                    "errors": errors,
                    "error_message": "; ".join(errors)
                }

            return {"success": True}

        except Exception as e:
            return {
                "success": False,
                "errors": [f"验证过程异常: {str(e)}"],
                "error_message": f"验证过程异常: {str(e)}"
            }

    async def _rewrite_subtitles_in_batches(self, subtitles: SubtitleCollection, style_config: Dict[str, Any]) -> Tuple[BaseResult, Optional[SubtitleCollection], Optional[List[MappingRelation]]]:
        """分批处理大量字幕的改写"""
        try:
            batch_size = 50  # 每批处理50条字幕
            total_items = len(subtitles.items)
            batches = []

            # 分割字幕为批次
            for i in range(0, total_items, batch_size):
                batch_items = subtitles.items[i:i + batch_size]
                batch_subtitles = SubtitleCollection(
                    items=batch_items,
                    format=subtitles.format,
                    language=subtitles.language,
                    encoding=subtitles.encoding,
                    metadata=subtitles.metadata.copy()
                )
                batches.append(batch_subtitles)

            self.logger.info(f"分为 {len(batches)} 批处理，每批最多 {batch_size} 条字幕")

            # 处理每个批次
            all_rewritten_items = []
            all_mappings = []
            current_rewritten_index = 0

            for batch_idx, batch in enumerate(batches):
                self.update_progress(
                    20 + (batch_idx * 60 // len(batches)),
                    f"处理第 {batch_idx + 1}/{len(batches)} 批字幕..."
                )

                # 处理单个批次
                batch_result, batch_rewritten, batch_mappings = await self._process_single_batch(
                    batch, style_config, current_rewritten_index
                )

                if not batch_result.success:
                    return batch_result, None, None

                # 合并结果
                if batch_rewritten and batch_mappings:
                    all_rewritten_items.extend(batch_rewritten.items)
                    all_mappings.extend(batch_mappings)
                    current_rewritten_index += len(batch_rewritten.items)

            # 创建最终的字幕集合
            final_subtitles = SubtitleCollection(
                items=all_rewritten_items,
                format=subtitles.format,
                language=subtitles.language,
                encoding=subtitles.encoding,
                metadata=subtitles.metadata.copy()
            )

            self.logger.info(f"分批处理完成: {total_items} -> {len(all_rewritten_items)} 条字幕")

            return BaseResult(
                success=True,
                message=f"成功分批改写 {total_items} -> {len(all_rewritten_items)} 条字幕",
                metadata={
                    "original_count": total_items,
                    "rewritten_count": len(all_rewritten_items),
                    "mapping_count": len(all_mappings),
                    "batch_count": len(batches)
                }
            ), final_subtitles, all_mappings

        except Exception as e:
            self.logger.error(f"分批处理失败: {e}")
            return BaseResult(
                success=False,
                message=f"分批处理异常: {str(e)}",
                error_code="BATCH_PROCESSING_FAILED"
            ), None, None

    async def _process_single_batch(self, batch_subtitles: SubtitleCollection, style_config: Dict[str, Any], start_index: int) -> Tuple[BaseResult, Optional[SubtitleCollection], Optional[List[MappingRelation]]]:
        """处理单个批次的字幕"""
        try:
            # 构建Prompt
            prompt = self._build_rewrite_prompt(batch_subtitles, style_config)

            # 调用API
            api_result = await self._call_deepseek_api(prompt, style_config)

            if not api_result["success"]:
                return BaseResult(
                    success=False,
                    message=f"批次API调用失败: {api_result['error']}",
                    error_code="BATCH_API_CALL_FAILED"
                ), None, None

            # 解析响应
            parse_result = self._parse_ai_response(api_result["response"], "rewrite")

            if not parse_result["success"]:
                return BaseResult(
                    success=False,
                    message=f"批次响应解析失败: {parse_result['error']}",
                    error_code="BATCH_RESPONSE_PARSE_FAILED"
                ), None, None

            # 构建结果
            rewritten_subtitles, mappings = self._build_rewritten_subtitles(
                batch_subtitles, parse_result["data"]
            )

            # 调整映射关系的索引（因为是分批处理）
            adjusted_mappings = []
            for mapping in mappings:
                # 计算原字幕的偏移量
                original_offset = start_index * 50 // len(batch_subtitles.items) if len(batch_subtitles.items) > 0 else 0

                adjusted_mapping = MappingRelation(
                    rewritten_index=mapping.rewritten_index + start_index,
                    original_indices=[idx + original_offset for idx in mapping.original_indices],
                    confidence=mapping.confidence,
                    notes=mapping.notes
                )
                adjusted_mappings.append(adjusted_mapping)

            return BaseResult(success=True, message="批次处理成功"), rewritten_subtitles, adjusted_mappings

        except Exception as e:
            self.logger.error(f"单批次处理失败: {e}")
            return BaseResult(
                success=False,
                message=f"单批次处理异常: {str(e)}",
                error_code="SINGLE_BATCH_FAILED"
            ), None, None

    def _build_rewritten_subtitles(self, original_subtitles: SubtitleCollection, ai_data: Dict[str, Any]) -> Tuple[SubtitleCollection, List[MappingRelation]]:
        """构建改写后的字幕集合和映射关系"""
        rewritten_items = []
        mappings = []
        
        # 处理改写后的字幕（支持字典和列表格式，不包含时间码）
        rewritten_subtitles = ai_data["rewritten_subtitles"]

        if isinstance(rewritten_subtitles, dict):
            # 字典格式：{"0": "文本内容", "1": "文本内容"}
            for key, text in rewritten_subtitles.items():
                index = int(key)
                subtitle_item = SubtitleItem(
                    index=index,
                    start_time=None,  # 不设置时间码，等待TTS合成后生成
                    end_time=None,    # 不设置时间码，等待TTS合成后生成
                    text=text,
                    status=SubtitleStatus.REWRITTEN
                )
                rewritten_items.append(subtitle_item)
        else:
            # 列表格式：[{"index": 0, "text": "内容"}, ...]
            for item_data in rewritten_subtitles:
                subtitle_item = SubtitleItem(
                    index=item_data["index"],
                    start_time=None,  # 不设置时间码，等待TTS合成后生成
                    end_time=None,    # 不设置时间码，等待TTS合成后生成
                    text=item_data["text"],
                    status=SubtitleStatus.REWRITTEN
                )
                rewritten_items.append(subtitle_item)
        
        # 处理映射关系（字典格式）
        for mapping_key, original_indices in ai_data["mapping"].items():
            # 统一处理索引格式
            if isinstance(mapping_key, str):
                if mapping_key.startswith("rewritten_"):
                    rewritten_index = int(mapping_key.split("_")[1])
                else:
                    rewritten_index = int(mapping_key)
            else:
                rewritten_index = int(mapping_key)

            # 确保original_indices是列表
            if not isinstance(original_indices, list):
                original_indices = [original_indices]

            mapping = MappingRelation(
                rewritten_index=rewritten_index,
                original_indices=original_indices,
                confidence=1.0,
                notes=f"AI改写映射: {mapping_key} -> {original_indices}"
            )
            mappings.append(mapping)
        
        # 创建改写后的字幕集合
        rewritten_collection = SubtitleCollection(
            items=rewritten_items,
            format=original_subtitles.format,
            language=original_subtitles.language,
            encoding=original_subtitles.encoding,
            metadata={
                "rewritten_by": "DeepSeek AI",
                "original_count": len(original_subtitles.items),
                "rewritten_count": len(rewritten_items),
                "has_timecode": False,  # 标记为无时间码
                "stage": "text_only",   # 当前阶段：仅文本
                "next_stage": "tts_synthesis"  # 下一阶段：TTS合成
            }
        )
        
        return rewritten_collection, mappings
    
    async def review_content(self, content: str, context: Dict[str, Any]) -> Tuple[BaseResult, Optional[Dict[str, Any]]]:
        """
        内容审核
        
        Args:
            content: 待审核内容
            context: 审核上下文
            
        Returns:
            tuple: (处理结果, 审核报告)
        """
        try:
            # 构建审核Prompt
            review_prompt = f"""请对以下内容进行审核，检查是否存在以下问题：
1. 语法错误
2. 逻辑不通
3. 表达不当
4. 内容重复

内容：
{content}

请以JSON格式返回审核结果：
{{
  "issues": [
    {{"type": "语法错误", "description": "具体问题描述", "severity": "low|medium|high"}},
    {{"type": "逻辑问题", "description": "具体问题描述", "severity": "low|medium|high"}}
  ],
  "suggestions": [
    "改进建议1",
    "改进建议2"
  ],
  "overall_score": 85
}}"""
            
            # 调用API
            api_result = await self._call_deepseek_api(review_prompt, {})
            
            if not api_result["success"]:
                return BaseResult(
                    success=False,
                    message=f"内容审核API调用失败: {api_result['error']}",
                    error_code="REVIEW_API_FAILED"
                ), None
            
            # 解析审核结果
            parse_result = self._parse_ai_response(api_result["response"], "review")
            
            if not parse_result["success"]:
                return BaseResult(
                    success=False,
                    message=f"审核结果解析失败: {parse_result['error']}",
                    error_code="REVIEW_PARSE_FAILED"
                ), None
            
            return BaseResult(
                success=True,
                message="内容审核完成"
            ), parse_result["data"]
            
        except Exception as e:
            self.logger.error(f"内容审核异常: {e}")
            return BaseResult(
                success=False,
                message=f"内容审核异常: {str(e)}",
                error_code="REVIEW_EXCEPTION"
            ), None
    
    def validate_response(self, response: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        验证AI响应格式
        
        Args:
            response: AI响应内容
            
        Returns:
            tuple: (是否有效, 解析后的数据)
        """
        parse_result = self._parse_ai_response(response, "rewrite")
        return parse_result["success"], parse_result.get("data")
