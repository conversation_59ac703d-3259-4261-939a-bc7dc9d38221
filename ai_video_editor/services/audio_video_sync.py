#!/usr/bin/env python3
"""
音画同步服务

实现音频和视频的时长匹配和同步处理
"""

import os
import shutil
import logging
from typing import Dict, List, Tuple, Optional
from pathlib import Path

from ..common.models import BaseResult
from ..utils.video_utils import VideoUtils


class AudioVideoSyncService:
    """音画同步服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.video_utils = VideoUtils()
    
    def sync_audio_video(self,
                        audio_file: str,
                        subtitle_file: str,
                        video_segments_dir: str,
                        mapping_relations,  # 支持Dict或MappingRelation列表
                        output_dir: str) -> BaseResult:
        """
        执行音画同步
        
        Args:
            audio_file: 音频文件路径
            subtitle_file: 字幕文件路径
            video_segments_dir: 视频片段目录
            mapping_relations: 映射关系 {"rewritten_0": [0, 1], ...}
            output_dir: 输出目录
            
        Returns:
            BaseResult: 同步结果
        """
        try:
            self.logger.info("开始音画同步处理...")

            # 0. 标准化映射关系格式
            normalized_mapping = self._normalize_mapping_relations(mapping_relations)
            if not normalized_mapping:
                return BaseResult(
                    success=False,
                    message="映射关系格式化失败"
                )

            # 1. 解析字幕文件，计算每条字幕的总时长
            subtitle_durations = self._calculate_subtitle_durations(subtitle_file)
            if not subtitle_durations:
                return BaseResult(
                    success=False,
                    message="字幕时长计算失败"
                )
            
            # 2. 计算每条字幕匹配的视频片段总时长
            video_durations = self._calculate_video_durations(
                video_segments_dir, normalized_mapping
            )
            if not video_durations:
                return BaseResult(
                    success=False,
                    message="视频片段时长计算失败"
                )
            
            # 3. 创建输出目录结构
            output_structure = self._create_output_structure(output_dir)
            
            # 4. 复制并重命名音频和字幕文件
            self._copy_and_rename_audio_subtitle(
                audio_file, subtitle_file, output_structure
            )
            
            # 5. 处理视频片段（原始和变速）
            sync_result = self._process_video_segments(
                video_segments_dir,
                mapping_relations,
                subtitle_durations,
                video_durations,
                output_structure
            )
            
            if not sync_result.success:
                return sync_result
            
            self.logger.info("音画同步处理完成")
            
            return BaseResult(
                success=True,
                message="音画同步完成",
                metadata={
                    "output_dir": output_dir,
                    "subtitle_count": len(subtitle_durations),
                    "video_segments_count": len(video_durations),
                    "sync_results": sync_result.metadata
                }
            )
            
        except Exception as e:
            self.logger.error(f"音画同步失败: {e}")
            return BaseResult(
                success=False,
                message=f"音画同步异常: {str(e)}"
            )
    
    def _calculate_subtitle_durations(self, subtitle_file: str) -> Dict[int, float]:
        """
        计算每条字幕的总时长
        
        规则：
        - 第一条字幕：从0:00:00开始到下一条字幕开始时间点
        - 中间字幕：从当前字幕开始到下一条字幕开始时间点
        - 最后字幕：从最后字幕开始到音频结束时间点
        """
        try:
            durations = {}
            
            # 解析SRT文件
            subtitles = self._parse_srt_file(subtitle_file)
            if not subtitles:
                return {}
            
            # 获取音频总时长
            audio_duration = self._get_audio_duration_from_srt(subtitles)
            
            for i, subtitle in enumerate(subtitles):
                if i == 0:
                    # 第一条字幕：从0开始到下一条字幕开始
                    if len(subtitles) > 1:
                        duration = subtitles[1]['start_time'] - 0.0
                    else:
                        # 只有一条字幕，从0到音频结束
                        duration = audio_duration
                elif i == len(subtitles) - 1:
                    # 最后字幕：从当前字幕开始到音频结束
                    duration = audio_duration - subtitle['start_time']
                else:
                    # 中间字幕：从当前字幕开始到下一条字幕开始
                    duration = subtitles[i + 1]['start_time'] - subtitle['start_time']
                
                durations[i] = max(duration, 0.1)  # 最小0.1秒
                
            self.logger.info(f"计算字幕时长: {len(durations)} 条")
            return durations
            
        except Exception as e:
            self.logger.error(f"计算字幕时长失败: {e}")
            return {}
    
    def _calculate_video_durations(self, 
                                 video_segments_dir: str,
                                 mapping_relations: Dict[str, List[int]]) -> Dict[int, float]:
        """计算每条字幕匹配的视频片段总时长"""
        try:
            durations = {}
            
            for rewritten_key, original_indices in mapping_relations.items():
                # 处理不同格式的映射关系键
                if isinstance(rewritten_key, str):
                    if rewritten_key.startswith('rewritten_'):
                        rewritten_index = int(rewritten_key.split('_')[1])
                    else:
                        rewritten_index = int(rewritten_key)
                else:
                    rewritten_index = int(rewritten_key)
                
                total_duration = 0.0
                
                # 计算所有匹配视频片段的总时长
                for original_index in original_indices:
                    segment_file = os.path.join(
                        video_segments_dir, f"segment_{original_index:03d}.mp4"
                    )
                    
                    if os.path.exists(segment_file):
                        duration = self.video_utils.get_video_duration(segment_file)
                        total_duration += duration
                    else:
                        self.logger.warning(f"视频片段不存在: {segment_file}")
                
                durations[rewritten_index] = total_duration
                
            self.logger.info(f"计算视频时长: {len(durations)} 条")
            return durations
            
        except Exception as e:
            self.logger.error(f"计算视频时长失败: {e}")
            return {}
    
    def _create_output_structure(self, output_dir: str) -> Dict[str, str]:
        """创建输出目录结构"""
        try:
            structure = {
                "root": output_dir,
                "audio": os.path.join(output_dir, "audio"),
                "subtitle": os.path.join(output_dir, "subtitle"),
                "video_original": os.path.join(output_dir, "video_segments_original"),
                "video_synced": os.path.join(output_dir, "video_segments_synced")
            }
            
            # 创建所有目录
            for dir_path in structure.values():
                os.makedirs(dir_path, exist_ok=True)
            
            self.logger.info("输出目录结构创建完成")
            return structure
            
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {e}")
            return {}
    
    def _copy_and_rename_audio_subtitle(self, 
                                       audio_file: str,
                                       subtitle_file: str,
                                       output_structure: Dict[str, str]):
        """复制并重命名音频和字幕文件"""
        try:
            # 获取原文件名（不含扩展名）
            audio_name = Path(audio_file).stem
            subtitle_name = Path(subtitle_file).stem
            
            # 复制音频文件
            new_audio_name = f"New_{audio_name}.mp3"
            new_audio_path = os.path.join(output_structure["audio"], new_audio_name)
            shutil.copy2(audio_file, new_audio_path)
            
            # 复制字幕文件
            new_subtitle_name = f"New_{subtitle_name}.srt"
            new_subtitle_path = os.path.join(output_structure["subtitle"], new_subtitle_name)
            shutil.copy2(subtitle_file, new_subtitle_path)
            
            self.logger.info(f"音频和字幕文件重命名完成: {new_audio_name}, {new_subtitle_name}")
            
        except Exception as e:
            self.logger.error(f"复制重命名音频字幕失败: {e}")
            raise
    
    def _parse_srt_file(self, subtitle_file: str) -> List[Dict]:
        """解析SRT文件"""
        try:
            subtitles = []
            
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单的SRT解析
            blocks = content.strip().split('\n\n')
            
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 解析时间码
                    time_line = lines[1]
                    if ' --> ' in time_line:
                        start_str, end_str = time_line.split(' --> ')
                        start_time = self._parse_time_code(start_str)
                        end_time = self._parse_time_code(end_str)
                        
                        # 文本内容
                        text = '\n'.join(lines[2:])
                        
                        subtitles.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'text': text
                        })
            
            return subtitles
            
        except Exception as e:
            self.logger.error(f"解析SRT文件失败: {e}")
            return []
    
    def _parse_time_code(self, time_str: str) -> float:
        """解析时间码为秒数"""
        try:
            # 格式: HH:MM:SS,mmm
            time_part, ms_part = time_str.split(',')
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)
            
            return h * 3600 + m * 60 + s + ms / 1000.0
            
        except Exception as e:
            self.logger.error(f"解析时间码失败: {time_str}, {e}")
            return 0.0
    
    def _get_audio_duration_from_srt(self, subtitles: List[Dict]) -> float:
        """从字幕中获取音频总时长"""
        if not subtitles:
            return 0.0
        
        # 使用最后一条字幕的结束时间作为音频总时长
        return subtitles[-1]['end_time']

    def _normalize_mapping_relations(self, mapping_relations) -> Dict[str, List[int]]:
        """标准化映射关系格式（支持MappingRelation对象列表和字典格式）"""
        try:
            normalized_mapping = {}

            # 处理MappingRelation对象列表
            if isinstance(mapping_relations, list):
                self.logger.info("处理MappingRelation对象列表格式")
                for mapping_obj in mapping_relations:
                    if hasattr(mapping_obj, 'rewritten_index') and hasattr(mapping_obj, 'original_indices'):
                        key = f"rewritten_{mapping_obj.rewritten_index}"
                        normalized_mapping[key] = mapping_obj.original_indices
                        self.logger.debug(f"映射: {key} -> {mapping_obj.original_indices}")
                    else:
                        self.logger.warning(f"无效的MappingRelation对象: {mapping_obj}")

            # 处理字典格式
            elif isinstance(mapping_relations, dict):
                self.logger.info("处理字典格式映射关系")
                for key, value in mapping_relations.items():
                    # 统一键格式
                    if isinstance(key, int):
                        normalized_key = f"rewritten_{key}"
                    elif isinstance(key, str):
                        if key.startswith('rewritten_'):
                            normalized_key = key
                        else:
                            normalized_key = f"rewritten_{key}"
                    else:
                        normalized_key = f"rewritten_{key}"

                    # 确保值是列表
                    if isinstance(value, list):
                        normalized_value = value
                    else:
                        normalized_value = [value]

                    normalized_mapping[normalized_key] = normalized_value
                    self.logger.debug(f"映射: {normalized_key} -> {normalized_value}")

            else:
                self.logger.error(f"不支持的映射关系格式: {type(mapping_relations)}")
                return {}

            self.logger.info(f"标准化映射关系完成: {len(normalized_mapping)} 个映射")
            return normalized_mapping

        except Exception as e:
            self.logger.error(f"映射关系标准化失败: {e}")
            return {}

    def _process_video_segments(self,
                              video_segments_dir: str,
                              mapping_relations: Dict[str, List[int]],
                              subtitle_durations: Dict[int, float],
                              video_durations: Dict[int, float],
                              output_structure: Dict[str, str]) -> BaseResult:
        """处理视频片段（原始和变速）"""
        try:
            sync_results = []

            for rewritten_key, original_indices in mapping_relations.items():
                # 处理不同格式的映射关系键
                if isinstance(rewritten_key, str):
                    if rewritten_key.startswith('rewritten_'):
                        rewritten_index = int(rewritten_key.split('_')[1])
                    else:
                        rewritten_index = int(rewritten_key)
                else:
                    rewritten_index = int(rewritten_key)

                # 获取目标时长和当前时长
                target_duration = subtitle_durations.get(rewritten_index, 0.0)
                current_duration = video_durations.get(rewritten_index, 0.0)

                if current_duration == 0.0:
                    self.logger.warning(f"字幕{rewritten_index}没有匹配的视频片段")
                    continue

                # 计算变速比例
                speed_factor = current_duration / target_duration if target_duration > 0 else 1.0

                # 处理每个匹配的视频片段
                segment_results = []

                for i, original_index in enumerate(original_indices):
                    segment_file = os.path.join(
                        video_segments_dir, f"segment_{original_index:03d}.mp4"
                    )

                    if not os.path.exists(segment_file):
                        self.logger.warning(f"视频片段不存在: {segment_file}")
                        continue

                    # 复制原始视频片段
                    original_name = f"{rewritten_index + 1}_{Path(segment_file).stem}.mp4"
                    original_output = os.path.join(
                        output_structure["video_original"], original_name
                    )
                    shutil.copy2(segment_file, original_output)

                    # 创建变速视频片段
                    synced_name = f"{rewritten_index + 1}.mp4" if len(original_indices) == 1 else f"{rewritten_index + 1}_{i + 1}.mp4"
                    synced_output = os.path.join(
                        output_structure["video_synced"], synced_name
                    )

                    # 应用变速处理
                    speed_result = self._apply_speed_change(
                        segment_file, synced_output, speed_factor
                    )

                    if speed_result.success:
                        segment_results.append({
                            "original_index": original_index,
                            "original_file": original_output,
                            "synced_file": synced_output,
                            "speed_factor": speed_factor,
                            "original_duration": self.video_utils.get_video_duration(segment_file),
                            "synced_duration": self.video_utils.get_video_duration(synced_output)
                        })
                    else:
                        self.logger.error(f"变速处理失败: {speed_result.message}")

                sync_results.append({
                    "rewritten_index": rewritten_index,
                    "target_duration": target_duration,
                    "original_duration": current_duration,
                    "speed_factor": speed_factor,
                    "segments": segment_results
                })

                self.logger.info(
                    f"字幕{rewritten_index}: 目标{target_duration:.2f}s, "
                    f"原始{current_duration:.2f}s, 变速{speed_factor:.2f}x"
                )

            return BaseResult(
                success=True,
                message="视频片段处理完成",
                metadata={"sync_results": sync_results}
            )

        except Exception as e:
            self.logger.error(f"处理视频片段失败: {e}")
            return BaseResult(
                success=False,
                message=f"视频片段处理异常: {str(e)}"
            )

    def _apply_speed_change(self, input_file: str, output_file: str, speed_factor: float) -> BaseResult:
        """应用变速处理"""
        try:
            # 使用VideoUtils进行变速处理
            success = self.video_utils.change_video_speed(
                input_file, output_file, speed_factor
            )

            if success:
                return BaseResult(
                    success=True,
                    message=f"变速处理完成: {speed_factor:.2f}x"
                )
            else:
                return BaseResult(
                    success=False,
                    message="变速处理失败"
                )

        except Exception as e:
            self.logger.error(f"变速处理异常: {e}")
            return BaseResult(
                success=False,
                message=f"变速处理异常: {str(e)}"
            )
