"""
Main Window - 主窗口

AI视频编辑软件的完整用户界面
"""

import sys
import os
import asyncio
import uuid
from typing import Optional, Dict, Any, List
from pathlib import Path

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QPushButton, QTextEdit, QProgressBar,
    QFileDialog, QComboBox, QGroupBox, QSplitter, QTabWidget,
    QListWidget, QListWidgetItem, QMessageBox, QStatusBar,
    QMenuBar, QMenu, QToolBar, QFrame, QScrollArea, QLineEdit,
    QSpinBox, QSlider, QCheckBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QStyledItemDelegate, QProgressDialog,
    QHeaderView, Q<PERSON><PERSON><PERSON><PERSON>t, QTreeWidgetItem, QRadioButton, QComboBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize, QEvent
from PyQt6.QtGui import QFont, QIcon, QPixmap, QAction, QPalette, QColor

from config.config_manager import ConfigManager
from ..workflow.workflow_manager import WorkflowManager
from ..workflow.workflow_engine import WorkflowProgress, WorkflowStatus
from ..common.models import ProcessingError
from ..utils.logger import get_default_logger


class SubtitleTextDelegate(QStyledItemDelegate):
    """自定义字幕文本编辑器委托"""

    def createEditor(self, parent, option, index):
        """创建编辑器"""
        if index.column() == 1:  # 只对新字幕列使用自定义编辑器
            editor = QTextEdit(parent)

            # 设置深色主题样式
            editor.setStyleSheet("""
                QTextEdit {
                    background-color: #2b2b2b;
                    color: white;
                    border: 2px solid #4a90e2;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 12px;
                    line-height: 1.4;
                }
                QTextEdit:focus {
                    border: 2px solid #66b3ff;
                    background-color: #333333;
                }
            """)

            # 设置自动换行（使用正确的PyQt6 API）
            editor.setLineWrapMode(QTextEdit.LineWrapMode.WidgetWidth)

            # 注意：QTextEdit没有setWordWrapMode方法，自动换行由LineWrapMode控制

            # 设置最小高度以显示多行内容
            editor.setMinimumHeight(80)
            editor.setMaximumHeight(120)

            return editor

        return super().createEditor(parent, option, index)

    def setEditorData(self, editor, index):
        """设置编辑器数据"""
        if isinstance(editor, QTextEdit):
            # 提取纯文本内容（移除索引前缀）
            text = index.model().data(index, Qt.ItemDataRole.DisplayRole)
            if text and '] ' in text:
                text = text.split('] ', 1)[1]  # 移除 [索引] 前缀
            editor.setPlainText(text or "")
        else:
            super().setEditorData(editor, index)

    def setModelData(self, editor, model, index):
        """将编辑器数据设置回模型"""
        if isinstance(editor, QTextEdit):
            # 获取编辑后的文本
            new_text = editor.toPlainText().strip()

            # 重新添加索引前缀
            original_text = model.data(index, Qt.ItemDataRole.DisplayRole)
            if original_text and '] ' in original_text:
                prefix = original_text.split('] ', 1)[0] + '] '
                new_text = prefix + new_text

            model.setData(index, new_text, Qt.ItemDataRole.EditRole)
        else:
            super().setModelData(editor, model, index)

    def updateEditorGeometry(self, editor, option, index):
        """更新编辑器几何形状"""
        if isinstance(editor, QTextEdit):
            # 为多行编辑器提供更大的空间
            rect = option.rect
            rect.setHeight(max(80, rect.height()))
            editor.setGeometry(rect)
        else:
            super().updateEditorGeometry(editor, option, index)


class TTSAndMatchWorkflowThread(QThread):
    """TTS合成和音画匹配工作流程线程"""
    progress_updated = pyqtSignal(float, str)
    workflow_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, subtitles, video_file: str, original_subtitles,
                 subtitle_mapping: Dict[str, List[int]], output_dir: str):
        super().__init__()
        self.subtitles = subtitles
        self.video_file = video_file
        self.original_subtitles = original_subtitles
        self.subtitle_mapping = subtitle_mapping
        self.output_dir = output_dir

        # 初始化logger
        import logging
        self.logger = logging.getLogger(__name__)

    def update_progress(self, progress: float, message: str):
        """更新进度"""
        self.progress_updated.emit(progress, message)

    def run(self):
        """执行TTS合成和音画匹配工作流程"""
        try:
            import time
            start_time = time.time()

            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)

            # 第一步：TTS语音合成
            self.progress_updated.emit(10.0, "开始TTS语音合成...")

            tts_result = self._perform_tts_synthesis()
            if not tts_result['success']:
                self.error_occurred.emit(f"TTS合成失败: {tts_result['message']}")
                return

            self.progress_updated.emit(40.0, "TTS合成完成，开始视频处理...")

            # 第二步：视频预处理
            video_result = self._perform_video_processing()
            if not video_result['success']:
                self.error_occurred.emit(f"视频处理失败: {video_result['message']}")
                return

            self.progress_updated.emit(70.0, "视频处理完成，开始音画匹配...")

            # 第三步：音画匹配
            match_result = self._perform_audio_video_matching(
                tts_result, video_result
            )
            if not match_result['success']:
                self.error_occurred.emit(f"音画匹配失败: {match_result['message']}")
                return

            self.progress_updated.emit(90.0, "生成最终输出...")

            # 第四步：生成最终输出
            final_result = self._generate_final_output(
                tts_result, match_result
            )

            self.progress_updated.emit(100.0, "工作流程完成")

            # 返回完整结果
            processing_time = time.time() - start_time
            result = {
                "success": True,
                "audio_file": tts_result.get('audio_file'),
                "subtitle_file": tts_result.get('subtitle_file'),
                "match_data": match_result.get('match_data', []),
                "match_count": len(match_result.get('match_data', [])),
                "output_dir": self.output_dir,
                "processing_time": processing_time
            }

            self.workflow_completed.emit(result)

        except Exception as e:
            self.error_occurred.emit(f"工作流程异常: {str(e)}")

    def _perform_tts_synthesis(self) -> dict:
        """执行TTS语音合成"""
        try:
            from ..services.aliyun_tts_client import AliyunTTSClient
            from config.config_manager import ConfigManager
            import asyncio

            # 获取TTS配置
            config_manager = ConfigManager()
            api_config = config_manager.get_api_config()
            tts_config = api_config.get("aliyun_tts", {})

            # 创建TTS客户端
            tts_client = AliyunTTSClient(tts_config)

            # 生成输出文件路径
            import time
            timestamp = int(time.time())
            audio_file = os.path.join(self.output_dir, f"tts_audio_{timestamp}.mp3")
            subtitle_file = os.path.join(self.output_dir, f"tts_subtitles_{timestamp}.srt")

            # 准备TTS文本内容
            if not hasattr(self.subtitles, 'items') or not self.subtitles.items:
                return {
                    "success": False,
                    "message": "没有字幕数据进行TTS合成"
                }

            # 提取所有字幕文本进行统一合成
            all_text = ""
            subtitle_segments = []
            current_time = 0.6  # 从0.6秒开始（静音前缀）

            for i, item in enumerate(self.subtitles.items):
                text = item.text.strip()
                if text:
                    all_text += text

                    # 估算音频时长（每个字符约0.15秒）
                    estimated_duration = len(text) * 0.15

                    # 记录字幕段信息
                    subtitle_segments.append({
                        'index': i,
                        'text': text,
                        'start_time': current_time,
                        'end_time': current_time + estimated_duration,
                        'duration': estimated_duration
                    })

                    current_time += estimated_duration

                    # 段落间添加短暂停顿
                    if i < len(self.subtitles.items) - 1:
                        current_time += 0.3

            if not all_text:
                return {
                    "success": False,
                    "message": "字幕文本为空，无法进行TTS合成"
                }

            # 执行TTS合成
            try:
                # 在工作线程中运行异步TTS合成
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                tts_result = loop.run_until_complete(
                    tts_client.synthesize_text(all_text, audio_file)
                )

                loop.close()

                if not tts_result.success:
                    return {
                        "success": False,
                        "message": f"TTS合成失败: {tts_result.message}"
                    }

                # 获取实际音频时长
                actual_duration = tts_result.metadata.get('duration', current_time)

                # 根据实际音频时长重新计算字幕段时长
                if actual_duration > 0 and len(subtitle_segments) > 0:
                    subtitle_segments = self._recalculate_subtitle_timings(subtitle_segments, actual_duration)

                # 生成SRT字幕文件
                self._generate_srt_file(subtitle_segments, subtitle_file, actual_duration)

                return {
                    "success": True,
                    "audio_file": audio_file,
                    "subtitle_file": subtitle_file,
                    "total_duration": actual_duration,
                    "subtitle_segments": subtitle_segments,
                    "message": "TTS合成完成"
                }

            except Exception as tts_e:
                return {
                    "success": False,
                    "message": f"TTS合成调用失败: {str(tts_e)}"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"TTS合成异常: {str(e)}"
            }

    def _generate_srt_file(self, subtitle_segments: list, subtitle_file: str, total_duration: float):
        """生成SRT字幕文件"""
        try:
            with open(subtitle_file, 'w', encoding='utf-8') as f:
                for i, segment in enumerate(subtitle_segments):
                    # SRT格式：序号
                    f.write(f"{i + 1}\n")

                    # SRT格式：时间码
                    start_time = self._seconds_to_srt_time(segment['start_time'])
                    end_time = self._seconds_to_srt_time(segment['end_time'])
                    f.write(f"{start_time} --> {end_time}\n")

                    # SRT格式：字幕文本
                    f.write(f"{segment['text']}\n\n")

        except Exception as e:
            raise Exception(f"生成SRT文件失败: {str(e)}")

    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:03d},{millisecs:03d}"

    def _recalculate_subtitle_timings(self, subtitle_segments: list, actual_duration: float) -> list:
        """根据实际音频时长重新计算字幕段时长"""
        try:
            if not subtitle_segments or actual_duration <= 0:
                return subtitle_segments

            # 计算总的估算时长（不包括静音前缀）
            total_estimated = sum(seg['duration'] for seg in subtitle_segments)

            # 实际可用时长（减去0.6秒静音前缀）
            available_duration = actual_duration - 0.6

            if total_estimated <= 0:
                return subtitle_segments

            # 计算缩放因子
            scale_factor = available_duration / total_estimated

            # 重新计算每个字幕段的时长
            current_time = 0.6  # 从静音前缀结束开始

            for segment in subtitle_segments:
                # 缩放时长
                scaled_duration = segment['duration'] * scale_factor

                # 更新时间信息
                segment['start_time'] = current_time
                segment['end_time'] = current_time + scaled_duration
                segment['duration'] = scaled_duration

                current_time += scaled_duration

                # 段落间停顿也要缩放
                if segment != subtitle_segments[-1]:  # 不是最后一个
                    current_time += 0.3 * scale_factor

            self.logger.info(f"重新计算字幕时长: 估算{total_estimated:.2f}s → 实际{available_duration:.2f}s (缩放{scale_factor:.3f})")

            return subtitle_segments

        except Exception as e:
            self.logger.error(f"重新计算字幕时长失败: {e}")
            return subtitle_segments

    def _perform_video_processing(self) -> dict:
        """执行视频预处理"""
        try:
            from ..services.video_processor import VideoProcessor

            processor = VideoProcessor()

            # 清除视频音频
            temp_video_dir = os.path.join(self.output_dir, "temp_video")
            os.makedirs(temp_video_dir, exist_ok=True)

            silent_video = os.path.join(temp_video_dir, "silent_video.mp4")
            audio_removal_result = processor.remove_audio_from_video(
                self.video_file, silent_video
            )

            if not audio_removal_result.success:
                return {
                    "success": False,
                    "message": f"视频音频清除失败: {audio_removal_result.message}"
                }

            # 根据原字幕分割视频
            if self.original_subtitles and hasattr(self.original_subtitles, 'items'):
                subtitles_data = []
                for item in self.original_subtitles.items:
                    # 将TimeCode转换为float秒数，处理None值
                    if item.start_time is None:
                        start_seconds = 0.0
                    elif hasattr(item.start_time, 'to_seconds'):
                        start_seconds = item.start_time.to_seconds()
                    else:
                        start_seconds = float(item.start_time) if item.start_time is not None else 0.0

                    if item.end_time is None:
                        end_seconds = start_seconds + 2.0  # 默认2秒时长
                    elif hasattr(item.end_time, 'to_seconds'):
                        end_seconds = item.end_time.to_seconds()
                    else:
                        end_seconds = float(item.end_time) if item.end_time is not None else start_seconds + 2.0

                    subtitles_data.append({
                        'start_time': start_seconds,
                        'end_time': end_seconds,
                        'text': item.text
                    })

                segments_dir = os.path.join(temp_video_dir, "segments")
                split_result = processor.split_video_by_subtitles(
                    silent_video, subtitles_data, segments_dir
                )

                if split_result.success:
                    return {
                        "success": True,
                        "segments": split_result.metadata.get('segments', []),
                        "segments_dir": segments_dir,
                        "message": "视频处理完成"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"视频分割失败: {split_result.message}"
                    }
            else:
                return {
                    "success": False,
                    "message": "没有原字幕数据进行视频分割"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"视频处理异常: {str(e)}"
            }

    def _perform_audio_video_matching(self, tts_result: dict, video_result: dict) -> dict:
        """执行音画匹配"""
        try:
            from ..services.audio_video_matcher import AudioVideoMatcher

            matcher = AudioVideoMatcher()

            # 准备新字幕数据
            new_subtitles = []
            if hasattr(self.subtitles, 'items'):
                for i, item in enumerate(self.subtitles.items):
                    # 将TimeCode转换为float秒数，处理None值
                    if item.start_time is None:
                        start_seconds = 0.0
                    elif hasattr(item.start_time, 'to_seconds'):
                        start_seconds = item.start_time.to_seconds()
                    else:
                        start_seconds = float(item.start_time) if item.start_time is not None else 0.0

                    if item.end_time is None:
                        end_seconds = start_seconds + 2.0  # 默认2秒时长
                    elif hasattr(item.end_time, 'to_seconds'):
                        end_seconds = item.end_time.to_seconds()
                    else:
                        end_seconds = float(item.end_time) if item.end_time is not None else start_seconds + 2.0

                    new_subtitles.append({
                        'start_time': start_seconds,
                        'end_time': end_seconds,
                        'text': item.text
                    })

            # 准备原字幕数据
            old_subtitles = []
            if self.original_subtitles and hasattr(self.original_subtitles, 'items'):
                for item in self.original_subtitles.items:
                    # 将TimeCode转换为float秒数，处理None值
                    if item.start_time is None:
                        start_seconds = 0.0
                    elif hasattr(item.start_time, 'to_seconds'):
                        start_seconds = item.start_time.to_seconds()
                    else:
                        start_seconds = float(item.start_time) if item.start_time is not None else 0.0

                    if item.end_time is None:
                        end_seconds = start_seconds + 2.0  # 默认2秒时长
                    elif hasattr(item.end_time, 'to_seconds'):
                        end_seconds = item.end_time.to_seconds()
                    else:
                        end_seconds = float(item.end_time) if item.end_time is not None else start_seconds + 2.0

                    old_subtitles.append({
                        'start_time': start_seconds,
                        'end_time': end_seconds,
                        'text': item.text
                    })

            # 执行匹配
            match_output_dir = os.path.join(self.output_dir, "match_results")
            match_result = matcher.match_audio_video_segments(
                new_subtitles=new_subtitles,
                old_subtitles=old_subtitles,
                subtitle_mapping=self.subtitle_mapping,
                video_segments=video_result.get('segments', []),
                audio_file=tts_result.get('audio_file', ''),
                output_dir=match_output_dir
            )

            if match_result.success:
                # 获取匹配的片段对象
                matched_segments = match_result.metadata.get('matched_segments', [])

                # 创建预览数据
                preview_data = matcher.create_preview_data(
                    matched_segments,
                    match_result.metadata.get('audio_segments_dir', ''),
                    match_result.metadata.get('video_segments_dir', '')
                )

                # 执行音画同步
                self.update_progress(90, "执行音画同步...")

                sync_result = self._perform_audio_video_sync(
                    tts_result, video_result, match_result
                )

                if sync_result["success"]:
                    self.update_progress(100, "音画同步完成！")

                    return {
                        "success": True,
                        "match_data": preview_data,
                        "matched_segments": matched_segments,
                        "sync_data": sync_result,
                        "message": "TTS合成、音画匹配和同步完成"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"音画同步失败: {sync_result['message']}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"音画匹配失败: {match_result.message}"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"音画匹配异常: {str(e)}"
            }

    def _generate_final_output(self, tts_result: dict, match_result: dict) -> dict:
        """生成最终输出"""
        try:
            # 这里可以添加最终输出的生成逻辑
            # 比如重命名文件、创建目录结构等

            return {
                "success": True,
                "message": "最终输出生成完成"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"最终输出生成异常: {str(e)}"
            }

    def _perform_audio_video_sync(self, tts_result: dict, video_result: dict, match_result) -> dict:
        """执行音画同步"""
        try:
            from ..services.audio_video_sync import AudioVideoSyncService

            # 获取必要的文件路径
            audio_file = tts_result.get("audio_file") or tts_result.get("data", {}).get("audio_file")
            subtitle_file = tts_result.get("subtitle_file") or tts_result.get("data", {}).get("subtitle_file")

            # 多种方式获取视频片段目录
            video_segments_dir = (
                video_result.get("output_dir") or
                video_result.get("metadata", {}).get("output_dir") or
                video_result.get("data", {}).get("output_dir") or
                (hasattr(video_result, 'metadata') and video_result.metadata.get("output_dir")) or
                os.path.join(self.output_dir, "video_segments")  # 默认路径
            )

            self.logger.info(f"音画同步文件路径检查:")
            self.logger.info(f"  audio_file: {audio_file}")
            self.logger.info(f"  subtitle_file: {subtitle_file}")
            self.logger.info(f"  video_segments_dir: {video_segments_dir}")

            if not all([audio_file, subtitle_file, video_segments_dir]):
                missing = []
                if not audio_file: missing.append("audio_file")
                if not subtitle_file: missing.append("subtitle_file")
                if not video_segments_dir: missing.append("video_segments_dir")

                return {
                    "success": False,
                    "message": f"缺少必要的文件路径: {', '.join(missing)}"
                }

            # 获取映射关系，确保与AI编辑界面一致
            mapping_relations = self._get_current_mapping()
            if not mapping_relations:
                return {
                    "success": False,
                    "message": "没有找到映射关系"
                }

            self.logger.info(f"使用的映射关系: {mapping_relations}")

            # 创建同步输出目录
            sync_output_dir = os.path.join(self.output_dir, "final_output")

            # 执行音画同步
            sync_service = AudioVideoSyncService()
            sync_result = sync_service.sync_audio_video(
                audio_file=audio_file,
                subtitle_file=subtitle_file,
                video_segments_dir=video_segments_dir,
                mapping_relations=mapping_relations,
                output_dir=sync_output_dir
            )

            if sync_result.success:
                return {
                    "success": True,
                    "output_dir": sync_output_dir,
                    "sync_results": sync_result.metadata,
                    "message": "音画同步完成"
                }
            else:
                return {
                    "success": False,
                    "message": sync_result.message
                }

        except Exception as e:
            self.logger.error(f"音画同步执行失败: {e}")
            return {
                "success": False,
                "message": f"音画同步异常: {str(e)}"
            }

    def _get_current_mapping(self) -> Dict[str, List[int]]:
        """获取当前的字幕映射关系（支持MappingRelation对象列表和字典格式）"""
        try:
            self.logger.info("开始获取映射关系...")

            # 优先使用current_mappings（来自AI改写结果）
            if hasattr(self, 'current_mappings') and self.current_mappings:
                self.logger.info(f"使用current_mappings: {type(self.current_mappings)}")
                return self._normalize_mapping_format(self.current_mappings)

            # 其次使用subtitle_mapping（传入参数）
            if hasattr(self, 'subtitle_mapping') and self.subtitle_mapping:
                self.logger.info(f"使用subtitle_mapping: {type(self.subtitle_mapping)}")
                return self._normalize_mapping_format(self.subtitle_mapping)

            # 如果没有映射关系，创建默认的1:1映射
            self.logger.warning("未找到映射关系，创建默认1:1映射")
            mapping = {}
            if hasattr(self, 'subtitles') and self.subtitles:
                subtitle_count = len(self.subtitles.items)
                for i in range(subtitle_count):
                    mapping[f"rewritten_{i}"] = [i]
                self.logger.info(f"默认映射关系: {mapping}")

            return mapping

        except Exception as e:
            self.logger.error(f"获取映射关系失败: {e}")
            return {}

    def _normalize_mapping_format(self, mapping_data) -> Dict[str, List[int]]:
        """统一映射关系格式（支持MappingRelation对象列表和字典格式）"""
        try:
            normalized_mapping = {}

            # 处理MappingRelation对象列表
            if isinstance(mapping_data, list):
                self.logger.info("处理MappingRelation对象列表格式")
                for mapping_obj in mapping_data:
                    if hasattr(mapping_obj, 'rewritten_index') and hasattr(mapping_obj, 'original_indices'):
                        key = f"rewritten_{mapping_obj.rewritten_index}"
                        normalized_mapping[key] = mapping_obj.original_indices
                    else:
                        self.logger.warning(f"无效的MappingRelation对象: {mapping_obj}")

            # 处理字典格式
            elif isinstance(mapping_data, dict):
                self.logger.info("处理字典格式")
                for key, value in mapping_data.items():
                    # 统一键格式
                    if isinstance(key, int):
                        normalized_key = f"rewritten_{key}"
                    elif isinstance(key, str):
                        if key.startswith('rewritten_'):
                            normalized_key = key
                        else:
                            normalized_key = f"rewritten_{key}"
                    else:
                        normalized_key = f"rewritten_{key}"

                    # 确保值是列表
                    if isinstance(value, list):
                        normalized_value = value
                    else:
                        normalized_value = [value]

                    normalized_mapping[normalized_key] = normalized_value

            else:
                self.logger.error(f"不支持的映射关系格式: {type(mapping_data)}")
                return {}

            self.logger.info(f"标准化映射关系: {normalized_mapping}")
            return normalized_mapping

        except Exception as e:
            self.logger.error(f"映射关系格式标准化失败: {e}")
            return {}


class VideoSyncThread(QThread):
    """视频同步工作线程"""
    progress_updated = pyqtSignal(float, str)
    sync_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, audio_path: str, video_path: str, subtitle_path: str,
                 output_dir: str, sync_mode: str):
        super().__init__()
        self.audio_path = audio_path
        self.video_path = video_path
        self.subtitle_path = subtitle_path
        self.output_dir = output_dir
        self.sync_mode = sync_mode

    def run(self):
        """执行视频同步"""
        try:
            import time
            start_time = time.time()

            self.progress_updated.emit(10.0, "分析音频文件...")

            # 获取音频信息
            audio_info = self._get_media_info(self.audio_path)
            if not audio_info:
                self.error_occurred.emit("无法获取音频文件信息")
                return

            self.progress_updated.emit(20.0, "分析视频文件...")

            # 获取视频信息
            video_info = self._get_media_info(self.video_path)
            if not video_info:
                self.error_occurred.emit("无法获取视频文件信息")
                return

            audio_duration = audio_info['duration']
            video_duration = video_info['duration']

            self.progress_updated.emit(30.0, "准备处理参数...")

            # 生成输出文件名
            import time
            timestamp = int(time.time())
            output_filename = f"synced_video_{timestamp}.mp4"
            output_path = os.path.join(self.output_dir, output_filename)

            self.progress_updated.emit(40.0, "开始视频处理...")

            # 根据同步模式处理视频
            if self.sync_mode == "auto":
                success = self._process_auto_speed(output_path, audio_duration, video_duration)
            elif self.sync_mode == "crop":
                success = self._process_crop_video(output_path, audio_duration)
            else:  # loop
                success = self._process_loop_video(output_path, audio_duration, video_duration)

            if not success:
                self.error_occurred.emit("视频处理失败")
                return

            self.progress_updated.emit(90.0, "添加字幕...")

            # 如果有字幕文件，添加字幕
            if self.subtitle_path and os.path.exists(self.subtitle_path):
                final_output = output_path.replace('.mp4', '_with_subtitle.mp4')
                if self._add_subtitle(output_path, final_output):
                    output_path = final_output

            self.progress_updated.emit(100.0, "处理完成")

            # 返回结果
            processing_time = time.time() - start_time
            result = {
                "success": True,
                "output_file": output_path,
                "processing_time": processing_time,
                "output_duration": audio_duration,
                "sync_mode": self.sync_mode
            }

            self.sync_completed.emit(result)

        except Exception as e:
            self.error_occurred.emit(f"视频同步异常: {str(e)}")

    def _get_media_info(self, file_path: str) -> dict:
        """获取媒体文件信息"""
        try:
            import subprocess
            import json

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', file_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return {
                    'duration': float(data['format']['duration']),
                    'size': int(data['format']['size'])
                }
            return None

        except Exception:
            return None

    def _process_auto_speed(self, output_path: str, audio_duration: float, video_duration: float) -> bool:
        """自动调速处理"""
        try:
            import subprocess

            speed_ratio = video_duration / audio_duration

            self.progress_updated.emit(50.0, f"调整视频速度 {speed_ratio:.2f}x...")

            cmd = [
                'ffmpeg', '-y',
                '-i', self.video_path,
                '-i', self.audio_path,
                '-filter:v', f'setpts={1/speed_ratio}*PTS',
                '-map', '1:a',  # 使用音频文件的音频
                '-map', '0:v',  # 使用视频文件的视频
                '-c:a', 'aac',
                '-c:v', 'libx264',
                '-shortest',
                output_path
            ]

            process = subprocess.run(cmd, capture_output=True, text=True)
            return process.returncode == 0

        except Exception:
            return False

    def _process_crop_video(self, output_path: str, audio_duration: float) -> bool:
        """裁剪视频处理"""
        try:
            import subprocess

            self.progress_updated.emit(50.0, f"裁剪视频到 {audio_duration:.2f} 秒...")

            cmd = [
                'ffmpeg', '-y',
                '-i', self.video_path,
                '-i', self.audio_path,
                '-t', str(audio_duration),
                '-map', '1:a',  # 使用音频文件的音频
                '-map', '0:v',  # 使用视频文件的视频
                '-c:a', 'aac',
                '-c:v', 'libx264',
                output_path
            ]

            process = subprocess.run(cmd, capture_output=True, text=True)
            return process.returncode == 0

        except Exception:
            return False

    def _process_loop_video(self, output_path: str, audio_duration: float, video_duration: float) -> bool:
        """循环视频处理"""
        try:
            import subprocess

            loop_count = int(audio_duration / video_duration) + 1

            self.progress_updated.emit(50.0, f"循环视频 {loop_count} 次...")

            cmd = [
                'ffmpeg', '-y',
                '-stream_loop', str(loop_count),
                '-i', self.video_path,
                '-i', self.audio_path,
                '-t', str(audio_duration),
                '-map', '1:a',  # 使用音频文件的音频
                '-map', '0:v',  # 使用视频文件的视频
                '-c:a', 'aac',
                '-c:v', 'libx264',
                output_path
            ]

            process = subprocess.run(cmd, capture_output=True, text=True)
            return process.returncode == 0

        except Exception:
            return False

    def _add_subtitle(self, input_path: str, output_path: str) -> bool:
        """添加字幕"""
        try:
            import subprocess

            self.progress_updated.emit(95.0, "添加字幕...")

            cmd = [
                'ffmpeg', '-y',
                '-i', input_path,
                '-vf', f'subtitles={self.subtitle_path}',
                '-c:a', 'copy',
                output_path
            ]

            process = subprocess.run(cmd, capture_output=True, text=True)
            return process.returncode == 0

        except Exception:
            return False


class DirectTTSWorkflowThread(QThread):
    """直接TTS合成工作流程线程"""
    progress_updated = pyqtSignal(float, str)  # progress, message
    tts_completed = pyqtSignal(dict)  # result
    error_occurred = pyqtSignal(str)  # error_message

    def __init__(self, subtitles, voice_code, output_dir, config_manager):
        super().__init__()
        self.subtitles = subtitles
        self.voice_code = voice_code
        self.output_dir = output_dir
        self.config_manager = config_manager
        self.logger = get_default_logger()

    def run(self):
        """执行直接TTS合成"""
        try:
            self.progress_updated.emit(5.0, "初始化TTS客户端...")

            # 获取TTS配置
            api_config = self.config_manager.get_api_config()
            tts_config = api_config.get("aliyun_tts", {})

            # 临时修改语音配置
            original_voice = tts_config.get('voice')
            tts_config['voice'] = self.voice_code

            # 创建TTS客户端
            from ..services.aliyun_tts_client import AliyunTTSClient
            tts_client = AliyunTTSClient(tts_config)

            self.progress_updated.emit(10.0, "开始分段TTS合成...")

            # 创建临时目录
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="direct_tts_synthesis_")

            # 执行分段TTS合成
            import asyncio
            result = asyncio.run(self._perform_direct_tts_synthesis(tts_client, temp_dir))

            # 恢复原始语音配置
            if original_voice:
                tts_config['voice'] = original_voice

            if result["success"]:
                self.tts_completed.emit(result)
            else:
                self.error_occurred.emit(result["message"])

        except Exception as e:
            self.error_occurred.emit(f"直接TTS合成异常: {str(e)}")

    async def _perform_direct_tts_synthesis(self, tts_client, temp_dir):
        """执行直接TTS合成"""
        try:
            audio_segments = []
            total_duration = 0.0

            for i, subtitle_item in enumerate(self.subtitles.items):
                progress = 15.0 + (i / len(self.subtitles.items)) * 70.0
                self.progress_updated.emit(
                    progress,
                    f"合成第 {i+1}/{len(self.subtitles.items)} 段: {subtitle_item.text[:20]}..."
                )

                # 为每个字幕段生成音频
                segment_output_path = os.path.join(temp_dir, f"segment_{i+1:03d}.mp3")

                result = await tts_client.synthesize_text(
                    text=subtitle_item.text,
                    output_file=segment_output_path
                )

                if result.success:
                    # 计算时间码
                    start_time = total_duration
                    end_time = total_duration + result.duration

                    # 更新字幕项的时间码
                    subtitle_item.start_time = self._seconds_to_timecode(start_time)
                    subtitle_item.end_time = self._seconds_to_timecode(end_time)

                    audio_segments.append({
                        "file": segment_output_path,
                        "duration": result.duration,
                        "start_time": start_time,
                        "end_time": end_time,
                        "subtitle_index": subtitle_item.index
                    })

                    total_duration += result.duration
                else:
                    return {
                        "success": False,
                        "message": f"第 {i+1} 段音频合成失败: {result.message}"
                    }

            # 合并音频
            self.progress_updated.emit(90.0, "合并音频文件...")

            # 使用时间戳和随机数确保文件名唯一
            import time
            import random
            timestamp = int(time.time())
            random_suffix = random.randint(1000, 9999)
            final_audio_path = os.path.join(self.output_dir, f"merged_audio_{timestamp}_{random_suffix}.mp3")

            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)

            merge_result = await self._merge_audio_segments(audio_segments, final_audio_path)

            if not merge_result["success"]:
                return merge_result

            # 生成字幕文件
            self.progress_updated.emit(95.0, "生成字幕文件...")

            subtitle_file = os.path.join(self.output_dir, "subtitles_with_timecode.srt")
            self._generate_subtitle_file(subtitle_file)

            self.progress_updated.emit(100.0, "直接TTS合成完成！")

            return {
                "success": True,
                "audio_file": final_audio_path,
                "subtitle_file": subtitle_file,
                "total_duration": total_duration,
                "segment_count": len(audio_segments),
                "output_dir": self.output_dir,
                "temp_dir": temp_dir
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"直接TTS合成过程异常: {str(e)}"
            }

    def _seconds_to_timecode(self, seconds: float) -> str:
        """将秒数转换为SRT时间码格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    async def _merge_audio_segments(self, segments: list, output_path: str):
        """合并音频段"""
        try:
            # 检查所有音频文件是否存在
            missing_files = []
            for segment in segments:
                if not os.path.exists(segment['file']):
                    missing_files.append(segment['file'])

            if missing_files:
                return {
                    "success": False,
                    "message": f"音频文件不存在: {missing_files[:3]}..."
                }

            # 创建文件列表（确保路径不冲突）
            import tempfile
            temp_list_file = tempfile.mktemp(suffix='_concat_list.txt')

            with open(temp_list_file, 'w', encoding='utf-8') as f:
                for segment in segments:
                    abs_path = os.path.abspath(segment['file']).replace('\\', '/')
                    f.write(f"file '{abs_path}'\n")

            # 使用FFmpeg合并MP3文件
            import subprocess
            cmd = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_file,
                '-acodec', 'libmp3lame',
                '-ar', '16000',
                '-ac', '1',
                '-b:a', '128k',
                output_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # 清理临时文件
            try:
                os.remove(temp_list_file)
            except:
                pass

            if process.returncode == 0:
                if os.path.exists(output_path):
                    return {"success": True, "message": "音频合并成功"}
                else:
                    return {"success": False, "message": "音频合并完成但输出文件不存在"}
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')

                # 检查是否是路径冲突错误
                if "same as Input" in error_msg or "cannot edit existing files in-place" in error_msg:
                    # 生成新的输出路径并重试
                    import time
                    import random
                    base_name = os.path.splitext(output_path)[0]
                    ext = os.path.splitext(output_path)[1]
                    new_output = f"{base_name}_retry_{int(time.time())}_{random.randint(1000,9999)}{ext}"

                    # 重新创建命令
                    retry_cmd = cmd[:-1] + [new_output]  # 替换最后的输出路径

                    try:
                        retry_process = await asyncio.create_subprocess_exec(
                            *retry_cmd,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                        retry_stdout, retry_stderr = await retry_process.communicate()

                        if retry_process.returncode == 0 and os.path.exists(new_output):
                            return {"success": True, "message": f"音频合并成功（重试后）: {os.path.basename(new_output)}"}
                        else:
                            retry_error = retry_stderr.decode('utf-8', errors='ignore')
                            return {"success": False, "message": f"音频合并重试失败: {retry_error[:200]}..."}
                    except Exception as retry_e:
                        return {"success": False, "message": f"音频合并重试异常: {str(retry_e)}"}
                else:
                    return {"success": False, "message": f"音频合并失败: {error_msg[:200]}..."}

        except Exception as e:
            return {"success": False, "message": f"音频合并异常: {str(e)}"}

    def _generate_subtitle_file(self, output_file: str):
        """生成带时间码的字幕文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for item in self.subtitles.items:
                    f.write(f"{item.index}\n")
                    f.write(f"{item.start_time} --> {item.end_time}\n")
                    f.write(f"{item.text}\n\n")

            self.logger.info(f"字幕文件生成完成: {output_file}")

        except Exception as e:
            self.logger.error(f"字幕文件生成失败: {e}")


class TTSWorkflowThread(QThread):
    """TTS专用工作流程线程"""
    progress_updated = pyqtSignal(float, str)  # progress, message
    tts_completed = pyqtSignal(dict)  # result
    error_occurred = pyqtSignal(str)  # error_message

    def __init__(self, subtitles, config_manager):
        super().__init__()
        self.subtitles = subtitles
        self.config_manager = config_manager
        self.logger = get_default_logger()

    def run(self):
        """执行TTS合成"""
        try:
            self.progress_updated.emit(5.0, "初始化TTS客户端...")

            # 获取TTS配置
            api_config = self.config_manager.get_api_config()
            tts_config = api_config.get("aliyun_tts", {})

            # 创建TTS客户端
            from ..services.aliyun_tts_client import AliyunTTSClient
            tts_client = AliyunTTSClient(tts_config)

            self.progress_updated.emit(10.0, "开始分段TTS合成...")

            # 创建临时目录
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="tts_synthesis_")

            # 执行分段TTS合成
            import asyncio
            result = asyncio.run(self._perform_tts_synthesis(tts_client, temp_dir))

            if result["success"]:
                self.tts_completed.emit(result)
            else:
                self.error_occurred.emit(result["message"])

        except Exception as e:
            self.error_occurred.emit(f"TTS合成异常: {str(e)}")

    async def _perform_tts_synthesis(self, tts_client, temp_dir):
        """执行TTS合成"""
        try:
            audio_segments = []
            total_duration = 0.0

            for i, subtitle_item in enumerate(self.subtitles.items):
                progress = 15.0 + (i / len(self.subtitles.items)) * 70.0
                self.progress_updated.emit(
                    progress,
                    f"合成第 {i+1}/{len(self.subtitles.items)} 段音频: {subtitle_item.text[:20]}..."
                )

                # 为每个字幕段生成音频
                segment_output_path = os.path.join(temp_dir, f"segment_{i+1:03d}.mp3")

                result = await tts_client.synthesize_text(
                    text=subtitle_item.text,
                    output_file=segment_output_path
                )

                if result.success:
                    # 计算时间码
                    start_time = total_duration
                    end_time = total_duration + result.duration

                    # 更新字幕项的时间码
                    subtitle_item.start_time = self._seconds_to_timecode(start_time)
                    subtitle_item.end_time = self._seconds_to_timecode(end_time)

                    audio_segments.append({
                        "file": segment_output_path,
                        "duration": result.duration,
                        "start_time": start_time,
                        "end_time": end_time,
                        "subtitle_index": subtitle_item.index
                    })

                    total_duration += result.duration
                else:
                    return {
                        "success": False,
                        "message": f"第 {i+1} 段音频合成失败: {result.message}"
                    }

            # 合并音频
            self.progress_updated.emit(90.0, "合并音频文件...")

            final_audio_path = os.path.join(temp_dir, "final_audio.wav")
            merge_result = await self._merge_audio_segments(audio_segments, final_audio_path)

            if not merge_result["success"]:
                return merge_result

            self.progress_updated.emit(100.0, "TTS合成完成！")

            return {
                "success": True,
                "audio_file": final_audio_path,
                "total_duration": total_duration,
                "segment_count": len(audio_segments),
                "audio_segments": audio_segments,
                "subtitles_with_timecode": self.subtitles,
                "temp_dir": temp_dir
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"TTS合成过程异常: {str(e)}"
            }

    def _seconds_to_timecode(self, seconds: float) -> str:
        """将秒数转换为SRT时间码格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    async def _merge_audio_segments(self, segments: list, output_path: str):
        """合并音频段"""
        try:
            # 检查所有音频文件是否存在
            missing_files = []
            for segment in segments:
                if not os.path.exists(segment['file']):
                    missing_files.append(segment['file'])

            if missing_files:
                return {
                    "success": False,
                    "message": f"音频文件不存在: {missing_files[:3]}..."  # 只显示前3个
                }

            # 创建文件列表，使用绝对路径（确保路径不冲突）
            import tempfile
            temp_list_file = tempfile.mktemp(suffix='_concat_list.txt')

            with open(temp_list_file, 'w', encoding='utf-8') as f:
                for segment in segments:
                    # 使用绝对路径并转换路径分隔符
                    abs_path = os.path.abspath(segment['file']).replace('\\', '/')
                    f.write(f"file '{abs_path}'\n")

            # 使用FFmpeg合并，支持MP3格式
            import subprocess
            cmd = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_file,
                '-acodec', 'libmp3lame',  # MP3编码
                '-ar', '16000',           # 统一采样率16000
                '-ac', '1',               # 单声道
                '-b:a', '128k',           # 音频比特率
                output_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # 清理临时文件
            try:
                os.remove(temp_list_file)
            except:
                pass

            if process.returncode == 0:
                # 验证输出文件是否存在
                if os.path.exists(output_path):
                    return {"success": True, "message": "音频合并成功"}
                else:
                    return {
                        "success": False,
                        "message": "音频合并完成但输出文件不存在"
                    }
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                return {
                    "success": False,
                    "message": f"音频合并失败: {error_msg}"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"音频合并异常: {str(e)}"
            }


class WorkflowThread(QThread):
    """工作流程执行线程"""

    progress_updated = pyqtSignal(str, object)  # session_id, WorkflowProgress
    error_occurred = pyqtSignal(str, str)       # session_id, error_message
    workflow_completed = pyqtSignal(str, bool, str)  # session_id, success, message

    def __init__(self, workflow_manager: WorkflowManager):
        super().__init__()
        self.workflow_manager = workflow_manager
        self.session_data = {}
        self.current_session_id = None

        # 设置回调
        self.workflow_manager.add_global_progress_callback(self._on_progress)
        self.workflow_manager.add_global_error_callback(self._on_error)
        self.workflow_manager.add_session_complete_callback(self._on_complete)

    def _on_progress(self, session_id: str, progress: WorkflowProgress):
        """进度回调"""
        self.progress_updated.emit(session_id, progress)

    def _on_error(self, session_id: str, error: ProcessingError):
        """错误回调"""
        self.error_occurred.emit(session_id, error.message)

    def _on_complete(self, session_id: str, result):
        """完成回调"""
        self.workflow_completed.emit(session_id, result.success, result.message)

    def start_workflow(self, session_id: str, video_file: str, subtitle_file: str,
                      style_name: str, output_directory: str):
        """启动工作流程"""
        self.session_data[session_id] = {
            'video_file': video_file,
            'subtitle_file': subtitle_file,
            'style_name': style_name,
            'output_directory': output_directory
        }
        self.current_session_id = session_id

        # 启动线程
        self.start()

    def run(self):
        """在线程中运行异步工作流程"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行工作流程
            loop.run_until_complete(self._run_workflow(self.current_session_id))

        except Exception as e:
            # 发送错误信号
            self.error_occurred.emit(self.current_session_id, str(e))
        finally:
            # 清理事件循环
            try:
                loop.close()
            except:
                pass

    async def _run_workflow(self, session_id: str):
        """运行工作流程"""
        try:
            data = self.session_data[session_id]
            result = await self.workflow_manager.start_video_editing(
                session_id=session_id,
                video_file=data['video_file'],
                subtitle_file=data['subtitle_file'],
                style_name=data['style_name'],
                output_directory=data['output_directory']
            )

            # 发送完成信号
            self.workflow_completed.emit(session_id, result.success, result.message)

        except Exception as e:
            # 发送错误信号
            self.error_occurred.emit(session_id, str(e))


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化配置和工作流程管理器
        self.config_manager = ConfigManager()
        self.workflow_manager = WorkflowManager(self.config_manager)
        self.workflow_thread = WorkflowThread(self.workflow_manager)
        self.logger = get_default_logger()
        
        # 当前会话信息
        self.current_session_id: Optional[str] = None
        self.video_file_path: Optional[str] = None
        self.subtitle_file_path: Optional[str] = None
        self.output_directory: Optional[str] = None
        
        # 会话状态跟踪
        self.session_widgets: Dict[str, Dict[str, Any]] = {}
        
        self.init_ui()
        self.setup_connections()
        self.setup_styles()

        # 在UI完全创建后加载风格列表
        self.load_style_list()

        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AI智能视频剪辑软件 v1.0")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央窗口部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        open_video_action = QAction('打开视频...', self)
        open_video_action.setShortcut('Ctrl+O')
        open_video_action.triggered.connect(self.open_video_file)
        file_menu.addAction(open_video_action)
        
        open_subtitle_action = QAction('打开字幕...', self)
        open_subtitle_action.setShortcut('Ctrl+S')
        open_subtitle_action.triggered.connect(self.open_subtitle_file)
        file_menu.addAction(open_subtitle_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        settings_action = QAction('设置...', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于...', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # 打开视频按钮
        open_video_btn = QPushButton('打开视频')
        open_video_btn.clicked.connect(self.open_video_file)
        toolbar.addWidget(open_video_btn)
        
        # 打开字幕按钮
        open_subtitle_btn = QPushButton('打开字幕')
        open_subtitle_btn.clicked.connect(self.open_subtitle_file)
        toolbar.addWidget(open_subtitle_btn)
        
        toolbar.addSeparator()
        
        # 开始处理按钮
        self.start_btn = QPushButton('开始处理')
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setEnabled(False)
        toolbar.addWidget(self.start_btn)
        
        # 停止处理按钮
        self.stop_btn = QPushButton('停止处理')
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        toolbar.addWidget(self.stop_btn)
    
    def create_central_widget(self):
        """创建中央窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 1000])
    
    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        left_widget.setLayout(left_layout)
        
        # 文件输入组
        file_group = QGroupBox("文件输入")
        file_layout = QVBoxLayout()
        file_group.setLayout(file_layout)
        
        # 视频文件选择
        video_layout = QHBoxLayout()
        video_layout.addWidget(QLabel("视频文件:"))
        self.video_file_label = QLabel("未选择")
        self.video_file_label.setStyleSheet("color: gray; font-style: italic;")
        video_layout.addWidget(self.video_file_label)
        video_btn = QPushButton("浏览...")
        video_btn.clicked.connect(self.open_video_file)
        video_layout.addWidget(video_btn)
        file_layout.addLayout(video_layout)
        
        # 字幕文件选择
        subtitle_layout = QHBoxLayout()
        subtitle_layout.addWidget(QLabel("字幕文件:"))
        self.subtitle_file_label = QLabel("未选择")
        self.subtitle_file_label.setStyleSheet("color: gray; font-style: italic;")
        subtitle_layout.addWidget(self.subtitle_file_label)
        subtitle_btn = QPushButton("浏览...")
        subtitle_btn.clicked.connect(self.open_subtitle_file)
        subtitle_layout.addWidget(subtitle_btn)
        file_layout.addLayout(subtitle_layout)
        
        # 输出目录选择
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_dir_label = QLabel("未选择")
        self.output_dir_label.setStyleSheet("color: gray; font-style: italic;")
        output_layout.addWidget(self.output_dir_label)
        output_btn = QPushButton("浏览...")
        output_btn.clicked.connect(self.select_output_directory)
        output_layout.addWidget(output_btn)
        file_layout.addLayout(output_layout)
        
        left_layout.addWidget(file_group)
        
        # 处理设置组
        settings_group = QGroupBox("处理设置")
        settings_layout = QVBoxLayout()
        settings_group.setLayout(settings_layout)
        
        # 改写风格选择
        style_layout = QHBoxLayout()
        style_layout.addWidget(QLabel("改写风格:"))
        self.style_combo = QComboBox()
        # 先添加默认选项，稍后再加载完整列表
        self.style_combo.addItem("科普解说风格")
        style_layout.addWidget(self.style_combo)
        settings_layout.addLayout(style_layout)
        
        left_layout.addWidget(settings_group)
        
        # 会话列表
        session_group = QGroupBox("处理会话")
        session_layout = QVBoxLayout()
        session_group.setLayout(session_layout)
        
        self.session_list = QListWidget()
        session_layout.addWidget(self.session_list)
        
        left_layout.addWidget(session_group)
        
        # 添加弹性空间
        left_layout.addStretch()
        
        return left_widget

    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_widget.setLayout(right_layout)

        # 创建标签页
        self.main_tab_widget = QTabWidget()
        right_layout.addWidget(self.main_tab_widget)

        # 进度监控标签页
        progress_tab = self.create_progress_tab()
        self.main_tab_widget.addTab(progress_tab, "进度监控")

        # 字幕编辑标签页
        subtitle_tab = self.create_subtitle_tab()
        self.main_tab_widget.addTab(subtitle_tab, "字幕编辑")

        # 直接TTS合成标签页
        direct_tts_tab = self.create_direct_tts_tab()
        self.main_tab_widget.addTab(direct_tts_tab, "直接TTS合成")

        # 音画匹配标签页
        video_sync_tab = self.create_video_sync_tab()
        self.main_tab_widget.addTab(video_sync_tab, "音画匹配")

        # 结果预览标签页
        preview_tab = self.create_preview_tab()
        self.main_tab_widget.addTab(preview_tab, "结果预览")

        return right_widget

    def create_progress_tab(self):
        """创建进度监控标签页"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        tab_widget.setLayout(layout)

        # 当前任务信息
        current_group = QGroupBox("当前任务")
        current_layout = QVBoxLayout()
        current_group.setLayout(current_layout)

        self.current_task_label = QLabel("无任务")
        current_layout.addWidget(self.current_task_label)

        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        current_layout.addWidget(self.overall_progress)

        self.task_progress = QProgressBar()
        self.task_progress.setRange(0, 100)
        current_layout.addWidget(self.task_progress)

        self.status_label_tab = QLabel("就绪")
        current_layout.addWidget(self.status_label_tab)

        layout.addWidget(current_group)

        # 任务列表
        tasks_group = QGroupBox("任务列表")
        tasks_layout = QVBoxLayout()
        tasks_group.setLayout(tasks_layout)

        self.task_table = QTableWidget()
        self.task_table.setColumnCount(3)
        self.task_table.setHorizontalHeaderLabels(["任务", "状态", "进度"])
        self.task_table.horizontalHeader().setStretchLastSection(True)
        tasks_layout.addWidget(self.task_table)

        layout.addWidget(tasks_group)

        return tab_widget

    def create_subtitle_tab(self):
        """创建字幕编辑标签页"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        tab_widget.setLayout(layout)

        # 原始字幕
        original_group = QGroupBox("原始字幕")
        original_layout = QVBoxLayout()
        original_group.setLayout(original_layout)

        self.original_subtitle_text = QTextEdit()
        self.original_subtitle_text.setReadOnly(True)
        self.original_subtitle_text.setMaximumHeight(150)
        original_layout.addWidget(self.original_subtitle_text)

        # AI改写按钮
        original_button_layout = QHBoxLayout()
        rewrite_subtitle_btn = QPushButton("AI改写字幕")
        rewrite_subtitle_btn.clicked.connect(self.rewrite_subtitle)
        original_button_layout.addWidget(rewrite_subtitle_btn)
        original_button_layout.addStretch()  # 添加弹性空间
        original_layout.addLayout(original_button_layout)

        layout.addWidget(original_group)



        # 映射关系展示（放大窗口）
        mapping_group = QGroupBox("新旧字幕映射关系")
        mapping_layout = QVBoxLayout()
        mapping_group.setLayout(mapping_layout)

        # 创建映射关系表格（放大高度）
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(3)
        self.mapping_table.setHorizontalHeaderLabels(["原字幕", "新字幕（可编辑）", "映射类型"])
        self.mapping_table.setMinimumHeight(400)  # 放大窗口高度

        # 设置表格列宽
        header = self.mapping_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        self.mapping_table.setColumnWidth(2, 100)

        # 设置表格样式
        self.mapping_table.setAlternatingRowColors(True)
        self.mapping_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        # 设置自定义编辑器委托
        self.subtitle_delegate = SubtitleTextDelegate()
        self.mapping_table.setItemDelegate(self.subtitle_delegate)

        # 设置表格样式（深色主题）
        self.mapping_table.setStyleSheet("""
            QTableWidget {
                background-color: #2b2b2b;
                color: white;
                gridline-color: #555555;
                selection-background-color: #4a90e2;
                alternate-background-color: #333333;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #4a90e2;
            }
            QHeaderView::section {
                background-color: #404040;
                color: white;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
        """)

        # 连接单元格编辑信号
        self.mapping_table.itemChanged.connect(self.on_mapping_cell_changed)

        mapping_layout.addWidget(self.mapping_table)

        # 映射关系统计信息和操作按钮
        stats_button_layout = QHBoxLayout()

        self.mapping_stats_label = QLabel("映射关系统计：等待改写...")
        self.mapping_stats_label.setStyleSheet("color: #666; font-size: 12px;")
        stats_button_layout.addWidget(self.mapping_stats_label)

        stats_button_layout.addStretch()

        # 保存修改按钮
        save_mapping_btn = QPushButton("保存修改")
        save_mapping_btn.clicked.connect(self.save_mapping_changes)
        stats_button_layout.addWidget(save_mapping_btn)

        # TTS合成按钮
        self.tts_synthesis_btn = QPushButton("TTS语音合成")
        self.tts_synthesis_btn.clicked.connect(self.start_tts_and_auto_match)
        self.tts_synthesis_btn.setEnabled(False)  # 初始禁用
        self.tts_synthesis_btn.setToolTip("完成字幕改写后可进行TTS语音合成并自动音画匹配")
        stats_button_layout.addWidget(self.tts_synthesis_btn)

        mapping_layout.addLayout(stats_button_layout)

        layout.addWidget(mapping_group)



        return tab_widget

    def create_direct_tts_tab(self):
        """创建直接TTS合成标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 标题和说明
        title_label = QLabel("📝 直接TTS语音合成")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        info_label = QLabel("上传您手动准备的字幕文案，直接进行TTS语音合成，跳过AI改写步骤")
        info_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        layout.addWidget(info_label)

        # 文案输入区域
        input_group = QGroupBox("字幕文案输入")
        input_layout = QVBoxLayout()
        input_group.setLayout(input_layout)

        # 输入方式选择
        input_method_layout = QHBoxLayout()

        self.input_method_text = QRadioButton("直接输入文本")
        self.input_method_text.setChecked(True)
        self.input_method_text.toggled.connect(self._on_input_method_changed)
        input_method_layout.addWidget(self.input_method_text)

        self.input_method_file = QRadioButton("上传文件")
        self.input_method_file.toggled.connect(self._on_input_method_changed)
        input_method_layout.addWidget(self.input_method_file)

        input_method_layout.addStretch()
        input_layout.addLayout(input_method_layout)

        # 文本输入区域
        self.direct_text_input = QTextEdit()
        self.direct_text_input.setPlaceholderText(
            "请输入要合成语音的字幕文案，每行一条字幕：\n\n"
            "人工智能技术正在快速发展\n"
            "它在各个领域都有广泛的应用\n"
            "视频制作也开始大量使用AI技术\n\n"
            "注意：系统将自动为每行文本生成时间码"
        )
        self.direct_text_input.setMinimumHeight(200)
        input_layout.addWidget(self.direct_text_input)

        # 文件上传区域
        file_upload_layout = QHBoxLayout()

        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("选择字幕文件 (.txt, .srt)")
        self.file_path_input.setEnabled(False)
        file_upload_layout.addWidget(self.file_path_input)

        self.browse_file_btn = QPushButton("浏览文件")
        self.browse_file_btn.clicked.connect(self._browse_subtitle_file)
        self.browse_file_btn.setEnabled(False)
        file_upload_layout.addWidget(self.browse_file_btn)

        input_layout.addLayout(file_upload_layout)

        layout.addWidget(input_group)

        # TTS设置区域
        settings_group = QGroupBox("TTS合成设置")
        settings_layout = QVBoxLayout()
        settings_group.setLayout(settings_layout)

        # 语音选择
        voice_layout = QHBoxLayout()
        voice_layout.addWidget(QLabel("语音类型:"))

        self.direct_voice_combo = QComboBox()
        self.direct_voice_combo.addItems([
            "zhixiaobai (知小白-普通话女声)",
            "zhixiaoxia (知小夏-普通话女声)",
            "zhixiaomei (知小妹-普通话女声)",
            "xiaoyun (小云-标准女声)",
            "xiaogang (小刚-标准男声)",
            "ruoxi (若兮-温柔女声)",
            "siqi (思琪-温柔女声)",
            "sijia (思佳-标准女声)",
            "sicheng (思诚-标准男声)"
        ])
        voice_layout.addWidget(self.direct_voice_combo)
        voice_layout.addStretch()

        settings_layout.addLayout(voice_layout)

        # 输出设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))

        self.direct_output_path = QLineEdit()
        self.direct_output_path.setPlaceholderText("选择音频文件输出目录")
        output_layout.addWidget(self.direct_output_path)

        self.browse_output_btn = QPushButton("浏览")
        self.browse_output_btn.clicked.connect(self._browse_output_directory)
        output_layout.addWidget(self.browse_output_btn)

        settings_layout.addLayout(output_layout)

        layout.addWidget(settings_group)

        # 操作按钮区域
        button_layout = QHBoxLayout()

        self.preview_subtitles_btn = QPushButton("预览字幕")
        self.preview_subtitles_btn.clicked.connect(self._preview_direct_subtitles)
        button_layout.addWidget(self.preview_subtitles_btn)

        self.start_direct_tts_btn = QPushButton("开始TTS合成")
        self.start_direct_tts_btn.clicked.connect(self._start_direct_tts)
        self.start_direct_tts_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.start_direct_tts_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 结果显示区域
        self.direct_tts_result = QTextEdit()
        self.direct_tts_result.setMaximumHeight(120)
        self.direct_tts_result.setPlaceholderText("TTS合成结果将显示在这里...")
        self.direct_tts_result.setReadOnly(True)
        layout.addWidget(self.direct_tts_result)

        # 下载按钮区域
        download_layout = QHBoxLayout()

        self.download_audio_btn = QPushButton("📥 下载音频文件")
        self.download_audio_btn.clicked.connect(self._download_audio_file)
        self.download_audio_btn.setEnabled(False)
        self.download_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        download_layout.addWidget(self.download_audio_btn)

        self.download_subtitle_btn = QPushButton("📄 下载字幕文件")
        self.download_subtitle_btn.clicked.connect(self._download_subtitle_file)
        self.download_subtitle_btn.setEnabled(False)
        self.download_subtitle_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        download_layout.addWidget(self.download_subtitle_btn)

        self.open_output_dir_btn = QPushButton("📁 打开输出目录")
        self.open_output_dir_btn.clicked.connect(self._open_output_directory)
        self.open_output_dir_btn.setEnabled(False)
        self.open_output_dir_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        download_layout.addWidget(self.open_output_dir_btn)

        download_layout.addStretch()
        layout.addLayout(download_layout)

        # 存储当前合成结果的文件路径
        self.current_audio_file = None
        self.current_subtitle_file = None
        self.current_output_dir = None

        return tab

    def create_video_sync_tab(self):
        """创建音画匹配标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 标题和说明
        title_label = QLabel("🎬 音画匹配与视频调速")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)

        desc_label = QLabel("将TTS生成的音频与视频进行匹配，并根据音频时长调整视频播放速度")
        desc_label.setStyleSheet("color: #7f8c8d; margin: 5px 10px;")
        layout.addWidget(desc_label)

        # 状态信息区域
        status_group = QGroupBox("匹配状态")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)

        self.match_status_label = QLabel("等待TTS语音合成完成后自动开始音画匹配...")
        self.match_status_label.setStyleSheet("color: #7f8c8d; font-style: italic; padding: 10px;")
        status_layout.addWidget(self.match_status_label)

        layout.addWidget(status_group)

        # 输出设置区域
        settings_group = QGroupBox("输出设置")
        settings_layout = QVBoxLayout()
        settings_group.setLayout(settings_layout)

        # 输出目录设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.match_output_path = QLineEdit()
        self.match_output_path.setPlaceholderText("选择输出目录...")
        default_output = os.path.join(os.path.expanduser("~"), "Desktop", "AI_Video_Output")
        self.match_output_path.setText(default_output)
        output_layout.addWidget(self.match_output_path)

        self.browse_match_output_btn = QPushButton("浏览")
        self.browse_match_output_btn.clicked.connect(self._browse_match_output)
        output_layout.addWidget(self.browse_match_output_btn)

        settings_layout.addLayout(output_layout)

        layout.addWidget(settings_group)

        # 匹配结果预览区域
        preview_group = QGroupBox("匹配结果预览")
        preview_layout = QVBoxLayout()
        preview_group.setLayout(preview_layout)

        # 匹配结果表格
        self.match_result_table = QTableWidget()
        self.match_result_table.setColumnCount(6)
        self.match_result_table.setHorizontalHeaderLabels([
            "序号", "字幕内容", "音频时长", "视频片段数", "视频总时长", "速度因子"
        ])

        # 设置表格样式
        self.match_result_table.horizontalHeader().setStretchLastSection(True)
        self.match_result_table.setAlternatingRowColors(True)
        self.match_result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.match_result_table.setMaximumHeight(300)

        preview_layout.addWidget(self.match_result_table)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.preview_match_btn = QPushButton("👁️ 预览选中项")
        self.preview_match_btn.clicked.connect(self._preview_selected_match)
        self.preview_match_btn.setEnabled(False)
        button_layout.addWidget(self.preview_match_btn)

        self.edit_match_btn = QPushButton("✏️ 编辑匹配")
        self.edit_match_btn.clicked.connect(self._edit_selected_match)
        self.edit_match_btn.setEnabled(False)
        button_layout.addWidget(self.edit_match_btn)

        self.refresh_match_btn = QPushButton("🔄 刷新匹配")
        self.refresh_match_btn.clicked.connect(self._refresh_match_results)
        self.refresh_match_btn.setEnabled(False)
        button_layout.addWidget(self.refresh_match_btn)

        self.export_results_btn = QPushButton("📥 导出结果")
        self.export_results_btn.clicked.connect(self._export_match_results)
        self.export_results_btn.setEnabled(False)
        self.export_results_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.export_results_btn)

        button_layout.addStretch()
        preview_layout.addLayout(button_layout)

        layout.addWidget(preview_group)

        # 状态显示区域
        self.match_status_display = QTextEdit()
        self.match_status_display.setMaximumHeight(80)
        self.match_status_display.setPlaceholderText("等待TTS语音合成完成后自动开始音画匹配...")
        self.match_status_display.setReadOnly(True)
        layout.addWidget(self.match_status_display)

        return tab

    def create_preview_tab(self):
        """创建结果预览标签页"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        tab_widget.setLayout(layout)

        # 输出文件列表
        output_group = QGroupBox("输出文件")
        output_layout = QVBoxLayout()
        output_group.setLayout(output_layout)

        self.output_list = QListWidget()
        output_layout.addWidget(self.output_list)

        # 文件操作按钮
        button_layout = QHBoxLayout()
        open_folder_btn = QPushButton("打开文件夹")
        open_folder_btn.clicked.connect(self.open_output_folder)
        button_layout.addWidget(open_folder_btn)

        play_video_btn = QPushButton("播放视频")
        play_video_btn.clicked.connect(self.play_output_video)
        button_layout.addWidget(play_video_btn)

        output_layout.addLayout(button_layout)
        layout.addWidget(output_group)

        # 统计信息
        stats_group = QGroupBox("处理统计")
        stats_layout = QVBoxLayout()
        stats_group.setLayout(stats_layout)

        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(100)
        stats_layout.addWidget(self.stats_text)

        layout.addWidget(stats_group)

        return tab_widget

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 添加状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 添加进度条
        self.status_progress = QProgressBar()
        self.status_progress.setVisible(False)
        self.status_progress.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.status_progress)

    def setup_connections(self):
        """设置信号连接"""
        # 工作流程线程信号连接
        self.workflow_thread.progress_updated.connect(self.on_progress_updated)
        self.workflow_thread.error_occurred.connect(self.on_error_occurred)
        self.workflow_thread.workflow_completed.connect(self.on_workflow_completed)

    def setup_styles(self):
        """设置样式"""
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

    def load_style_list(self):
        """加载改写风格列表"""
        try:
            # 安全检查：确保style_combo已经创建
            if not (hasattr(self, 'style_combo') and self.style_combo):
                if hasattr(self, 'logger'):
                    self.logger.warning("style_combo未初始化，跳过风格列表加载")
                return

            # 获取风格列表
            styles = self.config_manager.get_style_list()

            # 清空现有选项
            self.style_combo.clear()

            # 添加风格选项
            if styles and len(styles) > 0:
                self.style_combo.addItems(styles)
                if hasattr(self, 'logger'):
                    self.logger.info(f"加载了 {len(styles)} 个改写风格")
            else:
                # 添加默认风格
                default_styles = [
                    "科普解说风格",
                    "娱乐搞笑风格",
                    "商业宣传风格",
                    "新闻播报风格"
                ]
                self.style_combo.addItems(default_styles)
                if hasattr(self, 'logger'):
                    self.logger.info(f"使用默认风格列表，共 {len(default_styles)} 个")

        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"加载风格列表失败: {e}")

            # 确保至少有一个默认选项
            if hasattr(self, 'style_combo') and self.style_combo:
                self.style_combo.clear()
                self.style_combo.addItem("科普解说风格")  # 最基本的默认风格

    def open_video_file(self):
        """打开视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;所有文件 (*)"
        )

        if file_path:
            self.video_file_path = file_path
            self.video_file_label.setText(os.path.basename(file_path))
            self.video_file_label.setStyleSheet("color: black; font-style: normal;")
            self.check_ready_to_process()
            self.logger.info(f"选择视频文件: {file_path}")

    def open_subtitle_file(self):
        """打开字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择字幕文件", "",
            "字幕文件 (*.srt *.vtt);;所有文件 (*)"
        )

        if file_path:
            self.subtitle_file_path = file_path
            self.subtitle_file_label.setText(os.path.basename(file_path))
            self.subtitle_file_label.setStyleSheet("color: black; font-style: normal;")
            self.check_ready_to_process()
            self.load_subtitle_content(file_path)
            self.logger.info(f"选择字幕文件: {file_path}")

    def select_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")

        if dir_path:
            self.output_directory = dir_path
            self.output_dir_label.setText(os.path.basename(dir_path))
            self.output_dir_label.setStyleSheet("color: black; font-style: normal;")
            self.check_ready_to_process()
            self.logger.info(f"选择输出目录: {dir_path}")

    def check_ready_to_process(self):
        """检查是否准备好开始处理"""
        try:
            # 检查所有必需的条件
            has_video = bool(self.video_file_path)
            has_subtitle = bool(self.subtitle_file_path)
            has_output_dir = bool(self.output_directory)

            # 检查风格选择
            has_style = False
            if hasattr(self, 'style_combo') and self.style_combo:
                current_style = self.style_combo.currentText()
                has_style = bool(current_style and current_style.strip())

            ready = has_video and has_subtitle and has_output_dir and has_style

            # 安全检查：确保按钮已经创建
            if hasattr(self, 'start_btn') and self.start_btn:
                self.start_btn.setEnabled(ready)

            # 安全检查：确保状态标签已经创建
            if hasattr(self, 'status_label') and self.status_label:
                if ready:
                    self.status_label.setText("准备就绪")
                else:
                    missing = []
                    if not has_video:
                        missing.append("视频文件")
                    if not has_subtitle:
                        missing.append("字幕文件")
                    if not has_output_dir:
                        missing.append("输出目录")
                    if not has_style:
                        missing.append("改写风格")

                    self.status_label.setText(f"请选择: {', '.join(missing)}")

        except Exception as e:
            # 如果出现任何错误，记录日志并设置安全状态
            if hasattr(self, 'logger'):
                self.logger.error(f"检查处理状态时出错: {e}")

            if hasattr(self, 'start_btn') and self.start_btn:
                self.start_btn.setEnabled(False)
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText("状态检查出错")

    def load_subtitle_content(self, file_path: str):
        """加载字幕内容到编辑器"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.original_subtitle_text.setText(content)
        except Exception as e:
            self.logger.error(f"加载字幕内容失败: {e}")
            QMessageBox.warning(self, "警告", f"无法加载字幕文件: {str(e)}")

    def start_processing(self):
        """开始处理"""
        if not all([self.video_file_path, self.subtitle_file_path, self.output_directory]):
            QMessageBox.warning(self, "警告", "请先选择所有必需的文件和目录")
            return

        # 生成会话ID
        session_id = f"session_{uuid.uuid4().hex[:8]}"
        self.current_session_id = session_id

        # 添加到会话列表
        item = QListWidgetItem(f"{session_id} - 处理中...")
        self.session_list.addItem(item)

        # 检查是否有线程正在运行
        if self.workflow_thread.isRunning():
            QMessageBox.warning(self, "警告", "已有任务正在运行，请等待完成后再启动新任务")
            return

        # 启动工作流程
        self.workflow_thread.start_workflow(
            session_id=session_id,
            video_file=self.video_file_path,
            subtitle_file=self.subtitle_file_path,
            style_name=self.style_combo.currentText(),
            output_directory=self.output_directory
        )

        # 更新UI状态
        if hasattr(self, 'start_btn') and self.start_btn:
            self.start_btn.setEnabled(False)
        if hasattr(self, 'stop_btn') and self.stop_btn:
            self.stop_btn.setEnabled(True)
        if hasattr(self, 'status_progress') and self.status_progress:
            self.status_progress.setVisible(True)
        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.setText("处理中...")

        self.logger.info(f"开始处理会话: {session_id}")

    def stop_processing(self):
        """停止处理"""
        try:
            # 停止工作流程线程
            if self.workflow_thread.isRunning():
                # 尝试取消会话
                if self.current_session_id:
                    result = self.workflow_manager.cancel_session(self.current_session_id)
                    if result.success:
                        self.logger.info(f"取消会话: {self.current_session_id}")
                    else:
                        self.logger.error(f"取消会话失败: {result.message}")

                # 等待线程结束
                self.workflow_thread.quit()
                self.workflow_thread.wait(3000)  # 等待最多3秒

                if self.workflow_thread.isRunning():
                    self.workflow_thread.terminate()
                    self.workflow_thread.wait(1000)

                self.logger.info("工作流程线程已停止")

            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText("已取消")

        except Exception as e:
            self.logger.error(f"停止处理时发生错误: {e}")

        finally:
            self.reset_ui_state()

    def reset_ui_state(self):
        """重置UI状态"""
        # 安全检查：确保UI组件已经创建
        if hasattr(self, 'start_btn') and self.start_btn:
            self.start_btn.setEnabled(True)
        if hasattr(self, 'stop_btn') and self.stop_btn:
            self.stop_btn.setEnabled(False)
        if hasattr(self, 'status_progress') and self.status_progress:
            self.status_progress.setVisible(False)
            self.status_progress.setValue(0)
        if hasattr(self, 'overall_progress') and self.overall_progress:
            self.overall_progress.setValue(0)
        if hasattr(self, 'task_progress') and self.task_progress:
            self.task_progress.setValue(0)
        self.current_session_id = None

    def on_progress_updated(self, session_id: str, progress: WorkflowProgress):
        """处理进度更新"""
        if session_id == self.current_session_id:
            # 安全检查：确保进度条组件已经创建
            if hasattr(self, 'overall_progress') and self.overall_progress:
                self.overall_progress.setValue(int(progress.overall_progress))
            if hasattr(self, 'task_progress') and self.task_progress:
                self.task_progress.setValue(int(progress.current_task_progress))
            if hasattr(self, 'status_progress') and self.status_progress:
                self.status_progress.setValue(int(progress.overall_progress))

            # 安全检查：确保标签组件已经创建
            if progress.current_task and hasattr(self, 'current_task_label') and self.current_task_label:
                self.current_task_label.setText(progress.current_task)

            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText(progress.message)
            if hasattr(self, 'status_label_tab') and self.status_label_tab:
                self.status_label_tab.setText(progress.message)

    def on_error_occurred(self, session_id: str, error_message: str):
        """处理错误"""
        self.logger.error(f"会话 {session_id} 发生错误: {error_message}")
        QMessageBox.critical(self, "错误", f"处理过程中发生错误:\n{error_message}")

    def on_workflow_completed(self, session_id: str, success: bool, message: str):
        """处理工作流程完成"""
        if session_id == self.current_session_id:
            self.reset_ui_state()

            if success:
                self.status_label.setText("处理完成")
                QMessageBox.information(self, "完成", "视频处理完成！")
                self.load_output_files()
            else:
                self.status_label.setText("处理失败")
                QMessageBox.warning(self, "失败", f"处理失败: {message}")

        # 更新会话列表
        for i in range(self.session_list.count()):
            item = self.session_list.item(i)
            if session_id in item.text():
                status = "完成" if success else "失败"
                item.setText(f"{session_id} - {status}")
                break

    def load_output_files(self):
        """加载输出文件列表"""
        if self.output_directory:
            try:
                self.output_list.clear()
                for file in os.listdir(self.output_directory):
                    if file.endswith(('.mp4', '.avi', '.mov', '.srt', '.wav')):
                        self.output_list.addItem(file)
            except Exception as e:
                self.logger.error(f"加载输出文件失败: {e}")

    def rewrite_subtitle(self):
        """AI改写字幕"""
        # 检查是否有原始字幕内容
        if not self.original_subtitle_text.toPlainText():
            QMessageBox.warning(self, "警告", "请先加载字幕文件")
            return

        # 检查是否选择了改写风格
        if not (hasattr(self, 'style_combo') and self.style_combo and self.style_combo.currentText()):
            QMessageBox.warning(self, "警告", "请先选择改写风格")
            return

        try:
            # 显示进度对话框
            from PyQt6.QtWidgets import QProgressDialog
            from PyQt6.QtCore import Qt

            progress_dialog = QProgressDialog("正在进行AI字幕改写...", "取消", 0, 0, self)
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setAutoClose(True)
            progress_dialog.setAutoReset(True)
            progress_dialog.show()

            # 处理事件以显示对话框
            QApplication.processEvents()

            # 获取字幕内容和风格
            subtitle_content = self.original_subtitle_text.toPlainText()
            style_name = self.style_combo.currentText()

            self.logger.info(f"开始AI字幕改写，风格: {style_name}")

            # 创建DeepSeek客户端进行改写
            from ..services.deepseek_client import DeepSeekClient

            # 获取API配置
            api_config = self.config_manager.get_api_config().get("deepseek", {})
            deepseek_client = DeepSeekClient(api_config)

            # 解析字幕
            from ..core.subtitle_processor import SubtitleProcessor
            subtitle_processor = SubtitleProcessor()

            # 创建临时字幕文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', encoding='utf-8', delete=False) as temp_file:
                temp_file.write(subtitle_content)
                temp_subtitle_path = temp_file.name

            try:
                # 解析字幕
                parse_result, subtitles = subtitle_processor.parse_subtitle_file(temp_subtitle_path)

                if not parse_result.success:
                    progress_dialog.close()
                    QMessageBox.critical(self, "错误", f"字幕解析失败: {parse_result.message}")
                    return

                # 获取改写风格配置
                style_config = self.config_manager.get_prompt_style(style_name)
                if not style_config:
                    progress_dialog.close()
                    QMessageBox.critical(self, "错误", f"未找到改写风格: {style_name}")
                    return

                # 执行改写（同步调用异步方法）
                import asyncio

                # 创建新的事件循环来运行异步方法
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    rewrite_result, rewritten_subtitles, mappings = loop.run_until_complete(
                        deepseek_client.rewrite_subtitles(subtitles, style_config)
                    )
                finally:
                    loop.close()

                progress_dialog.close()

                if rewrite_result.success and rewritten_subtitles:
                    # 保存原字幕和改写结果到实例变量
                    self.current_subtitles = subtitles  # 保存原字幕数据
                    self.current_rewritten_subtitles = rewritten_subtitles

                    # 转换映射关系格式
                    self.current_mappings = self._convert_mappings_to_dict(mappings)
                    self.logger.info(f"保存映射关系: {self.current_mappings}")

                    # 更新映射关系展示
                    self.update_mapping_display(subtitles, rewritten_subtitles, mappings)

                    # 启用TTS合成按钮
                    if hasattr(self, 'tts_synthesis_btn'):
                        self.tts_synthesis_btn.setEnabled(True)
                        self.tts_synthesis_btn.setToolTip("点击开始TTS语音合成")

                    QMessageBox.information(self, "成功",
                                          f"字幕改写完成！\n"
                                          f"原始字幕: {subtitles.total_count} 条\n"
                                          f"改写后: {rewritten_subtitles.total_count} 条\n"
                                          f"请在映射关系表格中查看和编辑新字幕\n"
                                          f"编辑完成后可点击'TTS语音合成'按钮")

                    self.logger.info(f"字幕改写成功: {subtitles.total_count} -> {rewritten_subtitles.total_count}")
                else:
                    QMessageBox.critical(self, "错误", f"字幕改写失败: {rewrite_result.message}")
                    self.logger.error(f"字幕改写失败: {rewrite_result.message}")

            finally:
                # 清理临时文件
                import os
                if os.path.exists(temp_subtitle_path):
                    os.unlink(temp_subtitle_path)

        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.close()

            error_msg = str(e)
            if "API" in error_msg or "key" in error_msg.lower():
                QMessageBox.critical(self, "API配置错误",
                                   f"DeepSeek API配置有误:\n{error_msg}\n\n"
                                   f"请检查.env文件中的DEEPSEEK_API_KEY配置")
            else:
                QMessageBox.critical(self, "错误", f"字幕改写过程中发生错误:\n{error_msg}")

            self.logger.error(f"字幕改写异常: {e}")

    def update_mapping_display(self, original_subtitles, rewritten_subtitles, mappings):
        """更新映射关系显示"""
        try:
            # 清空表格
            self.mapping_table.setRowCount(0)

            # 创建原字幕索引映射
            original_dict = {item.index: item.text for item in original_subtitles.items}
            rewritten_dict = {item.index: item.text for item in rewritten_subtitles.items}

            # 统计信息
            one_to_one = 0  # 一对一
            one_to_many = 0  # 一对多
            many_to_one = 0  # 多对一

            # 填充映射关系表格
            for mapping in mappings:
                row_position = self.mapping_table.rowCount()
                self.mapping_table.insertRow(row_position)

                # 获取原字幕内容
                original_texts = []
                for orig_idx in mapping.original_indices:
                    if orig_idx in original_dict:
                        text = original_dict[orig_idx]
                        # 限制显示长度
                        if len(text) > 50:
                            text = text[:47] + "..."
                        original_texts.append(f"[{orig_idx}] {text}")

                # 获取新字幕内容
                rewritten_text = rewritten_dict.get(mapping.rewritten_index, "未找到")
                if len(rewritten_text) > 50:
                    rewritten_text = rewritten_text[:47] + "..."
                rewritten_text = f"[{mapping.rewritten_index}] {rewritten_text}"

                # 确定映射类型
                original_count = len(mapping.original_indices)
                if original_count == 1:
                    mapping_type = "1:1"
                    one_to_one += 1
                else:
                    mapping_type = f"{original_count}:1"
                    many_to_one += 1

                # 添加到表格
                original_item = QTableWidgetItem("\n".join(original_texts))
                original_item.setFlags(original_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 原字幕不可编辑
                # 设置原字幕的深色样式
                original_item.setBackground(QColor(45, 45, 45))  # 深灰色背景
                original_item.setForeground(QColor(255, 255, 255))  # 白色文字
                self.mapping_table.setItem(row_position, 0, original_item)

                rewritten_item = QTableWidgetItem(rewritten_text)
                rewritten_item.setFlags(rewritten_item.flags() | Qt.ItemFlag.ItemIsEditable)  # 新字幕可编辑
                # 设置新字幕的深色样式
                rewritten_item.setBackground(QColor(45, 45, 45))  # 深灰色背景
                rewritten_item.setForeground(QColor(255, 255, 255))  # 白色文字
                self.mapping_table.setItem(row_position, 1, rewritten_item)

                mapping_type_item = QTableWidgetItem(mapping_type)
                mapping_type_item.setFlags(mapping_type_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 映射类型不可编辑
                # 设置映射类型的深色样式
                mapping_type_item.setBackground(QColor(45, 45, 45))  # 深灰色背景
                mapping_type_item.setForeground(QColor(255, 255, 255))  # 白色文字
                self.mapping_table.setItem(row_position, 2, mapping_type_item)

                # 设置行高
                self.mapping_table.setRowHeight(row_position, 100)  # 增加行高以便编辑和显示

            # 更新统计信息
            total_mappings = len(mappings)
            stats_text = f"映射关系统计：共 {total_mappings} 组映射 | 一对一: {one_to_one} | 多对一: {many_to_one}"
            self.mapping_stats_label.setText(stats_text)

            # 调整表格列宽
            self.mapping_table.resizeColumnsToContents()

            self.logger.info(f"映射关系显示更新完成: {total_mappings} 组映射")

        except Exception as e:
            self.logger.error(f"更新映射关系显示失败: {e}")
            if hasattr(self, 'mapping_stats_label'):
                self.mapping_stats_label.setText("映射关系显示更新失败")

    def on_mapping_cell_changed(self, item):
        """映射表格单元格内容改变时的处理"""
        try:
            # 只允许编辑新字幕列（第1列）
            if item.column() == 1:
                row = item.row()
                new_text = item.text()

                # 更新内部数据结构（如果需要的话）
                self.logger.info(f"映射表格第{row+1}行的新字幕已修改: {new_text[:50]}...")

                # 标记为已修改（深色主题下的高亮色）
                item.setBackground(QColor(100, 80, 50))  # 深橙色背景表示已修改
                item.setForeground(QColor(255, 255, 255))  # 保持白色文字

        except Exception as e:
            self.logger.error(f"处理映射表格编辑失败: {e}")

    def save_mapping_changes(self):
        """保存映射关系的修改"""
        try:
            modified_count = 0

            # 遍历表格，收集修改的内容
            for row in range(self.mapping_table.rowCount()):
                new_subtitle_item = self.mapping_table.item(row, 1)
                if new_subtitle_item and new_subtitle_item.background().color() == QColor(100, 80, 50):
                    modified_count += 1
                    # 移除修改标记，恢复原始深色背景
                    new_subtitle_item.setBackground(QColor(45, 45, 45))
                    new_subtitle_item.setForeground(QColor(255, 255, 255))

            if modified_count > 0:
                QMessageBox.information(self, "保存成功", f"已保存 {modified_count} 条字幕的修改")
                self.logger.info(f"保存了 {modified_count} 条字幕修改")
            else:
                QMessageBox.information(self, "提示", "没有检测到修改内容")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存修改失败: {str(e)}")
            self.logger.error(f"保存映射修改失败: {e}")

    def save_subtitle(self):
        """保存字幕（从映射表格获取内容）"""
        if not hasattr(self, 'mapping_table') or self.mapping_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有字幕内容可保存，请先进行AI改写")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存字幕文件", "", "SRT文件 (*.srt);;所有文件 (*)"
        )

        if file_path:
            try:
                # 从映射表格构建字幕内容
                subtitle_lines = []
                for row in range(self.mapping_table.rowCount()):
                    new_subtitle_item = self.mapping_table.item(row, 1)
                    if new_subtitle_item:
                        # 提取字幕文本（移除索引前缀）
                        text = new_subtitle_item.text()
                        if '] ' in text:
                            text = text.split('] ', 1)[1]  # 移除 [索引] 前缀

                        # 构建SRT格式
                        subtitle_lines.append(str(row + 1))
                        subtitle_lines.append("[时间码待生成] --> [时间码待生成]")
                        subtitle_lines.append(text)
                        subtitle_lines.append("")  # 空行分隔

                content = "\n".join(subtitle_lines)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                QMessageBox.information(self, "成功",
                                      f"字幕文件保存成功\n"
                                      f"共保存 {self.mapping_table.rowCount()} 条字幕\n"
                                      f"注意：时间码将在TTS合成后生成")
                self.logger.info(f"保存字幕文件: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
                self.logger.error(f"保存字幕文件失败: {e}")

    def start_tts_and_auto_match(self):
        """开始TTS语音合成并自动音画匹配"""
        try:
            # 调试信息
            self.logger.info(f"开始TTS和自动匹配检查...")
            self.logger.info(f"video_file_path: {getattr(self, 'video_file_path', 'None')}")
            self.logger.info(f"current_rewritten_subtitles: {hasattr(self, 'current_rewritten_subtitles')}")
            self.logger.info(f"current_subtitles: {hasattr(self, 'current_subtitles')}")

            # 检查原字幕数据
            if hasattr(self, 'current_subtitles') and self.current_subtitles:
                self.logger.info(f"原字幕数量: {len(self.current_subtitles.items) if hasattr(self.current_subtitles, 'items') else 'N/A'}")
            else:
                self.logger.warning("没有找到原字幕数据")

            if not hasattr(self, 'current_rewritten_subtitles') or not self.current_rewritten_subtitles:
                QMessageBox.warning(self, "警告", "请先完成字幕改写")
                return

            # 检查是否有视频文件
            if not hasattr(self, 'video_file_path') or not self.video_file_path:
                QMessageBox.warning(self, "警告", "请先上传视频文件")
                return

            # 获取更新后的字幕数据
            updated_subtitles = self._get_updated_subtitles_from_table()
            if not updated_subtitles or not updated_subtitles.items:
                QMessageBox.warning(self, "警告", "没有有效的字幕数据")
                return

            # 确认开始TTS合成和音画匹配
            reply = QMessageBox.question(
                self, "确认",
                f"即将开始TTS语音合成和自动音画匹配\n"
                f"字幕数量: {len(updated_subtitles.items)} 条\n"
                f"视频文件: {os.path.basename(self.video_file_path)}\n"
                f"预计耗时: {len(updated_subtitles.items) * 3} 秒\n"
                f"完成后将自动跳转到音画匹配界面\n"
                f"是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 禁用TTS按钮，防止重复点击
            self.tts_synthesis_btn.setEnabled(False)
            self.tts_synthesis_btn.setText("合成和匹配中...")

            # 创建进度对话框
            self.tts_progress_dialog = QProgressDialog("TTS语音合成和音画匹配中...", "取消", 0, 100, self)
            self.tts_progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            self.tts_progress_dialog.setAutoClose(False)
            self.tts_progress_dialog.show()

            # 启动TTS合成和音画匹配工作流程
            self._start_tts_and_match_workflow(updated_subtitles)

            self.logger.info(f"开始TTS语音合成和音画匹配: {len(updated_subtitles.items)} 条字幕")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"TTS合成和匹配启动失败: {str(e)}")
            self.logger.error(f"TTS合成和匹配启动失败: {e}")

    def start_tts_synthesis(self):
        """开始TTS语音合成（保留原方法用于兼容）"""
        try:
            if not hasattr(self, 'current_rewritten_subtitles') or not self.current_rewritten_subtitles:
                QMessageBox.warning(self, "警告", "请先完成字幕改写")
                return

            # 获取表格中的最新字幕内容（包含用户修改）
            updated_subtitles = self._get_updated_subtitles_from_table()

            if not updated_subtitles:
                QMessageBox.warning(self, "警告", "没有找到可合成的字幕内容")
                return

            # 确认开始TTS合成
            reply = QMessageBox.question(
                self, "确认",
                f"即将开始TTS语音合成\n"
                f"字幕数量: {len(updated_subtitles.items)} 条\n"
                f"预计耗时: {len(updated_subtitles.items) * 2} 秒\n"
                f"是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 禁用TTS按钮，防止重复点击
            self.tts_synthesis_btn.setEnabled(False)
            self.tts_synthesis_btn.setText("合成中...")

            # 创建进度对话框
            self.tts_progress_dialog = QProgressDialog("TTS语音合成中...", "取消", 0, 100, self)
            self.tts_progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            self.tts_progress_dialog.setAutoClose(False)
            self.tts_progress_dialog.show()

            # 启动TTS合成工作流程
            self._start_tts_workflow(updated_subtitles)

            self.logger.info(f"开始TTS语音合成: {len(updated_subtitles.items)} 条字幕")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"TTS合成启动失败: {str(e)}")
            self.logger.error(f"TTS合成启动失败: {e}")

    def _get_updated_subtitles_from_table(self):
        """从映射表格获取更新后的字幕内容"""
        try:
            if not hasattr(self, 'mapping_table') or self.mapping_table.rowCount() == 0:
                return None

            # 复制原始字幕集合结构
            updated_subtitles = self.current_rewritten_subtitles

            # 更新字幕内容
            for row in range(self.mapping_table.rowCount()):
                new_subtitle_item = self.mapping_table.item(row, 1)
                if new_subtitle_item and row < len(updated_subtitles.items):
                    # 提取纯文本内容（移除索引前缀）
                    text = new_subtitle_item.text()
                    if '] ' in text:
                        text = text.split('] ', 1)[1]

                    # 更新字幕项的文本
                    updated_subtitles.items[row].text = text

            return updated_subtitles

        except Exception as e:
            self.logger.error(f"获取更新字幕失败: {e}")
            return None

    def _start_tts_workflow(self, subtitles):
        """启动TTS合成工作流程"""
        try:
            # 创建TTS专用的工作流程线程
            self.tts_thread = TTSWorkflowThread(subtitles, self.config_manager)

            # 连接信号
            self.tts_thread.progress_updated.connect(self._on_tts_progress)
            self.tts_thread.tts_completed.connect(self._on_tts_completed)
            self.tts_thread.error_occurred.connect(self._on_tts_error)

            # 启动线程
            self.tts_thread.start()

        except Exception as e:
            self.logger.error(f"TTS工作流程启动失败: {e}")
            self._on_tts_error(f"TTS工作流程启动失败: {str(e)}")

    def _on_tts_progress(self, progress, message):
        """TTS进度更新"""
        if hasattr(self, 'tts_progress_dialog') and self.tts_progress_dialog:
            self.tts_progress_dialog.setValue(int(progress))
            self.tts_progress_dialog.setLabelText(message)

    def _on_tts_completed(self, result):
        """TTS合成完成"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'tts_progress_dialog') and self.tts_progress_dialog:
                self.tts_progress_dialog.close()

            # 恢复按钮状态
            self.tts_synthesis_btn.setEnabled(True)
            self.tts_synthesis_btn.setText("TTS语音合成")

            if result.get("success", False):
                # 保存TTS结果
                self.tts_result = result

                # 更新映射表格显示新的时间码
                self._update_mapping_table_with_timecode(result.get("subtitles_with_timecode"))

                QMessageBox.information(
                    self, "成功",
                    f"TTS语音合成完成！\n"
                    f"音频文件: {result.get('audio_file', '未知')}\n"
                    f"总时长: {result.get('total_duration', 0):.2f}秒\n"
                    f"字幕段数: {result.get('segment_count', 0)}"
                )

                self.logger.info(f"TTS合成完成: {result}")
            else:
                QMessageBox.critical(
                    self, "错误",
                    f"TTS合成失败: {result.get('message', '未知错误')}"
                )

        except Exception as e:
            self.logger.error(f"TTS完成处理失败: {e}")

    def _on_tts_error(self, error_message):
        """TTS合成错误"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'tts_progress_dialog') and self.tts_progress_dialog:
                self.tts_progress_dialog.close()

            # 恢复按钮状态
            self.tts_synthesis_btn.setEnabled(True)
            self.tts_synthesis_btn.setText("TTS语音合成")

            QMessageBox.critical(self, "TTS合成错误", error_message)
            self.logger.error(f"TTS合成错误: {error_message}")

        except Exception as e:
            self.logger.error(f"TTS错误处理失败: {e}")

    def _update_mapping_table_with_timecode(self, subtitles_with_timecode):
        """更新映射表格显示时间码"""
        try:
            if not subtitles_with_timecode or not hasattr(self, 'mapping_table'):
                return

            # 更新映射关系统计信息
            if hasattr(self, 'mapping_stats_label'):
                self.mapping_stats_label.setText(
                    f"映射关系统计：共 {len(subtitles_with_timecode.items)} 组映射 | "
                    f"TTS合成完成，时间码已生成"
                )

            self.logger.info("映射表格时间码更新完成")

        except Exception as e:
            self.logger.error(f"更新映射表格时间码失败: {e}")

    def export_subtitle(self):
        """导出字幕"""
        # 这里可以添加更多的导出选项
        self.save_subtitle()

    def open_output_folder(self):
        """打开输出文件夹"""
        if self.output_directory:
            os.startfile(self.output_directory)  # Windows

    def play_output_video(self):
        """播放输出视频"""
        current_item = self.output_list.currentItem()
        if current_item and self.output_directory:
            video_file = os.path.join(self.output_directory, current_item.text())
            if os.path.exists(video_file):
                os.startfile(video_file)  # Windows

    def show_settings(self):
        """显示设置对话框"""
        QMessageBox.information(self, "设置", "设置功能正在开发中...")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         "AI智能视频剪辑软件 v1.0\n\n"
                         "基于AI技术的智能视频编辑工具\n"
                         "支持字幕改写、TTS语音合成、音画同步\n\n"
                         "技术栈: Python 3.13 + PyQt6 + DeepSeek + 阿里云TTS")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果有正在进行的任务，询问是否确认关闭
        if self.current_session_id:
            reply = QMessageBox.question(
                self, "确认关闭",
                "有任务正在进行中，确定要关闭程序吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return

            # 取消当前任务
            self.stop_processing()

        self.logger.info("主窗口正在关闭")
        event.accept()

    def _on_input_method_changed(self):
        """输入方式改变时的处理"""
        if self.input_method_text.isChecked():
            self.direct_text_input.setEnabled(True)
            self.file_path_input.setEnabled(False)
            self.browse_file_btn.setEnabled(False)
        else:
            self.direct_text_input.setEnabled(False)
            self.file_path_input.setEnabled(True)
            self.browse_file_btn.setEnabled(True)

    def _browse_subtitle_file(self):
        """浏览字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择字幕文件", "",
            "文本文件 (*.txt);;字幕文件 (*.srt);;所有文件 (*)"
        )

        if file_path:
            self.file_path_input.setText(file_path)

            # 自动加载文件内容到文本框
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 如果是SRT文件，提取文本内容
                if file_path.lower().endswith('.srt'):
                    content = self._extract_text_from_srt(content)

                self.direct_text_input.setText(content)
                self.logger.info(f"加载字幕文件: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"读取文件失败: {str(e)}")
                self.logger.error(f"读取字幕文件失败: {e}")

    def _browse_output_directory(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.direct_output_path.setText(directory)

    def _extract_text_from_srt(self, srt_content: str) -> str:
        """从SRT内容中提取纯文本"""
        lines = srt_content.strip().split('\n')
        text_lines = []

        for line in lines:
            line = line.strip()
            # 跳过序号行和时间码行
            if line and not line.isdigit() and '-->' not in line:
                text_lines.append(line)

        return '\n'.join(text_lines)

    def _preview_direct_subtitles(self):
        """预览字幕内容"""
        try:
            # 获取文本内容
            if self.input_method_text.isChecked():
                text_content = self.direct_text_input.toPlainText().strip()
            else:
                file_path = self.file_path_input.text().strip()
                if not file_path or not os.path.exists(file_path):
                    QMessageBox.warning(self, "警告", "请选择有效的字幕文件")
                    return

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                if file_path.lower().endswith('.srt'):
                    text_content = self._extract_text_from_srt(content)
                else:
                    text_content = content.strip()

            if not text_content:
                QMessageBox.warning(self, "警告", "请输入字幕内容")
                return

            # 分割成行
            lines = [line.strip() for line in text_content.split('\n') if line.strip()]

            # 创建预览对话框
            preview_text = f"共 {len(lines)} 条字幕：\n\n"
            for i, line in enumerate(lines, 1):
                preview_text += f"{i:2d}. {line}\n"

            QMessageBox.information(self, "字幕预览", preview_text)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"预览失败: {str(e)}")
            self.logger.error(f"字幕预览失败: {e}")

    def _start_direct_tts(self):
        """开始直接TTS合成"""
        try:
            # 获取文本内容
            if self.input_method_text.isChecked():
                text_content = self.direct_text_input.toPlainText().strip()
            else:
                file_path = self.file_path_input.text().strip()
                if not file_path or not os.path.exists(file_path):
                    QMessageBox.warning(self, "警告", "请选择有效的字幕文件")
                    return

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                if file_path.lower().endswith('.srt'):
                    text_content = self._extract_text_from_srt(content)
                else:
                    text_content = content.strip()

            if not text_content:
                QMessageBox.warning(self, "警告", "请输入字幕内容")
                return

            # 分割成行
            lines = [line.strip() for line in text_content.split('\n') if line.strip()]

            if not lines:
                QMessageBox.warning(self, "警告", "没有有效的字幕内容")
                return

            # 获取语音设置
            voice_text = self.direct_voice_combo.currentText()
            voice_code = voice_text.split(' ')[0]  # 提取语音代码

            # 获取输出目录
            output_dir = self.direct_output_path.text().strip()
            if not output_dir:
                output_dir = os.path.join(os.path.expanduser("~"), "Desktop", "TTS_Output")
                os.makedirs(output_dir, exist_ok=True)
                self.direct_output_path.setText(output_dir)

            # 确认开始合成
            reply = QMessageBox.question(
                self, "确认",
                f"即将开始直接TTS语音合成\n"
                f"字幕数量: {len(lines)} 条\n"
                f"语音类型: {voice_text}\n"
                f"输出目录: {output_dir}\n"
                f"预计耗时: {len(lines) * 2} 秒\n"
                f"是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 创建字幕集合
            from ..common.models import SubtitleCollection, SubtitleItem, SubtitleFormat, SubtitleStatus

            subtitle_items = []
            for i, line in enumerate(lines, 1):
                item = SubtitleItem(
                    index=i,
                    start_time=None,  # TTS前无时间码
                    end_time=None,
                    text=line,
                    status=SubtitleStatus.REWRITTEN
                )
                subtitle_items.append(item)

            subtitles = SubtitleCollection(
                items=subtitle_items,
                format=SubtitleFormat.SRT,
                language="zh-CN",
                encoding="utf-8",
                metadata={
                    "has_timecode": False,
                    "stage": "direct_tts",
                    "source": "manual_input"
                }
            )

            # 保存到实例变量
            self.current_direct_subtitles = subtitles
            self.current_direct_voice = voice_code
            self.current_direct_output = output_dir

            # 禁用按钮
            self.start_direct_tts_btn.setEnabled(False)
            self.start_direct_tts_btn.setText("合成中...")

            # 创建进度对话框
            self.direct_tts_progress = QProgressDialog("直接TTS语音合成中...", "取消", 0, 100, self)
            self.direct_tts_progress.setWindowModality(Qt.WindowModality.WindowModal)
            self.direct_tts_progress.setAutoClose(False)
            self.direct_tts_progress.show()

            # 启动TTS合成
            self._start_direct_tts_workflow(subtitles, voice_code, output_dir)

            self.logger.info(f"开始直接TTS语音合成: {len(lines)} 条字幕")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"直接TTS合成启动失败: {str(e)}")
            self.logger.error(f"直接TTS合成启动失败: {e}")

    def _start_direct_tts_workflow(self, subtitles, voice_code, output_dir):
        """启动直接TTS合成工作流程"""
        try:
            # 创建专用的直接TTS线程
            self.direct_tts_thread = DirectTTSWorkflowThread(subtitles, voice_code, output_dir, self.config_manager)

            # 连接信号
            self.direct_tts_thread.progress_updated.connect(self._on_direct_tts_progress)
            self.direct_tts_thread.tts_completed.connect(self._on_direct_tts_completed)
            self.direct_tts_thread.error_occurred.connect(self._on_direct_tts_error)

            # 启动线程
            self.direct_tts_thread.start()

        except Exception as e:
            self.logger.error(f"直接TTS工作流程启动失败: {e}")
            self._on_direct_tts_error(f"工作流程启动失败: {str(e)}")

    def _on_direct_tts_progress(self, progress, message):
        """直接TTS进度更新"""
        if hasattr(self, 'direct_tts_progress') and self.direct_tts_progress:
            self.direct_tts_progress.setValue(int(progress))
            self.direct_tts_progress.setLabelText(message)

    def _on_direct_tts_completed(self, result):
        """直接TTS合成完成"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'direct_tts_progress') and self.direct_tts_progress:
                self.direct_tts_progress.close()

            # 恢复按钮状态
            self.start_direct_tts_btn.setEnabled(True)
            self.start_direct_tts_btn.setText("开始TTS合成")

            if result.get("success", False):
                # 保存文件路径信息
                self.current_audio_file = result.get('audio_file')
                self.current_subtitle_file = result.get('subtitle_file')
                self.current_output_dir = result.get('output_dir')

                # 启用下载按钮
                if self.current_audio_file and os.path.exists(self.current_audio_file):
                    self.download_audio_btn.setEnabled(True)

                if self.current_subtitle_file and os.path.exists(self.current_subtitle_file):
                    self.download_subtitle_btn.setEnabled(True)

                if self.current_output_dir and os.path.exists(self.current_output_dir):
                    self.open_output_dir_btn.setEnabled(True)

                # 启用音画匹配中的"使用TTS"按钮
                if hasattr(self, 'use_tts_audio_btn'):
                    self.use_tts_audio_btn.setEnabled(True)
                if hasattr(self, 'use_tts_subtitle_btn'):
                    self.use_tts_subtitle_btn.setEnabled(True)

                # 显示结果
                result_text = (
                    f"✅ 直接TTS语音合成完成！\n\n"
                    f"📊 合成统计:\n"
                    f"• 字幕段数: {result.get('segment_count', 0)}\n"
                    f"• 总时长: {result.get('total_duration', 0):.2f}秒\n"
                    f"• 音频文件: {os.path.basename(self.current_audio_file) if self.current_audio_file else '未知'}\n"
                    f"• 字幕文件: {os.path.basename(self.current_subtitle_file) if self.current_subtitle_file else '未知'}\n\n"
                    f"📁 输出目录: {self.current_output_dir or '未知'}\n\n"
                    f"🎵 使用下方按钮下载文件或打开目录"
                )

                self.direct_tts_result.setText(result_text)

                QMessageBox.information(
                    self, "成功",
                    f"直接TTS语音合成完成！\n"
                    f"字幕段数: {result.get('segment_count', 0)}\n"
                    f"总时长: {result.get('total_duration', 0):.2f}秒\n"
                    f"文件已保存，可使用下载按钮获取"
                )

                self.logger.info(f"直接TTS合成完成: {result}")
            else:
                error_text = f"❌ 直接TTS合成失败: {result.get('message', '未知错误')}"
                self.direct_tts_result.setText(error_text)

                QMessageBox.critical(
                    self, "错误",
                    f"直接TTS合成失败: {result.get('message', '未知错误')}"
                )

        except Exception as e:
            self.logger.error(f"直接TTS完成处理失败: {e}")

    def _on_direct_tts_error(self, error_message):
        """直接TTS合成错误"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'direct_tts_progress') and self.direct_tts_progress:
                self.direct_tts_progress.close()

            # 恢复按钮状态
            self.start_direct_tts_btn.setEnabled(True)
            self.start_direct_tts_btn.setText("开始TTS合成")

            error_text = f"❌ 直接TTS合成错误: {error_message}"
            self.direct_tts_result.setText(error_text)

            QMessageBox.critical(self, "TTS合成错误", error_message)
            self.logger.error(f"直接TTS合成错误: {error_message}")

        except Exception as e:
            self.logger.error(f"直接TTS错误处理失败: {e}")

    def _download_audio_file(self):
        """下载音频文件"""
        try:
            if not self.current_audio_file or not os.path.exists(self.current_audio_file):
                QMessageBox.warning(self, "警告", "音频文件不存在")
                return

            # 获取保存路径
            file_name = os.path.basename(self.current_audio_file)
            save_path, _ = QFileDialog.getSaveFileName(
                self, "保存音频文件", file_name,
                "MP3文件 (*.mp3);;所有文件 (*)"
            )

            if save_path:
                # 复制文件
                import shutil
                shutil.copy2(self.current_audio_file, save_path)

                QMessageBox.information(
                    self, "成功",
                    f"音频文件已保存到:\n{save_path}"
                )
                self.logger.info(f"音频文件下载完成: {save_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"下载音频文件失败: {str(e)}")
            self.logger.error(f"下载音频文件失败: {e}")

    def _download_subtitle_file(self):
        """下载字幕文件"""
        try:
            if not self.current_subtitle_file or not os.path.exists(self.current_subtitle_file):
                QMessageBox.warning(self, "警告", "字幕文件不存在")
                return

            # 获取保存路径
            file_name = os.path.basename(self.current_subtitle_file)
            save_path, _ = QFileDialog.getSaveFileName(
                self, "保存字幕文件", file_name,
                "SRT字幕文件 (*.srt);;所有文件 (*)"
            )

            if save_path:
                # 复制文件
                import shutil
                shutil.copy2(self.current_subtitle_file, save_path)

                QMessageBox.information(
                    self, "成功",
                    f"字幕文件已保存到:\n{save_path}"
                )
                self.logger.info(f"字幕文件下载完成: {save_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"下载字幕文件失败: {str(e)}")
            self.logger.error(f"下载字幕文件失败: {e}")

    def _open_output_directory(self):
        """打开输出目录"""
        try:
            if not self.current_output_dir or not os.path.exists(self.current_output_dir):
                QMessageBox.warning(self, "警告", "输出目录不存在")
                return

            # 在文件管理器中打开目录
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                subprocess.run(['explorer', self.current_output_dir])
            elif system == "Darwin":  # macOS
                subprocess.run(['open', self.current_output_dir])
            else:  # Linux
                subprocess.run(['xdg-open', self.current_output_dir])

            self.logger.info(f"打开输出目录: {self.current_output_dir}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开目录失败: {str(e)}")
            self.logger.error(f"打开目录失败: {e}")

    def _browse_sync_audio(self):
        """浏览音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "",
            "音频文件 (*.mp3 *.wav *.aac *.m4a);;所有文件 (*)"
        )
        if file_path:
            self.sync_audio_path.setText(file_path)
            self._update_sync_file_info()

    def _browse_sync_video(self):
        """浏览视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )
        if file_path:
            self.sync_video_path.setText(file_path)
            self._update_sync_file_info()

    def _browse_sync_subtitle(self):
        """浏览字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择字幕文件", "",
            "字幕文件 (*.srt *.ass *.vtt);;所有文件 (*)"
        )
        if file_path:
            self.sync_subtitle_path.setText(file_path)

    def _browse_sync_output(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.sync_output_path.setText(dir_path)

    def _use_tts_audio(self):
        """使用TTS生成的音频"""
        if self.current_audio_file and os.path.exists(self.current_audio_file):
            self.sync_audio_path.setText(self.current_audio_file)
            self._update_sync_file_info()
        else:
            QMessageBox.warning(self, "警告", "没有可用的TTS音频文件")

    def _use_tts_subtitle(self):
        """使用TTS生成的字幕"""
        if self.current_subtitle_file and os.path.exists(self.current_subtitle_file):
            self.sync_subtitle_path.setText(self.current_subtitle_file)
        else:
            QMessageBox.warning(self, "警告", "没有可用的TTS字幕文件")

    def _update_sync_file_info(self):
        """更新文件信息显示"""
        try:
            # 更新音频信息
            audio_path = self.sync_audio_path.text().strip()
            if audio_path and os.path.exists(audio_path):
                audio_info = self._get_audio_info(audio_path)
                self.audio_info_label.setText(f"音频: {audio_info}")
            else:
                self.audio_info_label.setText("音频信息: 未选择")

            # 更新视频信息
            video_path = self.sync_video_path.text().strip()
            if video_path and os.path.exists(video_path):
                video_info = self._get_video_info(video_path)
                self.video_info_label.setText(f"视频: {video_info}")
            else:
                self.video_info_label.setText("视频信息: 未选择")

            # 更新按钮状态
            if audio_path and video_path and os.path.exists(audio_path) and os.path.exists(video_path):
                self.analyze_files_btn.setEnabled(True)
            else:
                self.analyze_files_btn.setEnabled(False)
                self.preview_sync_btn.setEnabled(False)
                self.start_sync_btn.setEnabled(False)

        except Exception as e:
            self.logger.error(f"更新文件信息失败: {e}")

    def _get_audio_info(self, audio_path: str) -> str:
        """获取音频文件信息"""
        try:
            import subprocess

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)

                duration = float(data['format']['duration'])
                size = int(data['format']['size'])

                # 获取音频流信息
                audio_stream = None
                for stream in data['streams']:
                    if stream['codec_type'] == 'audio':
                        audio_stream = stream
                        break

                if audio_stream:
                    sample_rate = audio_stream.get('sample_rate', 'Unknown')
                    channels = audio_stream.get('channels', 'Unknown')
                    return f"{duration:.2f}秒, {sample_rate}Hz, {channels}声道, {size//1024}KB"
                else:
                    return f"{duration:.2f}秒, {size//1024}KB"
            else:
                return "无法获取信息"

        except Exception as e:
            self.logger.error(f"获取音频信息失败: {e}")
            return "信息获取失败"

    def _get_video_info(self, video_path: str) -> str:
        """获取视频文件信息"""
        try:
            import subprocess

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)

                duration = float(data['format']['duration'])
                size = int(data['format']['size'])

                # 获取视频流信息
                video_stream = None
                for stream in data['streams']:
                    if stream['codec_type'] == 'video':
                        video_stream = stream
                        break

                if video_stream:
                    width = video_stream.get('width', 'Unknown')
                    height = video_stream.get('height', 'Unknown')
                    fps = eval(video_stream.get('r_frame_rate', '0/1'))
                    return f"{duration:.2f}秒, {width}x{height}, {fps:.2f}fps, {size//1024//1024}MB"
                else:
                    return f"{duration:.2f}秒, {size//1024//1024}MB"
            else:
                return "无法获取信息"

        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            return "信息获取失败"

    def _analyze_sync_files(self):
        """分析音视频文件"""
        try:
            audio_path = self.sync_audio_path.text().strip()
            video_path = self.sync_video_path.text().strip()

            if not audio_path or not video_path:
                QMessageBox.warning(self, "警告", "请选择音频和视频文件")
                return

            if not os.path.exists(audio_path) or not os.path.exists(video_path):
                QMessageBox.warning(self, "警告", "文件不存在")
                return

            # 获取文件信息
            audio_info = self._get_detailed_audio_info(audio_path)
            video_info = self._get_detailed_video_info(video_path)

            if not audio_info or not video_info:
                QMessageBox.critical(self, "错误", "无法分析文件信息")
                return

            # 计算匹配参数
            audio_duration = audio_info['duration']
            video_duration = video_info['duration']

            if self.sync_mode_auto.isChecked():
                speed_ratio = video_duration / audio_duration
                mode_desc = f"自动调速: 视频播放速度 {speed_ratio:.2f}x"
            elif self.sync_mode_crop.isChecked():
                mode_desc = f"裁剪视频: 保留前 {audio_duration:.2f} 秒"
            else:  # loop mode
                loop_count = int(audio_duration / video_duration) + 1
                mode_desc = f"循环播放: 视频循环 {loop_count} 次"

            # 显示分析结果
            result_text = (
                f"📊 文件分析完成\n\n"
                f"🎵 音频: {audio_duration:.2f}秒\n"
                f"🎬 视频: {video_duration:.2f}秒\n"
                f"⚙️ 匹配方案: {mode_desc}\n\n"
                f"✅ 可以开始匹配处理"
            )

            self.sync_result.setText(result_text)

            # 启用预览和开始按钮
            self.preview_sync_btn.setEnabled(True)
            self.start_sync_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"分析文件失败: {str(e)}")
            self.logger.error(f"分析文件失败: {e}")

    def _get_detailed_audio_info(self, audio_path: str) -> dict:
        """获取详细音频信息"""
        try:
            import subprocess
            import json

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return {
                    'duration': float(data['format']['duration']),
                    'size': int(data['format']['size']),
                    'format': data['format']['format_name']
                }
            return None

        except Exception as e:
            self.logger.error(f"获取详细音频信息失败: {e}")
            return None

    def _get_detailed_video_info(self, video_path: str) -> dict:
        """获取详细视频信息"""
        try:
            import subprocess
            import json

            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                data = json.loads(result.stdout)

                # 获取视频流信息
                video_stream = None
                for stream in data['streams']:
                    if stream['codec_type'] == 'video':
                        video_stream = stream
                        break

                info = {
                    'duration': float(data['format']['duration']),
                    'size': int(data['format']['size']),
                    'format': data['format']['format_name']
                }

                if video_stream:
                    info.update({
                        'width': video_stream.get('width', 0),
                        'height': video_stream.get('height', 0),
                        'fps': eval(video_stream.get('r_frame_rate', '0/1'))
                    })

                return info
            return None

        except Exception as e:
            self.logger.error(f"获取详细视频信息失败: {e}")
            return None

    def _preview_sync_effect(self):
        """预览同步效果"""
        try:
            # 这里可以实现预览功能，比如生成短片段预览
            QMessageBox.information(
                self, "预览",
                "预览功能开发中...\n"
                "将生成前10秒的预览片段供您查看效果"
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"预览失败: {str(e)}")
            self.logger.error(f"预览失败: {e}")

    def _start_video_sync(self):
        """开始音画匹配"""
        try:
            audio_path = self.sync_audio_path.text().strip()
            video_path = self.sync_video_path.text().strip()
            subtitle_path = self.sync_subtitle_path.text().strip()
            output_dir = self.sync_output_path.text().strip()

            if not output_dir:
                output_dir = os.path.join(os.path.expanduser("~"), "Desktop", "VideoSync_Output")
                self.sync_output_path.setText(output_dir)

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 禁用按钮
            self.start_sync_btn.setEnabled(False)
            self.start_sync_btn.setText("处理中...")

            # 创建并启动视频同步线程
            self.video_sync_thread = VideoSyncThread(
                audio_path=audio_path,
                video_path=video_path,
                subtitle_path=subtitle_path if subtitle_path else None,
                output_dir=output_dir,
                sync_mode="auto" if self.sync_mode_auto.isChecked() else
                         "crop" if self.sync_mode_crop.isChecked() else "loop"
            )

            self.video_sync_thread.progress_updated.connect(self._on_sync_progress)
            self.video_sync_thread.sync_completed.connect(self._on_sync_completed)
            self.video_sync_thread.error_occurred.connect(self._on_sync_error)

            self.video_sync_thread.start()

            self.sync_result.setText("🎬 开始音画匹配处理...\n请稍候，这可能需要几分钟时间")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动音画匹配失败: {str(e)}")
            self.logger.error(f"启动音画匹配失败: {e}")
            self.start_sync_btn.setEnabled(True)
            self.start_sync_btn.setText("🎬 开始匹配")

    def _on_sync_progress(self, progress: float, message: str):
        """音画匹配进度更新"""
        self.sync_result.setText(f"🎬 音画匹配进度: {progress:.1f}%\n{message}")

    def _on_sync_completed(self, result: dict):
        """音画匹配完成"""
        try:
            self.start_sync_btn.setEnabled(True)
            self.start_sync_btn.setText("🎬 开始匹配")

            if result.get("success", False):
                result_text = (
                    f"✅ 音画匹配完成！\n\n"
                    f"📁 输出文件: {result.get('output_file', '未知')}\n"
                    f"⏱️ 处理时长: {result.get('processing_time', 0):.1f}秒\n"
                    f"📊 输出时长: {result.get('output_duration', 0):.1f}秒\n\n"
                    f"🎉 您可以在输出目录中查看生成的视频文件"
                )

                self.sync_result.setText(result_text)

                QMessageBox.information(
                    self, "成功",
                    f"音画匹配完成！\n输出文件: {os.path.basename(result.get('output_file', ''))}"
                )
            else:
                error_msg = result.get('message', '未知错误')
                self.sync_result.setText(f"❌ 音画匹配失败: {error_msg}")
                QMessageBox.critical(self, "失败", f"音画匹配失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"音画匹配完成处理失败: {e}")

    def _on_sync_error(self, error_message: str):
        """音画匹配错误处理"""
        try:
            self.start_sync_btn.setEnabled(True)
            self.start_sync_btn.setText("🎬 开始匹配")

            self.sync_result.setText(f"❌ 音画匹配错误: {error_message}")
            QMessageBox.critical(self, "错误", f"音画匹配错误: {error_message}")

        except Exception as e:
            self.logger.error(f"音画匹配错误处理失败: {e}")

    def _start_tts_and_match_workflow(self, subtitles):
        """启动TTS合成和音画匹配工作流程"""
        try:
            # 创建并启动TTS和匹配工作线程
            self.tts_match_thread = TTSAndMatchWorkflowThread(
                subtitles=subtitles,
                video_file=self.video_file_path,
                original_subtitles=getattr(self, 'current_subtitles', None),
                subtitle_mapping=self._get_current_mapping(),
                output_dir=os.path.join(os.path.expanduser("~"), "Desktop", "AI_Video_Output")
            )

            self.tts_match_thread.progress_updated.connect(self._on_tts_match_progress)
            self.tts_match_thread.workflow_completed.connect(self._on_tts_match_completed)
            self.tts_match_thread.error_occurred.connect(self._on_tts_match_error)

            self.tts_match_thread.start()

        except Exception as e:
            self.logger.error(f"启动TTS和匹配工作流程失败: {e}")
            self._on_tts_match_error(f"启动工作流程失败: {str(e)}")

    def _get_current_mapping(self) -> Dict[str, List[int]]:
        """获取当前的字幕映射关系"""
        try:
            self.logger.info("开始获取映射关系...")

            # 检查是否有保存的映射关系
            if hasattr(self, 'current_mappings'):
                self.logger.info(f"找到current_mappings属性: {type(self.current_mappings)}")
                if self.current_mappings:
                    self.logger.info(f"使用保存的映射关系: {self.current_mappings}")
                    return self.current_mappings
                else:
                    self.logger.warning("current_mappings为空")
            else:
                self.logger.warning("未找到current_mappings属性")

            # 如果没有映射关系，创建默认的1:1映射
            self.logger.info("创建默认1:1映射关系...")
            mapping = {}
            if hasattr(self, 'current_rewritten_subtitles') and self.current_rewritten_subtitles:
                subtitle_count = len(self.current_rewritten_subtitles.items)
                for i in range(subtitle_count):
                    mapping[f"rewritten_{i}"] = [i]
                self.logger.warning(f"使用默认1:1映射关系: {subtitle_count}条字幕 -> {mapping}")
            else:
                self.logger.error("未找到改写字幕数据，无法创建映射关系")

            return mapping

        except Exception as e:
            self.logger.error(f"获取映射关系失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return {}

    def _perform_audio_video_sync(self, tts_result: dict, video_result: dict, match_result) -> dict:
        """执行音画同步"""
        try:
            from ..services.audio_video_sync import AudioVideoSyncService

            # 获取必要的文件路径
            audio_file = tts_result.get("audio_file")
            subtitle_file = tts_result.get("subtitle_file")
            video_segments_dir = video_result.get("output_dir")

            if not all([audio_file, subtitle_file, video_segments_dir]):
                return {
                    "success": False,
                    "message": "缺少必要的文件路径"
                }

            # 获取映射关系
            mapping_relations = self._get_current_mapping()
            if not mapping_relations:
                return {
                    "success": False,
                    "message": "没有找到映射关系"
                }

            # 创建同步输出目录
            sync_output_dir = os.path.join(self.output_dir, "final_output")

            # 执行音画同步
            sync_service = AudioVideoSyncService()
            sync_result = sync_service.sync_audio_video(
                audio_file=audio_file,
                subtitle_file=subtitle_file,
                video_segments_dir=video_segments_dir,
                mapping_relations=mapping_relations,
                output_dir=sync_output_dir
            )

            if sync_result.success:
                return {
                    "success": True,
                    "output_dir": sync_output_dir,
                    "sync_results": sync_result.metadata,
                    "message": "音画同步完成"
                }
            else:
                return {
                    "success": False,
                    "message": sync_result.message
                }

        except Exception as e:
            self.logger.error(f"音画同步执行失败: {e}")
            return {
                "success": False,
                "message": f"音画同步异常: {str(e)}"
            }

    def _convert_mappings_to_dict(self, mappings) -> Dict[str, List[int]]:
        """将映射关系转换为字典格式"""
        try:
            if not mappings:
                self.logger.warning("映射关系为空")
                return {}

            # 如果已经是字典格式，直接返回
            if isinstance(mappings, dict):
                self.logger.info("映射关系已是字典格式")
                return mappings

            # 如果是MappingRelation对象列表，转换为字典
            if isinstance(mappings, list):
                mapping_dict = {}
                for mapping in mappings:
                    if hasattr(mapping, 'rewritten_index') and hasattr(mapping, 'original_indices'):
                        key = f"rewritten_{mapping.rewritten_index}"
                        mapping_dict[key] = mapping.original_indices
                    else:
                        self.logger.warning(f"映射对象格式不正确: {mapping}")

                self.logger.info(f"转换映射关系: {len(mappings)} 个对象 -> {len(mapping_dict)} 个映射")
                return mapping_dict

            # 其他格式，尝试从metadata中获取
            if hasattr(mappings, 'metadata') and 'mappings' in mappings.metadata:
                return mappings.metadata['mappings']

            self.logger.error(f"无法识别的映射关系格式: {type(mappings)}")
            return {}

        except Exception as e:
            self.logger.error(f"转换映射关系失败: {e}")
            return {}

    def _on_tts_match_progress(self, progress: float, message: str):
        """TTS和匹配进度更新"""
        if hasattr(self, 'tts_progress_dialog') and self.tts_progress_dialog:
            self.tts_progress_dialog.setValue(int(progress))
            self.tts_progress_dialog.setLabelText(message)

    def _on_tts_match_completed(self, result: dict):
        """TTS和匹配完成"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'tts_progress_dialog') and self.tts_progress_dialog:
                self.tts_progress_dialog.close()

            # 恢复按钮状态
            self.tts_synthesis_btn.setEnabled(True)
            self.tts_synthesis_btn.setText("TTS语音合成")

            if result.get("success", False):
                # 保存结果数据
                self.tts_match_result = result

                # 更新音画匹配界面的数据
                self._update_match_interface_with_result(result)

                # 自动跳转到音画匹配标签页
                self._switch_to_match_tab()

                QMessageBox.information(
                    self, "成功",
                    f"TTS语音合成和音画匹配完成！\n"
                    f"音频文件: {os.path.basename(result.get('audio_file', ''))}\n"
                    f"匹配段数: {result.get('match_count', 0)}\n"
                    f"已自动跳转到音画匹配界面"
                )

                self.logger.info(f"TTS和匹配完成: {result}")
            else:
                error_msg = result.get('message', '未知错误')
                QMessageBox.critical(self, "失败", f"TTS合成和匹配失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"TTS和匹配完成处理失败: {e}")

    def _on_tts_match_error(self, error_message: str):
        """TTS和匹配错误处理"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'tts_progress_dialog') and self.tts_progress_dialog:
                self.tts_progress_dialog.close()

            # 恢复按钮状态
            self.tts_synthesis_btn.setEnabled(True)
            self.tts_synthesis_btn.setText("TTS语音合成")

            QMessageBox.critical(self, "TTS和匹配错误", error_message)
            self.logger.error(f"TTS和匹配错误: {error_message}")

        except Exception as e:
            self.logger.error(f"TTS和匹配错误处理失败: {e}")

    def _update_match_interface_with_result(self, result: dict):
        """更新音画匹配界面的结果数据"""
        try:
            # 更新匹配结果显示
            if hasattr(self, 'match_result_table'):
                self._display_match_results(result.get('match_data', []))

            # 设置输出目录
            if hasattr(self, 'match_output_path') and result.get('output_dir'):
                self.match_output_path.setText(result.get('output_dir'))

            # 更新状态显示
            if hasattr(self, 'match_status_display'):
                match_count = result.get('match_count', 0)
                self.match_status_display.setText(
                    f"✅ 音画匹配完成！\n"
                    f"共匹配 {match_count} 个字幕段\n"
                    f"可以预览结果或导出文件"
                )

        except Exception as e:
            self.logger.error(f"更新匹配界面失败: {e}")

    def _switch_to_match_tab(self):
        """切换到音画匹配标签页"""
        try:
            # 使用保存的标签页组件
            if hasattr(self, 'main_tab_widget') and self.main_tab_widget:
                tab_widget = self.main_tab_widget

                for i in range(tab_widget.count()):
                    tab_text = tab_widget.tabText(i)
                    if "音画匹配" in tab_text:
                        tab_widget.setCurrentIndex(i)
                        self.logger.info(f"已切换到音画匹配标签页 (索引: {i})")

                        # 强制刷新界面
                        tab_widget.repaint()
                        self.repaint()

                        return True

                self.logger.warning("未找到音画匹配标签页")
                return False
            else:
                self.logger.error("标签页组件不存在")
                return False
        except Exception as e:
            self.logger.error(f"切换标签页失败: {e}")
            return False

    def _browse_match_output(self):
        """浏览匹配输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.match_output_path.setText(dir_path)

    def _display_match_results(self, match_data: List[Dict]):
        """显示匹配结果"""
        try:
            self.match_result_table.setRowCount(len(match_data))

            for i, match_item in enumerate(match_data):
                # 序号
                self.match_result_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

                # 字幕内容
                subtitle_text = match_item.get('subtitle_text', '')
                display_text = subtitle_text[:30] + "..." if len(subtitle_text) > 30 else subtitle_text
                self.match_result_table.setItem(i, 1, QTableWidgetItem(display_text))

                # 音频时长
                audio_duration = match_item.get('audio_duration', 0)
                self.match_result_table.setItem(i, 2, QTableWidgetItem(f"{audio_duration:.2f}s"))

                # 视频片段数量
                video_count = match_item.get('video_count', 0)
                self.match_result_table.setItem(i, 3, QTableWidgetItem(str(video_count)))

                # 视频总时长
                total_video_duration = match_item.get('total_video_duration', 0)
                self.match_result_table.setItem(i, 4, QTableWidgetItem(f"{total_video_duration:.2f}s"))

                # 速度因子
                speed_factor = match_item.get('speed_factor', 1.0)
                self.match_result_table.setItem(i, 5, QTableWidgetItem(f"{speed_factor:.2f}x"))

            # 启用操作按钮
            self.preview_match_btn.setEnabled(True)
            self.edit_match_btn.setEnabled(True)
            self.refresh_match_btn.setEnabled(True)
            self.export_results_btn.setEnabled(True)

            # 更新状态显示
            self.match_status_display.setText(
                f"✅ 音画匹配完成！\n"
                f"共匹配 {len(match_data)} 个字幕段\n"
                f"可以预览结果或导出文件"
            )

        except Exception as e:
            self.logger.error(f"显示匹配结果失败: {e}")

    def _preview_selected_match(self):
        """预览选中的匹配项"""
        try:
            current_row = self.match_result_table.currentRow()
            if current_row >= 0:
                QMessageBox.information(
                    self, "预览",
                    f"预览功能开发中...\n"
                    f"将显示第 {current_row + 1} 项的音频和视频预览"
                )
            else:
                QMessageBox.warning(self, "警告", "请先选择要预览的项目")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"预览失败: {str(e)}")
            self.logger.error(f"预览失败: {e}")

    def _export_match_results(self):
        """导出匹配结果"""
        try:
            if not hasattr(self, 'tts_match_result') or not self.tts_match_result:
                QMessageBox.warning(self, "警告", "没有可导出的匹配结果")
                return

            # 获取输出目录
            output_dir = self.match_output_path.text().strip()
            if not output_dir:
                output_dir = os.path.join(os.path.expanduser("~"), "Desktop", "AI_Video_Output")
                self.match_output_path.setText(output_dir)

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 复制结果文件到指定目录
            result = self.tts_match_result

            success_files = []
            failed_files = []

            # 复制音频文件
            if result.get('audio_file') and os.path.exists(result['audio_file']):
                audio_target = os.path.join(output_dir, f"New_{os.path.basename(result['audio_file'])}")
                try:
                    import shutil
                    shutil.copy2(result['audio_file'], audio_target)
                    success_files.append(f"音频文件: {os.path.basename(audio_target)}")
                except Exception as e:
                    failed_files.append(f"音频文件: {str(e)}")

            # 复制字幕文件
            if result.get('subtitle_file') and os.path.exists(result['subtitle_file']):
                subtitle_target = os.path.join(output_dir, f"New_{os.path.basename(result['subtitle_file'])}")
                try:
                    import shutil
                    shutil.copy2(result['subtitle_file'], subtitle_target)
                    success_files.append(f"字幕文件: {os.path.basename(subtitle_target)}")
                except Exception as e:
                    failed_files.append(f"字幕文件: {str(e)}")

            # 显示结果
            if success_files:
                QMessageBox.information(
                    self, "导出成功",
                    f"文件已导出到: {output_dir}\n\n"
                    f"成功导出:\n" + "\n".join(success_files) +
                    (f"\n\n失败:\n" + "\n".join(failed_files) if failed_files else "")
                )

                # 打开输出目录
                try:
                    import subprocess
                    import platform

                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['explorer', output_dir])
                    elif system == "Darwin":  # macOS
                        subprocess.run(['open', output_dir])
                    else:  # Linux
                        subprocess.run(['xdg-open', output_dir])
                except:
                    pass
            else:
                QMessageBox.critical(self, "导出失败", "没有文件成功导出")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
            self.logger.error(f"导出失败: {e}")

    def _edit_selected_match(self):
        """编辑选中的匹配项"""
        try:
            current_row = self.match_result_table.currentRow()
            if current_row >= 0:
                # 获取当前匹配数据
                if hasattr(self, 'tts_match_result') and self.tts_match_result:
                    match_data = self.tts_match_result.get('match_data', [])
                    if current_row < len(match_data):
                        match_item = match_data[current_row]

                        # 打开编辑对话框
                        self._open_match_edit_dialog(current_row, match_item)
                    else:
                        QMessageBox.warning(self, "警告", "选中的项目数据不存在")
                else:
                    QMessageBox.warning(self, "警告", "没有匹配数据可编辑")
            else:
                QMessageBox.warning(self, "警告", "请先选择要编辑的项目")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑失败: {str(e)}")
            self.logger.error(f"编辑匹配失败: {e}")

    def _open_match_edit_dialog(self, row_index: int, match_item: dict):
        """打开匹配编辑对话框"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QPushButton, QMessageBox

            dialog = QDialog(self)
            dialog.setWindowTitle(f"编辑匹配项 {row_index + 1}")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # 字幕信息
            subtitle_info = QLabel(f"字幕内容: {match_item.get('subtitle_text', '')}")
            layout.addWidget(subtitle_info)

            audio_info = QLabel(f"音频时长: {match_item.get('audio_duration', 0):.2f}秒")
            layout.addWidget(audio_info)

            # 视频片段列表
            layout.addWidget(QLabel("匹配的视频片段:"))

            video_list = QListWidget()
            video_files = match_item.get('video_files', [])
            for i, video_file in enumerate(video_files):
                video_list.addItem(f"片段 {i+1}: {os.path.basename(video_file)}")
            layout.addWidget(video_list)

            # 操作按钮
            button_layout = QHBoxLayout()

            add_btn = QPushButton("➕ 添加片段")
            add_btn.clicked.connect(lambda: self._add_video_segment(video_list, match_item))
            button_layout.addWidget(add_btn)

            remove_btn = QPushButton("➖ 移除片段")
            remove_btn.clicked.connect(lambda: self._remove_video_segment(video_list, match_item))
            button_layout.addWidget(remove_btn)

            replace_btn = QPushButton("🔄 替换片段")
            replace_btn.clicked.connect(lambda: self._replace_video_segment(video_list, match_item))
            button_layout.addWidget(replace_btn)

            layout.addLayout(button_layout)

            # 确认按钮
            confirm_layout = QHBoxLayout()

            save_btn = QPushButton("💾 保存")
            save_btn.clicked.connect(lambda: self._save_match_changes(dialog, row_index, match_item))
            confirm_layout.addWidget(save_btn)

            cancel_btn = QPushButton("❌ 取消")
            cancel_btn.clicked.connect(dialog.reject)
            confirm_layout.addWidget(cancel_btn)

            layout.addLayout(confirm_layout)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开编辑对话框失败: {str(e)}")
            self.logger.error(f"打开编辑对话框失败: {e}")

    def _add_video_segment(self, video_list, match_item):
        """添加视频片段"""
        QMessageBox.information(self, "提示", "添加视频片段功能开发中...")

    def _remove_video_segment(self, video_list, match_item):
        """移除视频片段"""
        current_row = video_list.currentRow()
        if current_row >= 0:
            video_files = match_item.get('video_files', [])
            if current_row < len(video_files):
                video_files.pop(current_row)
                video_list.takeItem(current_row)
                match_item['video_count'] = len(video_files)
                QMessageBox.information(self, "成功", "视频片段已移除")
        else:
            QMessageBox.warning(self, "警告", "请先选择要移除的片段")

    def _replace_video_segment(self, video_list, match_item):
        """替换视频片段"""
        QMessageBox.information(self, "提示", "替换视频片段功能开发中...")

    def _save_match_changes(self, dialog, row_index, match_item):
        """保存匹配更改"""
        try:
            # 更新表格显示
            self._update_table_row(row_index, match_item)

            # 更新保存的数据
            if hasattr(self, 'tts_match_result') and self.tts_match_result:
                match_data = self.tts_match_result.get('match_data', [])
                if row_index < len(match_data):
                    match_data[row_index] = match_item

            dialog.accept()
            QMessageBox.information(self, "成功", "匹配更改已保存")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存更改失败: {str(e)}")

    def _update_table_row(self, row_index, match_item):
        """更新表格行"""
        try:
            # 更新视频片段数量
            video_count = match_item.get('video_count', 0)
            self.match_result_table.setItem(row_index, 3, QTableWidgetItem(str(video_count)))

            # 重新计算视频总时长（这里简化处理）
            # 实际应该重新计算所有片段的总时长

        except Exception as e:
            self.logger.error(f"更新表格行失败: {e}")

    def _refresh_match_results(self):
        """刷新匹配结果"""
        try:
            if hasattr(self, 'tts_match_result') and self.tts_match_result:
                match_data = self.tts_match_result.get('match_data', [])
                self._display_match_results(match_data)
                QMessageBox.information(self, "成功", "匹配结果已刷新")
            else:
                QMessageBox.warning(self, "警告", "没有匹配数据可刷新")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新失败: {str(e)}")
            self.logger.error(f"刷新匹配结果失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("AI智能视频剪辑软件")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Video Editor")

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
