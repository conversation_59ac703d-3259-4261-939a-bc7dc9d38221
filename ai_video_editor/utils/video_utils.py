#!/usr/bin/env python3
"""
视频处理工具类

提供视频文件的基本操作功能
"""

import os
import subprocess
import logging
from typing import Optional, Tuple


class VideoUtils:
    """视频处理工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_video_duration(self, video_file: str) -> float:
        """
        获取视频文件时长
        
        Args:
            video_file: 视频文件路径
            
        Returns:
            float: 视频时长（秒），失败返回0.0
        """
        try:
            if not os.path.exists(video_file):
                self.logger.warning(f"视频文件不存在: {video_file}")
                return 0.0
            
            # 使用ffprobe获取视频时长
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                video_file
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                duration_str = result.stdout.strip()
                if duration_str:
                    duration = float(duration_str)
                    self.logger.debug(f"视频时长: {video_file} = {duration:.3f}秒")
                    return duration
            
            self.logger.error(f"获取视频时长失败: {result.stderr}")
            return 0.0
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"获取视频时长超时: {video_file}")
            return 0.0
        except Exception as e:
            self.logger.error(f"获取视频时长异常: {video_file}, {e}")
            return 0.0
    
    def get_video_info(self, video_file: str) -> dict:
        """
        获取视频文件详细信息
        
        Args:
            video_file: 视频文件路径
            
        Returns:
            dict: 视频信息字典
        """
        try:
            if not os.path.exists(video_file):
                return {}
            
            # 使用ffprobe获取详细信息
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_file
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                import json
                info = json.loads(result.stdout)
                
                # 提取关键信息
                video_info = {
                    "duration": 0.0,
                    "width": 0,
                    "height": 0,
                    "fps": 0.0,
                    "codec": "",
                    "bitrate": 0
                }
                
                # 从format中获取时长
                if "format" in info:
                    format_info = info["format"]
                    if "duration" in format_info:
                        video_info["duration"] = float(format_info["duration"])
                    if "bit_rate" in format_info:
                        video_info["bitrate"] = int(format_info["bit_rate"])
                
                # 从streams中获取视频流信息
                if "streams" in info:
                    for stream in info["streams"]:
                        if stream.get("codec_type") == "video":
                            video_info["width"] = stream.get("width", 0)
                            video_info["height"] = stream.get("height", 0)
                            video_info["codec"] = stream.get("codec_name", "")
                            
                            # 计算帧率
                            fps_str = stream.get("r_frame_rate", "0/1")
                            if "/" in fps_str:
                                num, den = fps_str.split("/")
                                if int(den) != 0:
                                    video_info["fps"] = float(num) / float(den)
                            break
                
                return video_info
            
            return {}
            
        except Exception as e:
            self.logger.error(f"获取视频信息异常: {video_file}, {e}")
            return {}
    
    def extract_video_segment(self, 
                            input_file: str,
                            output_file: str,
                            start_time: float,
                            duration: float) -> bool:
        """
        提取视频片段
        
        Args:
            input_file: 输入视频文件
            output_file: 输出视频文件
            start_time: 开始时间（秒）
            duration: 持续时间（秒）
            
        Returns:
            bool: 是否成功
        """
        try:
            cmd = [
                "ffmpeg", "-y",
                "-i", input_file,
                "-ss", str(start_time),
                "-t", str(duration),
                "-c", "copy",
                output_file
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                self.logger.info(f"视频片段提取成功: {output_file}")
                return True
            else:
                self.logger.error(f"视频片段提取失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"视频片段提取异常: {e}")
            return False
    
    def change_video_speed(self,
                          input_file: str,
                          output_file: str,
                          speed_factor: float) -> bool:
        """
        改变视频播放速度
        
        Args:
            input_file: 输入视频文件
            output_file: 输出视频文件
            speed_factor: 速度因子（>1加速，<1减速）
            
        Returns:
            bool: 是否成功
        """
        try:
            if abs(speed_factor - 1.0) < 0.01:
                # 速度接近1.0，直接复制
                import shutil
                shutil.copy2(input_file, output_file)
                return True
            
            # 计算PTS因子
            pts_factor = 1.0 / speed_factor
            
            cmd = [
                "ffmpeg", "-y",
                "-i", input_file,
                "-filter:v", f"setpts={pts_factor}*PTS",
                "-c:v", "libx264",
                "-preset", "fast",
                "-crf", "23",
                output_file
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                self.logger.info(f"视频变速成功: {speed_factor:.2f}x -> {output_file}")
                return True
            else:
                self.logger.error(f"视频变速失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"视频变速异常: {e}")
            return False
    
    def remove_audio_from_video(self, input_file: str, output_file: str) -> bool:
        """
        从视频中移除音频
        
        Args:
            input_file: 输入视频文件
            output_file: 输出视频文件
            
        Returns:
            bool: 是否成功
        """
        try:
            cmd = [
                "ffmpeg", "-y",
                "-i", input_file,
                "-c:v", "copy",
                "-an",  # 移除音频
                output_file
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                self.logger.info(f"视频音频移除成功: {output_file}")
                return True
            else:
                self.logger.error(f"视频音频移除失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"视频音频移除异常: {e}")
            return False
    
    def check_ffmpeg_available(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def check_ffprobe_available(self) -> bool:
        """检查FFprobe是否可用"""
        try:
            result = subprocess.run(
                ["ffprobe", "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
