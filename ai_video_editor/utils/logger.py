"""
Logger utility - 日志工具

提供统一的日志配置和管理功能
"""

import os
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional


def setup_logger(name: str = None, level: str = "INFO", 
                log_file: str = None, max_file_size: int = 10*1024*1024,
                backup_count: int = 5) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称，默认为根记录器
        level: 日志级别
        log_file: 日志文件路径，如果为None则只输出到控制台
        max_file_size: 日志文件最大大小（字节）
        backup_count: 备份文件数量
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 使用RotatingFileHandler支持日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_default_log_file() -> str:
    """
    获取默认日志文件路径
    
    Returns:
        str: 默认日志文件路径
    """
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    logs_dir = project_root / "logs"
    
    # 创建日志目录
    logs_dir.mkdir(exist_ok=True)
    
    # 生成日志文件名（包含日期）
    today = datetime.now().strftime("%Y-%m-%d")
    log_file = logs_dir / f"ai_video_editor_{today}.log"
    
    return str(log_file)


class SessionLogger:
    """会话日志记录器"""
    
    def __init__(self, session_id: str, base_logger: logging.Logger = None):
        """
        初始化会话日志记录器
        
        Args:
            session_id: 会话ID
            base_logger: 基础日志记录器，如果为None则使用默认记录器
        """
        self.session_id = session_id
        self.logger = base_logger or logging.getLogger(__name__)
    
    def _format_message(self, message: str) -> str:
        """格式化消息，添加会话ID"""
        return f"[{self.session_id}] {message}"
    
    def debug(self, message: str, **kwargs):
        """记录调试信息"""
        self.logger.debug(self._format_message(message), **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录信息"""
        self.logger.info(self._format_message(message), **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告"""
        self.logger.warning(self._format_message(message), **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误"""
        self.logger.error(self._format_message(message), **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误"""
        self.logger.critical(self._format_message(message), **kwargs)
    
    def log_step(self, step_name: str, status: str, details: Optional[dict] = None):
        """记录处理步骤"""
        message = f"步骤: {step_name}, 状态: {status}"
        if details:
            message += f", 详情: {details}"
        self.info(message)
    
    def log_progress(self, step: str, progress: float, message: str = ""):
        """记录进度信息"""
        progress_msg = f"进度: {step} - {progress:.1f}%"
        if message:
            progress_msg += f" - {message}"
        self.info(progress_msg)
    
    def log_error_with_context(self, error: Exception, context: dict = None):
        """记录带上下文的错误"""
        error_msg = f"错误: {str(error)}"
        if context:
            error_msg += f", 上下文: {context}"
        self.error(error_msg, exc_info=True)


# 默认日志记录器
_default_logger: Optional[logging.Logger] = None


def setup_logging(level: str = "INFO") -> None:
    """
    设置全局日志配置

    Args:
        level: 日志级别
    """
    global _default_logger

    if _default_logger is None:
        log_file = get_default_log_file()
        _default_logger = setup_logger(
            name="ai_video_editor",
            level=level,
            log_file=log_file
        )


def get_default_logger() -> logging.Logger:
    """
    获取默认日志记录器

    Returns:
        logging.Logger: 默认日志记录器
    """
    global _default_logger

    if _default_logger is None:
        setup_logging()

    return _default_logger


# 便捷函数
def debug(message: str, **kwargs):
    """记录调试信息"""
    get_default_logger().debug(message, **kwargs)


def info(message: str, **kwargs):
    """记录信息"""
    get_default_logger().info(message, **kwargs)


def warning(message: str, **kwargs):
    """记录警告"""
    get_default_logger().warning(message, **kwargs)


def error(message: str, **kwargs):
    """记录错误"""
    get_default_logger().error(message, **kwargs)


def critical(message: str, **kwargs):
    """记录严重错误"""
    get_default_logger().critical(message, **kwargs)


# 使用示例
if __name__ == "__main__":
    # 设置日志
    logger = setup_logger("test", "DEBUG", "test.log")
    
    # 测试日志输出
    logger.debug("这是调试信息")
    logger.info("这是信息")
    logger.warning("这是警告")
    logger.error("这是错误")
    
    # 测试会话日志
    session_logger = SessionLogger("test_session", logger)
    session_logger.info("会话开始")
    session_logger.log_step("测试步骤", "成功")
    session_logger.log_progress("测试进度", 50.0, "进行中")
    
    print("日志测试完成")
