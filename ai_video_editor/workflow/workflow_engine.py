"""
Workflow Engine - 工作流程引擎

实现异步流水线，整合所有处理步骤，支持进度跟踪和错误处理
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field
import tempfile
import os
import shutil

from ..common.models import (
    BaseResult, ProcessingContext, FileInfo, FileType,
    SubtitleCollection, ProcessingError, ErrorSeverity
)
from ..common.interfaces import BaseProcessor
from ..common.types import SessionIdType, ProgressPercentType


class WorkflowStatus(Enum):
    """工作流程状态"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskStatus(Enum):
    """任务状态"""
    WAITING = "waiting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class WorkflowTask:
    """工作流程任务"""
    id: str
    name: str
    processor: BaseProcessor
    dependencies: List[str] = field(default_factory=list)
    status: TaskStatus = TaskStatus.WAITING
    progress: float = 0.0
    result: Optional[BaseResult] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error: Optional[Exception] = None
    retry_count: int = 0
    max_retries: int = 3
    
    @property
    def duration(self) -> float:
        """获取任务执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0
    
    @property
    def is_completed(self) -> bool:
        """任务是否已完成"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.SKIPPED]
    
    def can_run(self, completed_tasks: set) -> bool:
        """检查任务是否可以运行"""
        return (self.status == TaskStatus.WAITING and 
                all(dep in completed_tasks for dep in self.dependencies))


@dataclass
class WorkflowConfig:
    """工作流程配置"""
    session_id: SessionIdType
    input_files: Dict[FileType, FileInfo]
    output_directory: str
    temp_directory: Optional[str] = None
    style_config: Dict[str, Any] = field(default_factory=dict)
    processing_config: Dict[str, Any] = field(default_factory=dict)
    cleanup_temp: bool = True
    max_concurrent_tasks: int = 3
    
    def __post_init__(self):
        if self.temp_directory is None:
            self.temp_directory = tempfile.mkdtemp(prefix=f"workflow_{self.session_id}_")


@dataclass
class WorkflowProgress:
    """工作流程进度"""
    total_tasks: int
    completed_tasks: int
    current_task: Optional[str] = None
    current_task_progress: float = 0.0
    overall_progress: float = 0.0
    status: WorkflowStatus = WorkflowStatus.PENDING
    message: str = ""
    
    def update_overall_progress(self):
        """更新总体进度"""
        if self.total_tasks > 0:
            task_progress = self.completed_tasks / self.total_tasks * 100
            current_progress = self.current_task_progress / self.total_tasks if self.current_task else 0
            self.overall_progress = min(100.0, task_progress + current_progress)


class WorkflowEngine:
    """工作流程引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.tasks: Dict[str, WorkflowTask] = {}
        self.task_order: List[str] = []
        self.progress_callbacks: List[Callable[[WorkflowProgress], None]] = []
        self.error_callbacks: List[Callable[[ProcessingError], None]] = []
        self.current_workflow: Optional[WorkflowConfig] = None
        self.context: Optional[ProcessingContext] = None
        self.progress: WorkflowProgress = WorkflowProgress(0, 0)
        self._running = False
        self._cancelled = False
    
    def add_task(self, task_id: str, name: str, processor: BaseProcessor, 
                 dependencies: List[str] = None, max_retries: int = 3) -> 'WorkflowEngine':
        """
        添加任务到工作流程
        
        Args:
            task_id: 任务ID
            name: 任务名称
            processor: 处理器实例
            dependencies: 依赖的任务ID列表
            max_retries: 最大重试次数
            
        Returns:
            WorkflowEngine: 返回自身以支持链式调用
        """
        task = WorkflowTask(
            id=task_id,
            name=name,
            processor=processor,
            dependencies=dependencies or [],
            max_retries=max_retries
        )
        
        self.tasks[task_id] = task
        if task_id not in self.task_order:
            self.task_order.append(task_id)
        
        self.logger.info(f"添加任务: {task_id} - {name}")
        return self
    
    def add_progress_callback(self, callback: Callable[[WorkflowProgress], None]) -> 'WorkflowEngine':
        """添加进度回调"""
        self.progress_callbacks.append(callback)
        return self
    
    def add_error_callback(self, callback: Callable[[ProcessingError], None]) -> 'WorkflowEngine':
        """添加错误回调"""
        self.error_callbacks.append(callback)
        return self
    
    def _notify_progress(self):
        """通知进度更新"""
        for callback in self.progress_callbacks:
            try:
                callback(self.progress)
            except Exception as e:
                self.logger.error(f"进度回调执行失败: {e}")
    
    def _notify_error(self, error: ProcessingError):
        """通知错误"""
        for callback in self.error_callbacks:
            try:
                callback(error)
            except Exception as e:
                self.logger.error(f"错误回调执行失败: {e}")
    
    async def run(self, config: WorkflowConfig) -> BaseResult:
        """
        运行工作流程
        
        Args:
            config: 工作流程配置
            
        Returns:
            BaseResult: 执行结果
        """
        try:
            self.current_workflow = config
            self._running = True
            self._cancelled = False
            
            # 初始化进度
            self.progress = WorkflowProgress(
                total_tasks=len(self.tasks),
                completed_tasks=0,
                status=WorkflowStatus.RUNNING
            )
            
            self.logger.info(f"开始执行工作流程: {config.session_id}")
            
            # 创建处理上下文
            self.context = ProcessingContext(
                session_id=config.session_id,
                input_files=config.input_files,
                config={
                    "style_config": config.style_config,
                    "processing_config": config.processing_config
                },
                temp_directory=config.temp_directory,
                output_directory=config.output_directory
            )
            
            # 确保目录存在
            os.makedirs(config.output_directory, exist_ok=True)
            os.makedirs(config.temp_directory, exist_ok=True)
            
            # 执行任务
            result = await self._execute_tasks()
            
            # 更新最终状态
            if result.success:
                self.progress.status = WorkflowStatus.COMPLETED
                self.progress.overall_progress = 100.0
                self.progress.message = "工作流程完成"
            else:
                self.progress.status = WorkflowStatus.FAILED
                self.progress.message = f"工作流程失败: {result.message}"
            
            self._notify_progress()
            
            # 清理临时文件
            if config.cleanup_temp and os.path.exists(config.temp_directory):
                try:
                    shutil.rmtree(config.temp_directory)
                    self.logger.info(f"清理临时目录: {config.temp_directory}")
                except Exception as e:
                    self.logger.warning(f"清理临时目录失败: {e}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"工作流程执行异常: {e}")
            self.progress.status = WorkflowStatus.FAILED
            self.progress.message = f"工作流程异常: {str(e)}"
            self._notify_progress()
            
            return BaseResult(
                success=False,
                message=f"工作流程执行异常: {str(e)}",
                error_code="WORKFLOW_EXCEPTION"
            )
        finally:
            self._running = False
    
    async def _execute_tasks(self) -> BaseResult:
        """执行所有任务"""
        completed_tasks = set()
        failed_tasks = set()
        
        while len(completed_tasks) + len(failed_tasks) < len(self.tasks):
            if self._cancelled:
                return BaseResult(
                    success=False,
                    message="工作流程被取消",
                    error_code="WORKFLOW_CANCELLED"
                )
            
            # 查找可以运行的任务
            runnable_tasks = []
            for task_id in self.task_order:
                task = self.tasks[task_id]
                if task.can_run(completed_tasks):
                    runnable_tasks.append(task)
            
            if not runnable_tasks:
                # 检查是否有任务失败导致无法继续
                if failed_tasks:
                    return BaseResult(
                        success=False,
                        message=f"工作流程失败，{len(failed_tasks)} 个任务执行失败",
                        error_code="WORKFLOW_TASK_FAILED"
                    )
                
                # 检查是否存在循环依赖
                waiting_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.WAITING]
                if waiting_tasks:
                    return BaseResult(
                        success=False,
                        message="检测到循环依赖或无法满足的依赖关系",
                        error_code="WORKFLOW_DEPENDENCY_ERROR"
                    )
                
                break
            
            # 限制并发任务数
            max_concurrent = self.current_workflow.max_concurrent_tasks
            if len(runnable_tasks) > max_concurrent:
                runnable_tasks = runnable_tasks[:max_concurrent]
            
            # 并发执行任务
            tasks_to_run = [self._execute_task(task) for task in runnable_tasks]
            results = await asyncio.gather(*tasks_to_run, return_exceptions=True)
            
            # 处理结果
            for task, result in zip(runnable_tasks, results):
                if isinstance(result, Exception):
                    self.logger.error(f"任务 {task.id} 执行异常: {result}")
                    task.status = TaskStatus.FAILED
                    task.error = result
                    failed_tasks.add(task.id)
                elif result and result.success:
                    task.status = TaskStatus.COMPLETED
                    completed_tasks.add(task.id)
                else:
                    task.status = TaskStatus.FAILED
                    failed_tasks.add(task.id)
                
                # 更新进度
                self.progress.completed_tasks = len(completed_tasks)
                self.progress.update_overall_progress()
                self._notify_progress()
        
        # 检查最终结果
        if failed_tasks:
            return BaseResult(
                success=False,
                message=f"工作流程部分失败，{len(failed_tasks)} 个任务失败",
                error_code="WORKFLOW_PARTIAL_FAILURE",
                metadata={
                    "completed_tasks": len(completed_tasks),
                    "failed_tasks": len(failed_tasks),
                    "failed_task_ids": list(failed_tasks)
                }
            )
        
        return BaseResult(
            success=True,
            message=f"工作流程成功完成，共执行 {len(completed_tasks)} 个任务",
            metadata={
                "completed_tasks": len(completed_tasks),
                "total_execution_time": sum(task.duration for task in self.tasks.values())
            }
        )
    
    async def _execute_task(self, task: WorkflowTask) -> BaseResult:
        """执行单个任务"""
        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()
        
        # 更新当前任务进度
        self.progress.current_task = task.name
        self.progress.current_task_progress = 0.0
        self._notify_progress()
        
        self.logger.info(f"开始执行任务: {task.id} - {task.name}")
        
        # 设置进度回调
        def progress_callback(processor_name: str, progress: float, message: str):
            task.progress = progress
            self.progress.current_task_progress = progress
            self.progress.message = f"{task.name}: {message}"
            self.progress.update_overall_progress()
            self._notify_progress()
        
        task.processor.progress_callback = progress_callback
        
        try:
            # 执行任务
            result = await task.processor.process(self.context)
            task.result = result
            task.end_time = datetime.now()
            
            if result.success:
                self.logger.info(f"任务完成: {task.id} - {task.name} ({task.duration:.2f}s)")
            else:
                self.logger.error(f"任务失败: {task.id} - {task.name}: {result.message}")
                
                # 尝试重试
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    self.logger.info(f"重试任务: {task.id} (第 {task.retry_count} 次)")
                    await asyncio.sleep(2 ** task.retry_count)  # 指数退避
                    return await self._execute_task(task)
            
            return result
            
        except Exception as e:
            task.error = e
            task.end_time = datetime.now()
            self.logger.error(f"任务异常: {task.id} - {task.name}: {e}")
            
            # 通知错误
            error = ProcessingError(
                code="TASK_EXECUTION_ERROR",
                message=f"任务 {task.name} 执行失败: {str(e)}",
                severity=ErrorSeverity.ERROR,
                context={"task_id": task.id, "task_name": task.name}
            )
            self._notify_error(error)
            
            return BaseResult(
                success=False,
                message=f"任务执行异常: {str(e)}",
                error_code="TASK_EXECUTION_ERROR"
            )
    
    def cancel(self) -> bool:
        """取消工作流程"""
        if self._running:
            self._cancelled = True
            self.progress.status = WorkflowStatus.CANCELLED
            self.progress.message = "工作流程已取消"
            self._notify_progress()
            
            # 取消所有正在运行的任务
            for task in self.tasks.values():
                if task.status == TaskStatus.RUNNING:
                    task.processor.cancel()
            
            self.logger.info("工作流程已取消")
            return True
        return False
    
    def pause(self) -> bool:
        """暂停工作流程"""
        if self._running and not self._cancelled:
            self.progress.status = WorkflowStatus.PAUSED
            self.progress.message = "工作流程已暂停"
            self._notify_progress()
            self.logger.info("工作流程已暂停")
            return True
        return False
    
    def resume(self) -> bool:
        """恢复工作流程"""
        if self.progress.status == WorkflowStatus.PAUSED:
            self.progress.status = WorkflowStatus.RUNNING
            self.progress.message = "工作流程已恢复"
            self._notify_progress()
            self.logger.info("工作流程已恢复")
            return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[WorkflowTask]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_progress(self) -> WorkflowProgress:
        """获取当前进度"""
        return self.progress
    
    def get_summary(self) -> Dict[str, Any]:
        """获取工作流程摘要"""
        completed = sum(1 for task in self.tasks.values() if task.status == TaskStatus.COMPLETED)
        failed = sum(1 for task in self.tasks.values() if task.status == TaskStatus.FAILED)
        running = sum(1 for task in self.tasks.values() if task.status == TaskStatus.RUNNING)
        waiting = sum(1 for task in self.tasks.values() if task.status == TaskStatus.WAITING)
        
        return {
            "session_id": self.current_workflow.session_id if self.current_workflow else None,
            "status": self.progress.status.value,
            "overall_progress": self.progress.overall_progress,
            "total_tasks": len(self.tasks),
            "completed_tasks": completed,
            "failed_tasks": failed,
            "running_tasks": running,
            "waiting_tasks": waiting,
            "current_task": self.progress.current_task,
            "message": self.progress.message,
            "total_execution_time": sum(task.duration for task in self.tasks.values() if task.duration > 0)
        }
