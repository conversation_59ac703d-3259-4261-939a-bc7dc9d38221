"""
Workflow Manager - 工作流程管理器

管理多个并发工作流程，提供统一的接口和状态管理
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass
import threading

from .workflow_engine import WorkflowProgress, WorkflowStatus
from .video_editing_workflow import VideoEditingWorkflow
from config.config_manager import ConfigManager
from ..common.models import BaseResult, ProcessingError
from ..common.types import SessionIdType


@dataclass
class WorkflowSession:
    """工作流程会话"""
    session_id: SessionIdType
    workflow: VideoEditingWorkflow
    status: WorkflowStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: Optional[WorkflowProgress] = None
    result: Optional[BaseResult] = None
    error: Optional[Exception] = None
    
    @property
    def duration(self) -> float:
        """获取执行时长（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        elif self.started_at:
            return (datetime.now() - self.started_at).total_seconds()
        return 0.0
    
    @property
    def is_active(self) -> bool:
        """是否为活跃状态"""
        return self.status in [WorkflowStatus.RUNNING, WorkflowStatus.PAUSED]
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED]


class WorkflowManager:
    """工作流程管理器"""
    
    def __init__(self, config_manager: ConfigManager, max_concurrent_workflows: int = 3):
        self.config_manager = config_manager
        self.max_concurrent_workflows = max_concurrent_workflows
        self.logger = logging.getLogger(__name__)
        
        # 会话管理
        self.sessions: Dict[SessionIdType, WorkflowSession] = {}
        self.active_sessions: set = set()
        self._lock = threading.Lock()
        
        # 全局回调
        self.global_progress_callbacks: List[Callable[[SessionIdType, WorkflowProgress], None]] = []
        self.global_error_callbacks: List[Callable[[SessionIdType, ProcessingError], None]] = []
        self.session_complete_callbacks: List[Callable[[SessionIdType, BaseResult], None]] = []
        
        self.logger.info(f"工作流程管理器初始化完成，最大并发数: {max_concurrent_workflows}")
    
    def add_global_progress_callback(self, callback: Callable[[SessionIdType, WorkflowProgress], None]):
        """添加全局进度回调"""
        self.global_progress_callbacks.append(callback)
    
    def add_global_error_callback(self, callback: Callable[[SessionIdType, ProcessingError], None]):
        """添加全局错误回调"""
        self.global_error_callbacks.append(callback)
    
    def add_session_complete_callback(self, callback: Callable[[SessionIdType, BaseResult], None]):
        """添加会话完成回调"""
        self.session_complete_callbacks.append(callback)
    
    async def start_video_editing(self,
                                 session_id: SessionIdType,
                                 video_file: str,
                                 subtitle_file: str,
                                 style_name: str,
                                 output_directory: str) -> BaseResult:
        """
        开始视频编辑工作流程
        
        Args:
            session_id: 会话ID
            video_file: 视频文件路径
            subtitle_file: 字幕文件路径
            style_name: 改写风格名称
            output_directory: 输出目录
            
        Returns:
            BaseResult: 启动结果
        """
        try:
            with self._lock:
                # 检查会话是否已存在
                if session_id in self.sessions:
                    return BaseResult(
                        success=False,
                        message=f"会话 {session_id} 已存在",
                        error_code="SESSION_ALREADY_EXISTS"
                    )
                
                # 检查并发限制
                if len(self.active_sessions) >= self.max_concurrent_workflows:
                    return BaseResult(
                        success=False,
                        message=f"已达到最大并发数限制 ({self.max_concurrent_workflows})",
                        error_code="MAX_CONCURRENT_LIMIT_REACHED"
                    )
                
                # 创建工作流程
                workflow = VideoEditingWorkflow(self.config_manager)
                
                # 创建会话
                session = WorkflowSession(
                    session_id=session_id,
                    workflow=workflow,
                    status=WorkflowStatus.PENDING,
                    created_at=datetime.now()
                )
                
                self.sessions[session_id] = session
                self.active_sessions.add(session_id)
            
            self.logger.info(f"开始视频编辑会话: {session_id}")
            
            # 设置会话特定的回调
            def progress_callback(progress: WorkflowProgress):
                session.progress = progress
                session.status = progress.status
                
                # 通知全局回调
                for callback in self.global_progress_callbacks:
                    try:
                        callback(session_id, progress)
                    except Exception as e:
                        self.logger.error(f"全局进度回调执行失败: {e}")
            
            def error_callback(error: ProcessingError):
                session.error = Exception(error.message)
                
                # 通知全局回调
                for callback in self.global_error_callbacks:
                    try:
                        callback(session_id, error)
                    except Exception as e:
                        self.logger.error(f"全局错误回调执行失败: {e}")
            
            # 在后台执行工作流程
            asyncio.create_task(self._execute_workflow(
                session_id, video_file, subtitle_file, style_name, output_directory,
                progress_callback, error_callback
            ))
            
            return BaseResult(
                success=True,
                message=f"视频编辑会话 {session_id} 已启动",
                metadata={"session_id": session_id}
            )
            
        except Exception as e:
            self.logger.error(f"启动视频编辑会话失败: {e}")
            return BaseResult(
                success=False,
                message=f"启动会话失败: {str(e)}",
                error_code="START_SESSION_ERROR"
            )
    
    async def _execute_workflow(self,
                               session_id: SessionIdType,
                               video_file: str,
                               subtitle_file: str,
                               style_name: str,
                               output_directory: str,
                               progress_callback: Callable,
                               error_callback: Callable):
        """执行工作流程（内部方法）"""
        session = self.sessions[session_id]
        
        try:
            session.started_at = datetime.now()
            session.status = WorkflowStatus.RUNNING
            
            # 执行工作流程
            result = await session.workflow.process_video(
                video_file=video_file,
                subtitle_file=subtitle_file,
                style_name=style_name,
                output_directory=output_directory,
                progress_callback=progress_callback,
                error_callback=error_callback
            )
            
            # 更新会话状态
            session.completed_at = datetime.now()
            session.result = result
            
            if result.success:
                session.status = WorkflowStatus.COMPLETED
                self.logger.info(f"会话 {session_id} 完成，耗时: {session.duration:.2f}秒")
            else:
                session.status = WorkflowStatus.FAILED
                self.logger.error(f"会话 {session_id} 失败: {result.message}")
            
            # 通知会话完成回调
            for callback in self.session_complete_callbacks:
                try:
                    callback(session_id, result)
                except Exception as e:
                    self.logger.error(f"会话完成回调执行失败: {e}")
            
        except Exception as e:
            session.completed_at = datetime.now()
            session.status = WorkflowStatus.FAILED
            session.error = e
            
            self.logger.error(f"会话 {session_id} 执行异常: {e}")
            
            # 创建错误结果
            error_result = BaseResult(
                success=False,
                message=f"会话执行异常: {str(e)}",
                error_code="SESSION_EXECUTION_ERROR"
            )
            session.result = error_result
            
            # 通知会话完成回调
            for callback in self.session_complete_callbacks:
                try:
                    callback(session_id, error_result)
                except Exception as e:
                    self.logger.error(f"会话完成回调执行失败: {e}")
        
        finally:
            # 从活跃会话中移除
            with self._lock:
                self.active_sessions.discard(session_id)
    
    def cancel_session(self, session_id: SessionIdType) -> BaseResult:
        """
        取消会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            BaseResult: 取消结果
        """
        try:
            session = self.sessions.get(session_id)
            if not session:
                return BaseResult(
                    success=False,
                    message=f"会话 {session_id} 不存在",
                    error_code="SESSION_NOT_FOUND"
                )
            
            if not session.is_active:
                return BaseResult(
                    success=False,
                    message=f"会话 {session_id} 不是活跃状态",
                    error_code="SESSION_NOT_ACTIVE"
                )
            
            # 取消工作流程
            success = session.workflow.cancel()
            
            if success:
                session.status = WorkflowStatus.CANCELLED
                session.completed_at = datetime.now()
                
                with self._lock:
                    self.active_sessions.discard(session_id)
                
                self.logger.info(f"会话 {session_id} 已取消")
                
                return BaseResult(
                    success=True,
                    message=f"会话 {session_id} 已取消"
                )
            else:
                return BaseResult(
                    success=False,
                    message=f"取消会话 {session_id} 失败",
                    error_code="CANCEL_SESSION_FAILED"
                )
                
        except Exception as e:
            self.logger.error(f"取消会话失败: {e}")
            return BaseResult(
                success=False,
                message=f"取消会话失败: {str(e)}",
                error_code="CANCEL_SESSION_ERROR"
            )
    
    def get_session_status(self, session_id: SessionIdType) -> Optional[WorkflowSession]:
        """获取会话状态"""
        return self.sessions.get(session_id)
    
    def get_session_progress(self, session_id: SessionIdType) -> Optional[WorkflowProgress]:
        """获取会话进度"""
        session = self.sessions.get(session_id)
        return session.progress if session else None
    
    def list_sessions(self, status_filter: Optional[WorkflowStatus] = None) -> List[WorkflowSession]:
        """
        列出会话
        
        Args:
            status_filter: 状态过滤器
            
        Returns:
            List[WorkflowSession]: 会话列表
        """
        sessions = list(self.sessions.values())
        
        if status_filter:
            sessions = [s for s in sessions if s.status == status_filter]
        
        # 按创建时间排序
        sessions.sort(key=lambda s: s.created_at, reverse=True)
        
        return sessions
    
    def get_active_sessions(self) -> List[WorkflowSession]:
        """获取活跃会话列表"""
        return [self.sessions[sid] for sid in self.active_sessions if sid in self.sessions]
    
    def cleanup_completed_sessions(self, keep_recent: int = 10) -> int:
        """
        清理已完成的会话
        
        Args:
            keep_recent: 保留最近的会话数量
            
        Returns:
            int: 清理的会话数量
        """
        completed_sessions = [
            s for s in self.sessions.values() 
            if s.is_completed
        ]
        
        # 按完成时间排序，保留最近的
        completed_sessions.sort(key=lambda s: s.completed_at or s.created_at, reverse=True)
        
        sessions_to_remove = completed_sessions[keep_recent:]
        removed_count = 0
        
        for session in sessions_to_remove:
            try:
                del self.sessions[session.session_id]
                removed_count += 1
                self.logger.debug(f"清理会话: {session.session_id}")
            except KeyError:
                pass
        
        if removed_count > 0:
            self.logger.info(f"清理了 {removed_count} 个已完成的会话")
        
        return removed_count
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_sessions = len(self.sessions)
        active_sessions = len(self.active_sessions)
        completed_sessions = len([s for s in self.sessions.values() if s.status == WorkflowStatus.COMPLETED])
        failed_sessions = len([s for s in self.sessions.values() if s.status == WorkflowStatus.FAILED])
        
        # 计算平均执行时间
        completed_with_duration = [s for s in self.sessions.values() 
                                 if s.status == WorkflowStatus.COMPLETED and s.duration > 0]
        avg_duration = sum(s.duration for s in completed_with_duration) / len(completed_with_duration) if completed_with_duration else 0
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "completed_sessions": completed_sessions,
            "failed_sessions": failed_sessions,
            "success_rate": completed_sessions / total_sessions * 100 if total_sessions > 0 else 0,
            "average_duration": avg_duration,
            "max_concurrent_workflows": self.max_concurrent_workflows,
            "current_load": active_sessions / self.max_concurrent_workflows * 100
        }
