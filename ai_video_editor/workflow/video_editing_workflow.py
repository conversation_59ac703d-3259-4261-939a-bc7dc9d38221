"""
Video Editing Workflow - 视频编辑工作流程

预定义的完整视频编辑工作流程，整合所有处理步骤
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, Callable
import tempfile

from .workflow_engine import WorkflowEngine, WorkflowConfig, WorkflowProgress
from ..core.subtitle_processor import SubtitleProcessor
from ..core.video_processor import VideoProcessor
from ..services.deepseek_client import DeepSeekClient
from ..services.aliyun_tts_client import AliyunTTSClient, TTSConfig
from config.config_manager import ConfigManager
from ..common.models import BaseResult, FileInfo, FileType, ProcessingError
from ..common.types import SessionIdType


class VideoEditingWorkflow:
    """视频编辑工作流程"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.workflow_engine = WorkflowEngine()
        self._setup_workflow()
    
    def _setup_workflow(self):
        """设置工作流程"""
        # 获取配置
        api_config = self.config_manager.get_api_config()
        app_settings = self.config_manager.get_app_settings()
        
        # 创建处理器实例
        subtitle_processor = SubtitleProcessor()
        
        deepseek_client = DeepSeekClient(api_config.get("deepseek", {}))
        
        video_processor = VideoProcessor({
            "ffmpeg_path": app_settings.get("processing", {}).get("ffmpeg_path", "ffmpeg"),
            "ffprobe_path": app_settings.get("processing", {}).get("ffprobe_path", "ffprobe")
        })
        
        tts_config = TTSConfig(
            voice=api_config.get("aliyun_tts", {}).get("voice", "longxiaochun_v2"),
            format=api_config.get("aliyun_tts", {}).get("format", "wav"),
            sample_rate=api_config.get("aliyun_tts", {}).get("sample_rate", 24000),
            volume=api_config.get("aliyun_tts", {}).get("volume", 50)
        )
        tts_client = AliyunTTSClient(tts_config)
        
        # 添加任务到工作流程
        self.workflow_engine.add_task(
            "parse_subtitle",
            "解析字幕文件",
            subtitle_processor
        ).add_task(
            "get_video_info",
            "获取视频信息",
            video_processor
        ).add_task(
            "rewrite_subtitle",
            "AI字幕改写",
            SubtitleRewriter(deepseek_client, self.config_manager),
            dependencies=["parse_subtitle"]
        ).add_task(
            "generate_audio",
            "TTS语音合成",
            TTSAudioGenerator(tts_client),
            dependencies=["rewrite_subtitle"]
        ).add_task(
            "remove_original_audio",
            "移除原始音频",
            VideoAudioRemover(video_processor),
            dependencies=["get_video_info"]
        ).add_task(
            "adjust_video_speed",
            "调整视频速度",
            VideoSpeedAdjuster(video_processor),
            dependencies=["remove_original_audio", "generate_audio"]
        ).add_task(
            "merge_final_video",
            "合并最终视频",
            VideoAudioMerger(video_processor),
            dependencies=["adjust_video_speed"]
        )
    
    async def process_video(self, 
                          video_file: str,
                          subtitle_file: str,
                          style_name: str,
                          output_directory: str,
                          progress_callback: Optional[Callable[[WorkflowProgress], None]] = None,
                          error_callback: Optional[Callable[[ProcessingError], None]] = None) -> BaseResult:
        """
        处理视频的完整流程
        
        Args:
            video_file: 视频文件路径
            subtitle_file: 字幕文件路径
            style_name: 改写风格名称
            output_directory: 输出目录
            progress_callback: 进度回调函数
            error_callback: 错误回调函数
            
        Returns:
            BaseResult: 处理结果
        """
        try:
            # 生成会话ID
            import uuid
            session_id = str(uuid.uuid4())
            
            self.logger.info(f"开始视频编辑流程: {session_id}")
            
            # 验证输入文件
            if not os.path.exists(video_file):
                return BaseResult(
                    success=False,
                    message=f"视频文件不存在: {video_file}",
                    error_code="VIDEO_FILE_NOT_FOUND"
                )
            
            if not os.path.exists(subtitle_file):
                return BaseResult(
                    success=False,
                    message=f"字幕文件不存在: {subtitle_file}",
                    error_code="SUBTITLE_FILE_NOT_FOUND"
                )
            
            # 获取改写风格配置
            style_config = self.config_manager.get_prompt_style(style_name)
            if not style_config:
                return BaseResult(
                    success=False,
                    message=f"未找到改写风格: {style_name}",
                    error_code="STYLE_NOT_FOUND"
                )
            
            # 准备输入文件信息
            from datetime import datetime

            input_files = {
                FileType.VIDEO: FileInfo(
                    path=video_file,
                    name=os.path.basename(video_file),
                    size=os.path.getsize(video_file),
                    type=FileType.VIDEO,
                    format=os.path.splitext(video_file)[1][1:],  # 移除点号
                    created_at=datetime.now()
                ),
                FileType.SUBTITLE: FileInfo(
                    path=subtitle_file,
                    name=os.path.basename(subtitle_file),
                    size=os.path.getsize(subtitle_file),
                    type=FileType.SUBTITLE,
                    format=os.path.splitext(subtitle_file)[1][1:],  # 移除点号
                    created_at=datetime.now()
                )
            }
            
            # 创建工作流程配置
            workflow_config = WorkflowConfig(
                session_id=session_id,
                input_files=input_files,
                output_directory=output_directory,
                style_config=style_config,
                processing_config=self.config_manager.get_app_settings("processing")
            )
            
            # 添加回调
            if progress_callback:
                self.workflow_engine.add_progress_callback(progress_callback)
            
            if error_callback:
                self.workflow_engine.add_error_callback(error_callback)
            
            # 执行工作流程
            result = await self.workflow_engine.run(workflow_config)
            
            if result.success:
                self.logger.info(f"视频编辑流程完成: {session_id}")
            else:
                self.logger.error(f"视频编辑流程失败: {session_id} - {result.message}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"视频编辑流程异常: {e}")
            return BaseResult(
                success=False,
                message=f"视频编辑流程异常: {str(e)}",
                error_code="WORKFLOW_EXCEPTION"
            )
    
    def get_progress(self) -> WorkflowProgress:
        """获取当前进度"""
        return self.workflow_engine.get_progress()
    
    def cancel(self) -> bool:
        """取消处理"""
        return self.workflow_engine.cancel()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        return self.workflow_engine.get_summary()


# 专用处理器包装类
class SubtitleRewriter:
    """字幕改写器"""

    def __init__(self, deepseek_client: DeepSeekClient, config_manager: ConfigManager):
        self.deepseek_client = deepseek_client
        self.config_manager = config_manager
        self.progress_callback = None

    async def process(self, context) -> BaseResult:
        """执行字幕改写"""
        try:
            # 获取原始字幕
            original_subtitles = context.shared_data.get("original_subtitles")
            if not original_subtitles:
                return BaseResult(
                    success=False,
                    message="未找到原始字幕数据",
                    error_code="ORIGINAL_SUBTITLES_NOT_FOUND"
                )

            # 获取改写风格配置
            style_name = context.config.get("style_name", "科普解说风格")
            style_config = self.config_manager.get_prompt_style(style_name)

            if not style_config:
                return BaseResult(
                    success=False,
                    message=f"未找到改写风格: {style_name}",
                    error_code="STYLE_CONFIG_NOT_FOUND"
                )

            if self.progress_callback:
                self.progress_callback("SubtitleRewriter", 10.0, "开始字幕改写...")

            # 执行字幕改写
            result, rewritten_subtitles, mappings = await self.deepseek_client.rewrite_subtitles(
                original_subtitles, style_config
            )

            if result.success and rewritten_subtitles:
                # 保存结果到上下文
                context.shared_data["rewritten_subtitles"] = rewritten_subtitles
                context.shared_data["subtitle_mappings"] = mappings

                if self.progress_callback:
                    self.progress_callback("SubtitleRewriter", 100.0, "字幕改写完成")

                return BaseResult(
                    success=True,
                    message=f"成功改写 {len(original_subtitles.items)} -> {len(rewritten_subtitles.items)} 条字幕"
                )
            else:
                return result

        except Exception as e:
            return BaseResult(
                success=False,
                message=f"字幕改写失败: {str(e)}",
                error_code="SUBTITLE_REWRITE_ERROR"
            )

    def cancel(self) -> bool:
        return True


class TTSAudioGenerator:
    """TTS音频生成器"""

    def __init__(self, tts_client: AliyunTTSClient):
        self.tts_client = tts_client
        self.progress_callback = None

    async def process(self, context) -> BaseResult:
        """生成TTS音频并计算时间码"""
        try:
            # 获取改写后的字幕
            rewritten_subtitles = context.shared_data.get("rewritten_subtitles")
            if not rewritten_subtitles:
                return BaseResult(
                    success=False,
                    message="未找到改写后的字幕",
                    error_code="REWRITTEN_SUBTITLES_NOT_FOUND"
                )

            if self.progress_callback:
                self.progress_callback("TTSAudioGenerator", 5.0, "准备分段TTS合成...")

            # 为每个字幕段生成音频并计算时间码
            audio_segments = []
            total_duration = 0.0

            for i, subtitle_item in enumerate(rewritten_subtitles.items):
                if self.progress_callback:
                    progress = 10.0 + (i / len(rewritten_subtitles.items)) * 70.0
                    self.progress_callback("TTSAudioGenerator", progress,
                                         f"合成第 {i+1}/{len(rewritten_subtitles.items)} 段音频...")

                # 为每个字幕段生成单独的音频文件
                segment_output_path = os.path.join(
                    context.temp_directory,
                    f"tts_segment_{context.session_id}_{i+1:03d}.wav"
                )

                # 执行TTS合成
                result = await self.tts_client.synthesize_text(
                    text=subtitle_item.text,
                    output_file=segment_output_path
                )

                if result.success:
                    # 计算时间码
                    start_time = total_duration
                    end_time = total_duration + result.duration

                    # 更新字幕项的时间码
                    subtitle_item.start_time = self._seconds_to_timecode(start_time)
                    subtitle_item.end_time = self._seconds_to_timecode(end_time)

                    # 保存音频段信息
                    audio_segments.append({
                        "file": segment_output_path,
                        "duration": result.duration,
                        "start_time": start_time,
                        "end_time": end_time,
                        "subtitle_index": subtitle_item.index
                    })

                    total_duration += result.duration
                else:
                    return BaseResult(
                        success=False,
                        message=f"第 {i+1} 段音频合成失败: {result.message}",
                        error_code="TTS_SEGMENT_ERROR"
                    )

            # 合并所有音频段为完整音频
            if self.progress_callback:
                self.progress_callback("TTSAudioGenerator", 85.0, "合并音频段...")

            final_audio_path = os.path.join(
                context.temp_directory,
                f"tts_audio_{context.session_id}.wav"
            )

            merge_result = await self._merge_audio_segments(audio_segments, final_audio_path)
            if not merge_result.success:
                return merge_result

            # 保存结果到上下文
            context.shared_data["tts_audio"] = final_audio_path
            context.shared_data["tts_duration"] = total_duration
            context.shared_data["audio_segments"] = audio_segments
            context.shared_data["rewritten_subtitles_with_timecode"] = rewritten_subtitles

            if self.progress_callback:
                self.progress_callback("TTSAudioGenerator", 100.0,
                                     f"TTS音频生成完成，总时长: {total_duration:.2f}秒")

            return BaseResult(
                success=True,
                message=f"成功生成 {len(audio_segments)} 段音频，总时长: {total_duration:.2f}秒"
            )

        except Exception as e:
            return BaseResult(
                success=False,
                message=f"TTS音频生成失败: {str(e)}",
                error_code="TTS_GENERATION_ERROR"
            )

    def _seconds_to_timecode(self, seconds: float) -> str:
        """将秒数转换为SRT时间码格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    async def _merge_audio_segments(self, segments: list, output_path: str) -> BaseResult:
        """合并音频段为完整音频文件"""
        try:
            import subprocess

            # 创建临时文件列表
            temp_list_file = output_path.replace('.wav', '_list.txt')

            with open(temp_list_file, 'w', encoding='utf-8') as f:
                for segment in segments:
                    f.write(f"file '{segment['file']}'\n")

            # 使用FFmpeg合并音频
            cmd = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_file,
                '-c', 'copy',
                output_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # 清理临时文件
            try:
                os.remove(temp_list_file)
            except:
                pass

            if process.returncode == 0:
                return BaseResult(success=True, message="音频合并成功")
            else:
                return BaseResult(
                    success=False,
                    message=f"音频合并失败: {stderr.decode('utf-8', errors='ignore')}",
                    error_code="AUDIO_MERGE_ERROR"
                )

        except Exception as e:
            return BaseResult(
                success=False,
                message=f"音频合并异常: {str(e)}",
                error_code="AUDIO_MERGE_EXCEPTION"
            )

    def cancel(self) -> bool:
        return True


class VideoAudioRemover:
    """视频音频移除器"""
    
    def __init__(self, video_processor: VideoProcessor):
        self.video_processor = video_processor
        self.progress_callback = None
    
    async def process(self, context) -> BaseResult:
        """移除视频音频"""
        try:
            video_file = context.input_files[FileType.VIDEO]
            
            # 生成输出文件路径
            output_path = os.path.join(
                context.temp_directory,
                f"video_no_audio_{context.session_id}.mp4"
            )
            
            if self.progress_callback:
                self.progress_callback("VideoAudioRemover", 20.0, "开始移除音频...")
            
            # 移除音频
            result = self.video_processor.remove_audio(video_file.path, output_path)
            
            if result.success:
                # 保存结果到上下文
                context.shared_data["video_no_audio"] = output_path
                
                if self.progress_callback:
                    self.progress_callback("VideoAudioRemover", 100.0, "音频移除完成")
            
            return result
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"移除音频失败: {str(e)}",
                error_code="REMOVE_AUDIO_ERROR"
            )
    
    def cancel(self) -> bool:
        return True


class VideoSpeedAdjuster:
    """视频速度调整器"""
    
    def __init__(self, video_processor: VideoProcessor):
        self.video_processor = video_processor
        self.progress_callback = None
    
    async def process(self, context) -> BaseResult:
        """调整视频速度以匹配音频时长"""
        try:
            video_no_audio = context.shared_data.get("video_no_audio")
            if not video_no_audio:
                return BaseResult(
                    success=False,
                    message="未找到无音频视频文件",
                    error_code="VIDEO_NO_AUDIO_NOT_FOUND"
                )
            
            # 获取视频信息
            video_info_result, video_info = self.video_processor.get_video_info(video_no_audio)
            if not video_info_result.success:
                return video_info_result
            
            # 获取新音频时长（从TTS结果中获取）
            # 这里需要根据实际的TTS结果来计算
            # 暂时使用原视频时长，实际应用中需要根据TTS音频时长调整
            target_duration = video_info.duration
            speed_factor = 1.0  # 默认不调整速度
            
            if self.progress_callback:
                self.progress_callback("VideoSpeedAdjuster", 30.0, f"调整视频速度: {speed_factor}x")
            
            # 生成输出文件路径
            output_path = os.path.join(
                context.temp_directory,
                f"video_adjusted_{context.session_id}.mp4"
            )
            
            # 调整速度
            result = self.video_processor.adjust_speed(video_no_audio, output_path, speed_factor)
            
            if result.success:
                context.shared_data["video_adjusted"] = output_path
                
                if self.progress_callback:
                    self.progress_callback("VideoSpeedAdjuster", 100.0, "视频速度调整完成")
            
            return result
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"调整视频速度失败: {str(e)}",
                error_code="ADJUST_SPEED_ERROR"
            )
    
    def cancel(self) -> bool:
        return True


class VideoAudioMerger:
    """视频音频合并器"""
    
    def __init__(self, video_processor: VideoProcessor):
        self.video_processor = video_processor
        self.progress_callback = None
    
    async def process(self, context) -> BaseResult:
        """合并视频和音频"""
        try:
            video_adjusted = context.shared_data.get("video_adjusted")
            if not video_adjusted:
                return BaseResult(
                    success=False,
                    message="未找到调整后的视频文件",
                    error_code="VIDEO_ADJUSTED_NOT_FOUND"
                )
            
            # 获取TTS生成的音频文件
            # 这里需要从TTS处理结果中获取音频文件路径
            # 暂时使用占位符
            audio_file = context.shared_data.get("tts_audio")
            if not audio_file:
                return BaseResult(
                    success=False,
                    message="未找到TTS音频文件",
                    error_code="TTS_AUDIO_NOT_FOUND"
                )
            
            if self.progress_callback:
                self.progress_callback("VideoAudioMerger", 40.0, "开始合并音视频...")
            
            # 生成最终输出文件路径
            output_filename = f"final_video_{context.session_id}.mp4"
            output_path = os.path.join(context.output_directory, output_filename)
            
            # 合并音视频
            result = self.video_processor.merge_audio_video(
                video_adjusted, audio_file, output_path
            )
            
            if result.success:
                context.shared_data["final_video"] = output_path
                
                if self.progress_callback:
                    self.progress_callback("VideoAudioMerger", 100.0, "音视频合并完成")
            
            return result
            
        except Exception as e:
            return BaseResult(
                success=False,
                message=f"合并音视频失败: {str(e)}",
                error_code="MERGE_VIDEO_AUDIO_ERROR"
            )
    
    def cancel(self) -> bool:
        return True
