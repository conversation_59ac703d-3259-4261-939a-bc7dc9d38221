#!/usr/bin/env python3
"""
AI Video Editor 主程序入口

启动AI智能视频剪辑软件的主界面
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

from ai_video_editor.ui.main_window import MainWindow
from config.config_manager import ConfigManager
from ai_video_editor.utils.logger import setup_logging


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    app.setApplicationName("AI Video Editor")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Video Editor Team")
    
    # 设置应用程序图标
    icon_path = project_root / "ai_video_editor" / "ui" / "resources" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # 设置高DPI支持 (PyQt6中这些属性已被移除，自动启用)
    # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    return app


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("启动AI Video Editor...")

        # 创建应用程序
        app = setup_application()

        # 创建主窗口（新的MainWindow不需要config_manager参数）
        main_window = MainWindow()
        main_window.show()

        logger.info("应用程序启动成功")

        # 运行应用程序
        sys.exit(app.exec())

    except Exception as e:
        logging.error(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
