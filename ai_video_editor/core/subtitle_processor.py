"""
Subtitle Processor - 字幕处理器

实现SRT字幕文件的解析、验证、导出功能
支持时间轴处理和格式转换
"""

import os
import re
import logging
from typing import List, Optional, Tuple, Dict, Any
from pathlib import Path

from ..common.models import (
    BaseResult, SubtitleCollection, SubtitleItem, TimeCode,
    SubtitleFormat, SubtitleStatus, ProcessingContext
)
from ..common.interfaces import BaseProcessor
from ..common.types import FilePathType


class SubtitleProcessor(BaseProcessor):
    """字幕处理器实现类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("SubtitleProcessor", config)
        self.logger = logging.getLogger(__name__)
        
        # 支持的字幕格式
        self.supported_formats = [".srt", ".vtt"]
        
        # SRT格式正则表达式
        self.srt_pattern = re.compile(
            r'(\d+)\s*\n'                           # 序号
            r'(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s*-->\s*'  # 开始时间
            r'(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s*\n'      # 结束时间
            r'(.*?)(?=\n\s*\n|\n\s*\d+\s*\n|\Z)',      # 字幕内容
            re.DOTALL | re.MULTILINE
        )
    
    async def _process_impl(self, context: ProcessingContext) -> BaseResult:
        """处理字幕文件的主要逻辑"""
        try:
            # 从上下文获取字幕文件路径
            subtitle_file = context.input_files.get("subtitle")
            if not subtitle_file:
                return BaseResult(
                    success=False,
                    message="未找到字幕文件",
                    error_code="SUBTITLE_FILE_NOT_FOUND"
                )
            
            # 解析字幕文件
            self.update_progress(20, "解析字幕文件...")
            result, subtitles = self.parse_subtitle_file(subtitle_file.path)
            
            if not result.success:
                return result
            
            # 验证字幕
            self.update_progress(50, "验证字幕格式...")
            validation_errors = subtitles.validate()
            
            if validation_errors:
                self.logger.warning(f"字幕验证发现问题: {validation_errors}")
                # 可以选择是否继续处理
            
            # 保存到上下文
            context.shared_data["original_subtitles"] = subtitles
            
            self.update_progress(100, "字幕处理完成")
            
            return BaseResult(
                success=True,
                message=f"成功处理 {subtitles.total_count} 条字幕",
                metadata={
                    "subtitle_count": subtitles.total_count,
                    "total_duration": subtitles.total_duration,
                    "validation_errors": validation_errors
                }
            )
            
        except Exception as e:
            self.logger.error(f"字幕处理失败: {e}")
            return BaseResult(
                success=False,
                message=f"字幕处理失败: {str(e)}",
                error_code="SUBTITLE_PROCESSING_ERROR"
            )
    
    def parse_subtitle_file(self, file_path: FilePathType) -> Tuple[BaseResult, Optional[SubtitleCollection]]:
        """
        解析字幕文件
        
        Args:
            file_path: 字幕文件路径
            
        Returns:
            tuple: (处理结果, 字幕集合)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return BaseResult(
                    success=False,
                    message=f"字幕文件不存在: {file_path}",
                    error_code="FILE_NOT_FOUND"
                ), None
            
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return BaseResult(
                    success=False,
                    message=f"不支持的字幕格式: {file_ext}",
                    error_code="UNSUPPORTED_FORMAT"
                ), None
            
            # 读取文件内容
            content = self._read_file_with_encoding(file_path)
            if content is None:
                return BaseResult(
                    success=False,
                    message="无法读取字幕文件，可能是编码问题",
                    error_code="FILE_ENCODING_ERROR"
                ), None
            
            # 根据格式解析
            if file_ext == ".srt":
                return self._parse_srt_content(content, file_path)
            elif file_ext == ".vtt":
                return self._parse_vtt_content(content, file_path)
            else:
                return BaseResult(
                    success=False,
                    message=f"暂不支持 {file_ext} 格式",
                    error_code="FORMAT_NOT_IMPLEMENTED"
                ), None
                
        except Exception as e:
            self.logger.error(f"解析字幕文件失败: {e}")
            return BaseResult(
                success=False,
                message=f"解析字幕文件失败: {str(e)}",
                error_code="PARSE_ERROR"
            ), None
    
    def _read_file_with_encoding(self, file_path: str) -> Optional[str]:
        """尝试不同编码读取文件（优先UTF-8）"""
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'big5', 'latin1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                self.logger.info(f"成功使用 {encoding} 编码读取文件")
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                self.logger.error(f"读取文件失败 ({encoding}): {e}")
                continue
        
        self.logger.error("尝试所有编码都失败")
        return None
    
    def _parse_srt_content(self, content: str, file_path: str) -> Tuple[BaseResult, Optional[SubtitleCollection]]:
        """解析SRT格式内容"""
        try:
            # 标准化换行符
            content = content.replace('\r\n', '\n').replace('\r', '\n')
            
            # 使用正则表达式匹配
            matches = self.srt_pattern.findall(content)
            
            if not matches:
                return BaseResult(
                    success=False,
                    message="未找到有效的SRT字幕条目",
                    error_code="NO_SUBTITLE_ENTRIES"
                ), None
            
            subtitle_items = []
            
            for match in matches:
                try:
                    index = int(match[0])
                    start_time_str = match[1].replace('.', ',')  # 标准化时间格式
                    end_time_str = match[2].replace('.', ',')
                    text = match[3].strip()
                    
                    # 解析时间码
                    start_time = TimeCode.from_string(start_time_str)
                    end_time = TimeCode.from_string(end_time_str)
                    
                    # 创建字幕条目
                    subtitle_item = SubtitleItem(
                        index=len(subtitle_items),  # 重新编号，确保连续
                        start_time=start_time,
                        end_time=end_time,
                        text=text,
                        status=SubtitleStatus.ORIGINAL
                    )
                    
                    subtitle_items.append(subtitle_item)
                    
                except Exception as e:
                    self.logger.warning(f"跳过无效的字幕条目 {match[0]}: {e}")
                    continue
            
            if not subtitle_items:
                return BaseResult(
                    success=False,
                    message="没有成功解析的字幕条目",
                    error_code="NO_VALID_ENTRIES"
                ), None
            
            # 创建字幕集合
            subtitle_collection = SubtitleCollection(
                items=subtitle_items,
                format=SubtitleFormat.SRT,
                source_file=file_path,
                encoding="utf-8"
            )
            
            self.logger.info(f"成功解析 {len(subtitle_items)} 条SRT字幕")

            # 创建原字幕内容字典 {索引: 文本内容}
            original_subtitles_dict = {}
            for i, item in enumerate(subtitle_items):
                original_subtitles_dict[i] = item.text

            return BaseResult(
                success=True,
                message=f"成功解析 {len(subtitle_items)} 条字幕",
                metadata={
                    'original_subtitles_dict': original_subtitles_dict,
                    'subtitle_count': len(subtitle_items)
                }
            ), subtitle_collection
            
        except Exception as e:
            self.logger.error(f"SRT内容解析失败: {e}")
            return BaseResult(
                success=False,
                message=f"SRT内容解析失败: {str(e)}",
                error_code="SRT_PARSE_ERROR"
            ), None
    
    def _parse_vtt_content(self, content: str, file_path: str) -> Tuple[BaseResult, Optional[SubtitleCollection]]:
        """解析VTT格式内容（基础实现）"""
        # VTT格式解析的简单实现
        # 实际项目中可以使用专门的VTT解析库
        return BaseResult(
            success=False,
            message="VTT格式解析暂未实现",
            error_code="VTT_NOT_IMPLEMENTED"
        ), None
    
    def export_subtitle_file(self, subtitles: SubtitleCollection, file_path: FilePathType) -> BaseResult:
        """
        导出字幕文件
        
        Args:
            subtitles: 字幕集合
            file_path: 输出文件路径
            
        Returns:
            BaseResult: 导出结果
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(file_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 根据文件扩展名确定格式
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == ".srt":
                return self._export_srt(subtitles, file_path)
            elif file_ext == ".vtt":
                return self._export_vtt(subtitles, file_path)
            else:
                return BaseResult(
                    success=False,
                    message=f"不支持导出格式: {file_ext}",
                    error_code="UNSUPPORTED_EXPORT_FORMAT"
                )
                
        except Exception as e:
            self.logger.error(f"导出字幕文件失败: {e}")
            return BaseResult(
                success=False,
                message=f"导出字幕文件失败: {str(e)}",
                error_code="EXPORT_ERROR"
            )
    
    def _export_srt(self, subtitles: SubtitleCollection, file_path: str) -> BaseResult:
        """导出SRT格式"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for i, item in enumerate(subtitles.items, 1):
                    f.write(f"{i}\n")
                    f.write(f"{item.start_time} --> {item.end_time}\n")
                    f.write(f"{item.text}\n\n")
            
            self.logger.info(f"成功导出SRT文件: {file_path}")
            return BaseResult(
                success=True,
                message=f"成功导出 {len(subtitles.items)} 条字幕到 {file_path}"
            )
            
        except Exception as e:
            self.logger.error(f"SRT导出失败: {e}")
            return BaseResult(
                success=False,
                message=f"SRT导出失败: {str(e)}",
                error_code="SRT_EXPORT_ERROR"
            )
    
    def _export_vtt(self, subtitles: SubtitleCollection, file_path: str) -> BaseResult:
        """导出VTT格式（基础实现）"""
        return BaseResult(
            success=False,
            message="VTT格式导出暂未实现",
            error_code="VTT_EXPORT_NOT_IMPLEMENTED"
        )
    
    def generate_timeline(self, audio_file: FilePathType, subtitles: SubtitleCollection) -> Tuple[BaseResult, Optional[SubtitleCollection]]:
        """
        根据音频文件为字幕生成时间轴
        
        Args:
            audio_file: 音频文件路径
            subtitles: 字幕集合
            
        Returns:
            tuple: (处理结果, 带时间轴的字幕集合)
        """
        try:
            # 获取音频时长
            audio_duration = self._get_audio_duration(audio_file)
            if audio_duration <= 0:
                return BaseResult(
                    success=False,
                    message="无法获取音频时长",
                    error_code="AUDIO_DURATION_ERROR"
                ), None
            
            # 计算每条字幕的时长
            total_chars = sum(len(item.text.replace(' ', '')) for item in subtitles.items)
            if total_chars == 0:
                return BaseResult(
                    success=False,
                    message="字幕内容为空",
                    error_code="EMPTY_SUBTITLES"
                ), None
            
            # 按字符数分配时间
            current_time = 0.0
            timed_items = []
            
            for item in subtitles.items:
                char_count = len(item.text.replace(' ', ''))
                duration = (char_count / total_chars) * audio_duration
                
                start_time = TimeCode.from_seconds(current_time)
                end_time = TimeCode.from_seconds(current_time + duration)
                
                timed_item = SubtitleItem(
                    index=item.index,
                    start_time=start_time,
                    end_time=end_time,
                    text=item.text,
                    status=item.status,
                    confidence=item.confidence,
                    tags=item.tags.copy(),
                    metadata=item.metadata.copy()
                )
                
                timed_items.append(timed_item)
                current_time += duration
            
            # 创建新的字幕集合
            timed_collection = SubtitleCollection(
                items=timed_items,
                format=subtitles.format,
                language=subtitles.language,
                encoding=subtitles.encoding,
                source_file=subtitles.source_file,
                metadata=subtitles.metadata.copy()
            )
            
            self.logger.info(f"成功为 {len(timed_items)} 条字幕生成时间轴")
            
            return BaseResult(
                success=True,
                message=f"成功生成时间轴，总时长: {audio_duration:.2f}秒"
            ), timed_collection
            
        except Exception as e:
            self.logger.error(f"生成时间轴失败: {e}")
            return BaseResult(
                success=False,
                message=f"生成时间轴失败: {str(e)}",
                error_code="TIMELINE_GENERATION_ERROR"
            ), None
    
    def _get_audio_duration(self, audio_file: str) -> float:
        """获取音频文件时长"""
        try:
            # 这里可以使用pydub或ffmpeg来获取音频时长
            # 暂时返回一个模拟值
            import wave
            
            if audio_file.endswith('.wav'):
                with wave.open(audio_file, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    duration = frames / float(sample_rate)
                    return duration
            else:
                # 对于其他格式，可以使用ffmpeg-python
                # 这里先返回一个默认值
                return 60.0  # 默认60秒
                
        except Exception as e:
            self.logger.error(f"获取音频时长失败: {e}")
            return 0.0
    
    def merge_subtitles(self, subtitles: SubtitleCollection, merge_range: int = 10) -> SubtitleCollection:
        """
        合并相邻的短字幕
        
        Args:
            subtitles: 原字幕集合
            merge_range: 合并范围（最大合并条数）
            
        Returns:
            SubtitleCollection: 合并后的字幕集合
        """
        if not subtitles.items or merge_range <= 1:
            return subtitles
        
        merged_items = []
        current_group = []
        
        for item in subtitles.items:
            current_group.append(item)
            
            # 检查是否需要合并
            if (len(current_group) >= merge_range or 
                self._should_break_group(current_group)):
                
                # 合并当前组
                merged_item = self._merge_subtitle_group(current_group)
                merged_items.append(merged_item)
                current_group = []
        
        # 处理最后一组
        if current_group:
            merged_item = self._merge_subtitle_group(current_group)
            merged_items.append(merged_item)
        
        # 重新编号
        for i, item in enumerate(merged_items):
            item.index = i
        
        return SubtitleCollection(
            items=merged_items,
            format=subtitles.format,
            language=subtitles.language,
            encoding=subtitles.encoding,
            source_file=subtitles.source_file,
            metadata=subtitles.metadata.copy()
        )
    
    def _should_break_group(self, group: List[SubtitleItem]) -> bool:
        """判断是否应该断开字幕组"""
        if len(group) <= 1:
            return False
        
        # 检查时间间隔
        last_item = group[-1]
        prev_item = group[-2]
        
        time_gap = last_item.start_time.to_seconds() - prev_item.end_time.to_seconds()
        
        # 如果时间间隔超过2秒，断开
        return time_gap > 2.0
    
    def _merge_subtitle_group(self, group: List[SubtitleItem]) -> SubtitleItem:
        """合并一组字幕"""
        if len(group) == 1:
            return group[0]
        
        # 合并文本
        merged_text = " ".join(item.text for item in group)
        
        # 使用第一个和最后一个的时间
        start_time = group[0].start_time
        end_time = group[-1].end_time
        
        # 合并标签
        all_tags = set()
        for item in group:
            all_tags.update(item.tags)
        
        return SubtitleItem(
            index=group[0].index,
            start_time=start_time,
            end_time=end_time,
            text=merged_text,
            status=SubtitleStatus.ORIGINAL,
            tags=list(all_tags),
            metadata={"merged_from": [item.index for item in group]}
        )

    def format_subtitles_to_string(self, subtitles: SubtitleCollection, format_type: str = "srt") -> str:
        """
        将字幕集合格式化为字符串

        Args:
            subtitles: 字幕集合
            format_type: 格式类型 ("srt" 或 "vtt")

        Returns:
            str: 格式化后的字幕字符串
        """
        if format_type.lower() == "srt":
            return self._format_srt_string(subtitles)
        elif format_type.lower() == "vtt":
            return self._format_vtt_string(subtitles)
        else:
            raise ValueError(f"不支持的格式类型: {format_type}")

    def _format_srt_string(self, subtitles: SubtitleCollection) -> str:
        """将字幕集合格式化为SRT格式字符串"""
        lines = []

        # 检查是否包含时间码
        has_timecode = subtitles.metadata.get("has_timecode", True)

        for i, item in enumerate(subtitles.items, 1):
            # 序号
            lines.append(str(i))

            # 时间轴（如果有的话）
            if has_timecode and item.start_time is not None and item.end_time is not None:
                start_time = self._format_time_for_srt(item.start_time)
                end_time = self._format_time_for_srt(item.end_time)
                lines.append(f"{start_time} --> {end_time}")
            else:
                # 无时间码时显示占位符
                lines.append("[时间码待生成] --> [时间码待生成]")

            # 字幕内容
            lines.append(item.text)

            # 空行分隔
            lines.append("")

        return "\n".join(lines)

    def _format_vtt_string(self, subtitles: SubtitleCollection) -> str:
        """将字幕集合格式化为VTT格式字符串"""
        lines = ["WEBVTT", ""]

        # 检查是否包含时间码
        has_timecode = subtitles.metadata.get("has_timecode", True)

        for item in subtitles.items:
            # 时间轴（如果有的话）
            if has_timecode and item.start_time is not None and item.end_time is not None:
                start_time = self._format_time_for_vtt(item.start_time)
                end_time = self._format_time_for_vtt(item.end_time)
                lines.append(f"{start_time} --> {end_time}")
            else:
                # 无时间码时显示占位符
                lines.append("[时间码待生成] --> [时间码待生成]")

            # 字幕内容
            lines.append(item.text)

            # 空行分隔
            lines.append("")

        return "\n".join(lines)

    def _format_time_for_srt(self, time_code) -> str:
        """将时间码格式化为SRT格式"""
        if isinstance(time_code, str):
            # 如果已经是字符串格式，直接返回
            return time_code.replace('.', ',')  # SRT使用逗号分隔毫秒

        # 如果是TimeCode对象，使用其属性
        if hasattr(time_code, 'hours'):
            return f"{time_code.hours:02d}:{time_code.minutes:02d}:{time_code.seconds:02d},{time_code.milliseconds:03d}"

        # 如果是其他类型，尝试转换为秒数再处理
        total_seconds = time_code.to_seconds() if hasattr(time_code, 'to_seconds') else float(time_code)
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

    def _format_time_for_vtt(self, time_code) -> str:
        """将时间码格式化为VTT格式"""
        if isinstance(time_code, str):
            # 如果已经是字符串格式，确保使用点号分隔毫秒
            return time_code.replace(',', '.')  # VTT使用点号分隔毫秒

        # 如果是TimeCode对象，使用其属性
        if hasattr(time_code, 'hours'):
            return f"{time_code.hours:02d}:{time_code.minutes:02d}:{time_code.seconds:02d}.{time_code.milliseconds:03d}"

        # 如果是其他类型，尝试转换为秒数再处理
        total_seconds = time_code.to_seconds() if hasattr(time_code, 'to_seconds') else float(time_code)
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"

    def export_subtitles(self, subtitles: SubtitleCollection, format_type: str = "srt") -> str:
        """
        导出字幕为字符串（兼容性方法）

        Args:
            subtitles: 字幕集合
            format_type: 格式类型

        Returns:
            str: 格式化后的字幕字符串

        Note:
            这是为了兼容现有代码而添加的方法，
            推荐使用 format_subtitles_to_string() 方法
        """
        return self.format_subtitles_to_string(subtitles, format_type)
