"""
Video Processor - 视频处理器

集成FFmpeg实现视频信息获取、音频移除、视频分割等功能
支持多种视频格式和处理操作
"""

import os
import json
import logging
import subprocess
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import tempfile

from ..common.models import BaseResult, ProcessingContext, FileInfo, FileType
from ..common.interfaces import BaseProcessor
from ..common.types import FilePathType, DurationSecondsType


class VideoInfo:
    """视频信息类"""
    
    def __init__(self, data: Dict[str, Any]):
        self.file_path = data.get("file_path", "")
        self.duration = data.get("duration", 0.0)
        self.width = data.get("width", 0)
        self.height = data.get("height", 0)
        self.fps = data.get("fps", 0.0)
        self.bitrate = data.get("bitrate", 0)
        self.codec = data.get("codec", "")
        self.audio_codec = data.get("audio_codec", "")
        self.audio_channels = data.get("audio_channels", 0)
        self.audio_sample_rate = data.get("audio_sample_rate", 0)
        self.file_size = data.get("file_size", 0)
        self.format = data.get("format", "")
    
    @property
    def resolution(self) -> str:
        """获取分辨率字符串"""
        return f"{self.width}x{self.height}"
    
    @property
    def has_audio(self) -> bool:
        """是否包含音频"""
        return self.audio_codec != "" and self.audio_channels > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "file_path": self.file_path,
            "duration": self.duration,
            "width": self.width,
            "height": self.height,
            "fps": self.fps,
            "bitrate": self.bitrate,
            "codec": self.codec,
            "audio_codec": self.audio_codec,
            "audio_channels": self.audio_channels,
            "audio_sample_rate": self.audio_sample_rate,
            "file_size": self.file_size,
            "format": self.format,
            "resolution": self.resolution,
            "has_audio": self.has_audio
        }


class VideoSegment:
    """视频片段类"""
    
    def __init__(self, start_time: float, end_time: float, output_path: str):
        self.start_time = start_time
        self.end_time = end_time
        self.output_path = output_path
        self.duration = end_time - start_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "output_path": self.output_path
        }


class VideoProcessor(BaseProcessor):
    """视频处理器实现类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("VideoProcessor", config)
        self.logger = logging.getLogger(__name__)
        
        # FFmpeg配置
        self.ffmpeg_path = config.get("ffmpeg_path", "ffmpeg") if config else "ffmpeg"
        self.ffprobe_path = config.get("ffprobe_path", "ffprobe") if config else "ffprobe"
        
        # 支持的视频格式
        self.supported_formats = [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm"]
        
        # 检查FFmpeg是否可用
        self._check_ffmpeg_availability()
    
    def _check_ffmpeg_availability(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                [self.ffmpeg_path, "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                self.logger.info("FFmpeg可用")
            else:
                self.logger.warning("FFmpeg不可用，某些功能可能无法正常工作")
        except Exception as e:
            self.logger.warning(f"FFmpeg检查失败: {e}")
    
    async def _process_impl(self, context: ProcessingContext) -> BaseResult:
        """处理视频的主要逻辑"""
        try:
            # 从上下文获取视频文件
            video_file = context.input_files.get("video")
            if not video_file:
                return BaseResult(
                    success=False,
                    message="未找到视频文件",
                    error_code="VIDEO_FILE_NOT_FOUND"
                )
            
            self.update_progress(20, "获取视频信息...")
            
            # 获取视频信息
            result, video_info = self.get_video_info(video_file.path)
            
            if not result.success:
                return result
            
            # 保存视频信息到上下文
            context.shared_data["video_info"] = video_info
            
            self.update_progress(100, "视频处理完成")
            
            return BaseResult(
                success=True,
                message=f"成功处理视频文件: {video_info.resolution}, {video_info.duration:.2f}秒",
                metadata=video_info.to_dict()
            )
            
        except Exception as e:
            self.logger.error(f"视频处理失败: {e}")
            return BaseResult(
                success=False,
                message=f"视频处理失败: {str(e)}",
                error_code="VIDEO_PROCESSING_ERROR"
            )
    
    def get_video_info(self, file_path: FilePathType) -> Tuple[BaseResult, Optional[VideoInfo]]:
        """
        获取视频信息
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            tuple: (处理结果, 视频信息)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return BaseResult(
                    success=False,
                    message=f"视频文件不存在: {file_path}",
                    error_code="FILE_NOT_FOUND"
                ), None
            
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return BaseResult(
                    success=False,
                    message=f"不支持的视频格式: {file_ext}",
                    error_code="UNSUPPORTED_FORMAT"
                ), None
            
            # 使用ffprobe获取视频信息
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                file_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                return BaseResult(
                    success=False,
                    message=f"FFprobe执行失败: {result.stderr}",
                    error_code="FFPROBE_ERROR"
                ), None
            
            # 解析JSON输出
            probe_data = json.loads(result.stdout)
            
            # 提取视频信息
            video_info = self._parse_probe_data(probe_data, file_path)
            
            self.logger.info(f"成功获取视频信息: {video_info.resolution}, {video_info.duration:.2f}秒")
            
            return BaseResult(
                success=True,
                message="成功获取视频信息"
            ), video_info
            
        except subprocess.TimeoutExpired:
            return BaseResult(
                success=False,
                message="FFprobe执行超时",
                error_code="FFPROBE_TIMEOUT"
            ), None
        except json.JSONDecodeError as e:
            return BaseResult(
                success=False,
                message=f"FFprobe输出解析失败: {str(e)}",
                error_code="PROBE_PARSE_ERROR"
            ), None
        except Exception as e:
            self.logger.error(f"获取视频信息失败: {e}")
            return BaseResult(
                success=False,
                message=f"获取视频信息失败: {str(e)}",
                error_code="VIDEO_INFO_ERROR"
            ), None
    
    def _parse_probe_data(self, probe_data: Dict[str, Any], file_path: str) -> VideoInfo:
        """解析ffprobe输出数据"""
        format_info = probe_data.get("format", {})
        streams = probe_data.get("streams", [])
        
        # 查找视频流和音频流
        video_stream = None
        audio_stream = None
        
        for stream in streams:
            if stream.get("codec_type") == "video":
                video_stream = stream
            elif stream.get("codec_type") == "audio":
                audio_stream = stream
        
        # 提取基本信息
        duration = float(format_info.get("duration", 0))
        file_size = int(format_info.get("size", 0))
        format_name = format_info.get("format_name", "")
        bitrate = int(format_info.get("bit_rate", 0))
        
        # 提取视频信息
        width = 0
        height = 0
        fps = 0.0
        video_codec = ""
        
        if video_stream:
            width = int(video_stream.get("width", 0))
            height = int(video_stream.get("height", 0))
            video_codec = video_stream.get("codec_name", "")
            
            # 计算帧率
            fps_str = video_stream.get("r_frame_rate", "0/1")
            if "/" in fps_str:
                num, den = fps_str.split("/")
                if int(den) != 0:
                    fps = float(num) / float(den)
        
        # 提取音频信息
        audio_codec = ""
        audio_channels = 0
        audio_sample_rate = 0
        
        if audio_stream:
            audio_codec = audio_stream.get("codec_name", "")
            audio_channels = int(audio_stream.get("channels", 0))
            audio_sample_rate = int(audio_stream.get("sample_rate", 0))
        
        return VideoInfo({
            "file_path": file_path,
            "duration": duration,
            "width": width,
            "height": height,
            "fps": fps,
            "bitrate": bitrate,
            "codec": video_codec,
            "audio_codec": audio_codec,
            "audio_channels": audio_channels,
            "audio_sample_rate": audio_sample_rate,
            "file_size": file_size,
            "format": format_name
        })
    
    def remove_audio(self, input_path: FilePathType, output_path: FilePathType) -> BaseResult:
        """
        移除音频轨道
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            
        Returns:
            BaseResult: 处理结果
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-c:v", "copy",  # 复制视频流，不重新编码
                "-an",           # 移除音频
                "-y",            # 覆盖输出文件
                output_path
            ]
            
            self.logger.info(f"移除音频: {input_path} -> {output_path}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode != 0:
                return BaseResult(
                    success=False,
                    message=f"FFmpeg执行失败: {result.stderr}",
                    error_code="FFMPEG_ERROR"
                )
            
            # 检查输出文件是否存在
            if not os.path.exists(output_path):
                return BaseResult(
                    success=False,
                    message="输出文件未生成",
                    error_code="OUTPUT_FILE_NOT_FOUND"
                )
            
            self.logger.info(f"成功移除音频: {output_path}")
            
            return BaseResult(
                success=True,
                message="成功移除音频轨道",
                metadata={
                    "input_path": input_path,
                    "output_path": output_path,
                    "output_size": os.path.getsize(output_path)
                }
            )
            
        except subprocess.TimeoutExpired:
            return BaseResult(
                success=False,
                message="FFmpeg执行超时",
                error_code="FFMPEG_TIMEOUT"
            )
        except Exception as e:
            self.logger.error(f"移除音频失败: {e}")
            return BaseResult(
                success=False,
                message=f"移除音频失败: {str(e)}",
                error_code="REMOVE_AUDIO_ERROR"
            )

    def split_video(self, input_path: FilePathType, segments: List[Tuple[DurationSecondsType, DurationSecondsType]], output_dir: str = None) -> Tuple[BaseResult, List[VideoSegment]]:
        """
        分割视频

        Args:
            input_path: 输入视频路径
            segments: 分割时间段列表 [(开始时间, 结束时间), ...]
            output_dir: 输出目录，如果为None则使用临时目录

        Returns:
            tuple: (处理结果, 视频片段列表)
        """
        try:
            if not segments:
                return BaseResult(
                    success=False,
                    message="分割时间段列表为空",
                    error_code="EMPTY_SEGMENTS"
                ), []

            # 准备输出目录
            if output_dir is None:
                output_dir = tempfile.mkdtemp(prefix="video_segments_")
            else:
                os.makedirs(output_dir, exist_ok=True)

            video_segments = []
            input_name = Path(input_path).stem
            input_ext = Path(input_path).suffix

            for i, (start_time, end_time) in enumerate(segments):
                if start_time >= end_time:
                    self.logger.warning(f"跳过无效时间段: {start_time} >= {end_time}")
                    continue

                # 生成输出文件名
                output_filename = f"{input_name}_segment_{i:03d}{input_ext}"
                output_path = os.path.join(output_dir, output_filename)

                # 构建FFmpeg命令
                cmd = [
                    self.ffmpeg_path,
                    "-i", input_path,
                    "-ss", str(start_time),      # 开始时间
                    "-t", str(end_time - start_time),  # 持续时间
                    "-c", "copy",                # 复制流，不重新编码
                    "-avoid_negative_ts", "make_zero",
                    "-y",                        # 覆盖输出文件
                    output_path
                ]

                self.logger.info(f"分割视频片段 {i}: {start_time:.2f}s - {end_time:.2f}s")

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300
                )

                if result.returncode != 0:
                    self.logger.error(f"分割片段 {i} 失败: {result.stderr}")
                    continue

                # 检查输出文件
                if os.path.exists(output_path):
                    segment = VideoSegment(start_time, end_time, output_path)
                    video_segments.append(segment)
                    self.logger.info(f"成功生成片段: {output_path}")
                else:
                    self.logger.error(f"片段文件未生成: {output_path}")

            if not video_segments:
                return BaseResult(
                    success=False,
                    message="没有成功生成任何视频片段",
                    error_code="NO_SEGMENTS_GENERATED"
                ), []

            self.logger.info(f"成功分割视频: {len(video_segments)} 个片段")

            return BaseResult(
                success=True,
                message=f"成功分割视频为 {len(video_segments)} 个片段",
                metadata={
                    "input_path": input_path,
                    "output_dir": output_dir,
                    "segment_count": len(video_segments)
                }
            ), video_segments

        except subprocess.TimeoutExpired:
            return BaseResult(
                success=False,
                message="视频分割超时",
                error_code="SPLIT_TIMEOUT"
            ), []
        except Exception as e:
            self.logger.error(f"视频分割失败: {e}")
            return BaseResult(
                success=False,
                message=f"视频分割失败: {str(e)}",
                error_code="SPLIT_ERROR"
            ), []

    def adjust_speed(self, input_path: FilePathType, output_path: FilePathType, speed_factor: float) -> BaseResult:
        """
        调整播放速度

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            speed_factor: 速度倍数 (0.5-2.0)

        Returns:
            BaseResult: 处理结果
        """
        try:
            # 验证速度倍数
            if speed_factor <= 0 or speed_factor > 4.0:
                return BaseResult(
                    success=False,
                    message=f"无效的速度倍数: {speed_factor}，应在 0.1-4.0 范围内",
                    error_code="INVALID_SPEED_FACTOR"
                )

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # 构建FFmpeg命令
            # 使用setpts滤镜调整视频速度，使用atempo滤镜调整音频速度
            video_filter = f"setpts={1/speed_factor}*PTS"

            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-filter_complex", f"[0:v]{video_filter}[v];[0:a]atempo={speed_factor}[a]",
                "-map", "[v]",
                "-map", "[a]",
                "-y",
                output_path
            ]

            self.logger.info(f"调整视频速度: {speed_factor}x, {input_path} -> {output_path}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )

            if result.returncode != 0:
                # 如果包含音频的处理失败，尝试只处理视频
                self.logger.warning("包含音频的速度调整失败，尝试只处理视频")

                cmd_video_only = [
                    self.ffmpeg_path,
                    "-i", input_path,
                    "-filter:v", video_filter,
                    "-an",  # 移除音频
                    "-y",
                    output_path
                ]

                result = subprocess.run(
                    cmd_video_only,
                    capture_output=True,
                    text=True,
                    timeout=600
                )

                if result.returncode != 0:
                    return BaseResult(
                        success=False,
                        message=f"FFmpeg执行失败: {result.stderr}",
                        error_code="FFMPEG_ERROR"
                    )

            # 检查输出文件
            if not os.path.exists(output_path):
                return BaseResult(
                    success=False,
                    message="输出文件未生成",
                    error_code="OUTPUT_FILE_NOT_FOUND"
                )

            self.logger.info(f"成功调整视频速度: {output_path}")

            return BaseResult(
                success=True,
                message=f"成功调整视频速度为 {speed_factor}x",
                metadata={
                    "input_path": input_path,
                    "output_path": output_path,
                    "speed_factor": speed_factor,
                    "output_size": os.path.getsize(output_path)
                }
            )

        except subprocess.TimeoutExpired:
            return BaseResult(
                success=False,
                message="视频速度调整超时",
                error_code="SPEED_ADJUST_TIMEOUT"
            )
        except Exception as e:
            self.logger.error(f"调整视频速度失败: {e}")
            return BaseResult(
                success=False,
                message=f"调整视频速度失败: {str(e)}",
                error_code="SPEED_ADJUST_ERROR"
            )

    def extract_audio(self, input_path: FilePathType, output_path: FilePathType, format: str = "wav") -> BaseResult:
        """
        提取音频

        Args:
            input_path: 输入视频路径
            output_path: 输出音频路径
            format: 音频格式 (wav, mp3, aac)

        Returns:
            BaseResult: 处理结果
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-vn",  # 不包含视频
                "-acodec", "pcm_s16le" if format == "wav" else "libmp3lame" if format == "mp3" else "aac",
                "-ar", "44100",  # 采样率
                "-ac", "2",      # 声道数
                "-y",
                output_path
            ]

            self.logger.info(f"提取音频: {input_path} -> {output_path}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )

            if result.returncode != 0:
                return BaseResult(
                    success=False,
                    message=f"FFmpeg执行失败: {result.stderr}",
                    error_code="FFMPEG_ERROR"
                )

            # 检查输出文件
            if not os.path.exists(output_path):
                return BaseResult(
                    success=False,
                    message="音频文件未生成",
                    error_code="AUDIO_FILE_NOT_FOUND"
                )

            self.logger.info(f"成功提取音频: {output_path}")

            return BaseResult(
                success=True,
                message="成功提取音频",
                metadata={
                    "input_path": input_path,
                    "output_path": output_path,
                    "format": format,
                    "output_size": os.path.getsize(output_path)
                }
            )

        except subprocess.TimeoutExpired:
            return BaseResult(
                success=False,
                message="音频提取超时",
                error_code="EXTRACT_AUDIO_TIMEOUT"
            )
        except Exception as e:
            self.logger.error(f"提取音频失败: {e}")
            return BaseResult(
                success=False,
                message=f"提取音频失败: {str(e)}",
                error_code="EXTRACT_AUDIO_ERROR"
            )

    def merge_audio_video(self, video_path: FilePathType, audio_path: FilePathType, output_path: FilePathType) -> BaseResult:
        """
        合并音频和视频

        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径

        Returns:
            BaseResult: 处理结果
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path,
                "-i", video_path,
                "-i", audio_path,
                "-c:v", "copy",  # 复制视频流
                "-c:a", "aac",   # 音频编码为AAC
                "-map", "0:v:0", # 使用第一个输入的视频流
                "-map", "1:a:0", # 使用第二个输入的音频流
                "-shortest",     # 以较短的流为准
                "-y",
                output_path
            ]

            self.logger.info(f"合并音视频: {video_path} + {audio_path} -> {output_path}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600
            )

            if result.returncode != 0:
                return BaseResult(
                    success=False,
                    message=f"FFmpeg执行失败: {result.stderr}",
                    error_code="FFMPEG_ERROR"
                )

            # 检查输出文件
            if not os.path.exists(output_path):
                return BaseResult(
                    success=False,
                    message="合并文件未生成",
                    error_code="MERGED_FILE_NOT_FOUND"
                )

            self.logger.info(f"成功合并音视频: {output_path}")

            return BaseResult(
                success=True,
                message="成功合并音频和视频",
                metadata={
                    "video_path": video_path,
                    "audio_path": audio_path,
                    "output_path": output_path,
                    "output_size": os.path.getsize(output_path)
                }
            )

        except subprocess.TimeoutExpired:
            return BaseResult(
                success=False,
                message="音视频合并超时",
                error_code="MERGE_TIMEOUT"
            )
        except Exception as e:
            self.logger.error(f"合并音视频失败: {e}")
            return BaseResult(
                success=False,
                message=f"合并音视频失败: {str(e)}",
                error_code="MERGE_ERROR"
            )
