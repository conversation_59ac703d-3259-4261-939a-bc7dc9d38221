# AI Video Editor 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# 应用配置
APP_NAME=AI Video Editor
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 文件路径配置
TEMP_DIR=temp
OUTPUT_DIR=output
CONFIG_DIR=config
LOG_DIR=logs

# DeepSeek API配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-reasoner

# 阿里云TTS配置
# 重要：这些是阿里云访问凭证，请妥善保管
ALIYUN_AK_ID=LTAI5t9bLXabWagcyPSJGDdN
ALIYUN_AK_SECRET=******************************
ALIYUN_TTS_APPKEY=avn9JKysKyKbaL55

# 处理配置
MAX_FILE_SIZE=524288000  # 500MB in bytes
MAX_WORKERS=4
PROCESSING_TIMEOUT=3600  # 1 hour in seconds

# UI配置
WINDOW_WIDTH=1400
WINDOW_HEIGHT=900
THEME=light
LANGUAGE=zh_CN

# FFmpeg配置
FFMPEG_PATH=ffmpeg
FFPROBE_PATH=ffprobe

# 安全提示：
# 1. 请勿将包含真实API密钥的.env文件提交到版本控制系统
# 2. 在生产环境中，建议使用更安全的密钥管理方案
# 3. 定期轮换API密钥以提高安全性
